x-service-templates:
  runchise-base: &runchise-base
    image: "748426657045.dkr.ecr.ap-southeast-1.amazonaws.com/runchise:<IMAGE_TAG>"
    env_file:
      - .env

services:
  inventory_consumer_v2_1:
    <<: *runchise-base
    command: bundle exec racecar InventoryConsumerV2
  rails-worker_1:
    <<: *runchise-base
    ports:
      - "80:7433"
    command: bundle exec sidekiq
    deploy:
      resources:
        reservations:
          cpus: "1.2"
  rails-worker_3:
    <<: *runchise-base
    environment:
      - DISABLE_SIDEKIQ_ALIVE=true
    command: bundle exec sidekiq -C config/sidekiq_notification.yml
    deploy:
      resources:
        limits:
          cpus: "0.5"
