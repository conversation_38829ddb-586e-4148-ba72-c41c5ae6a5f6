.PHONY: generate_swagger drop_db_test create_db_test migrate_db_test reset_db_test

generate_swagger:
	bundle exec rake rswag:specs:swaggerize PATTERN="spec/**/*_spec.rb"

drop_db_test:
	bundle exec rake db:drop RAILS_ENV=test

create_db_test:
	bundle exec rake db:create RAILS_ENV=test

migrate_db_test:
	bundle exec rake db:migrate RAILS_ENV=test

reset_db_test: drop_db_test create_db_test migrate_db_test
