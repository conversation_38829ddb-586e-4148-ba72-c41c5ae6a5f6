# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

/console/*

**/.DS_Store

# Ignore bundler config.
/.bundle
/.aws

# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-journal

# Ignore all logfiles and tempfiles.
/log
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep
vendor/
coverage/

.byebug_history

/public/assets/*

# Rails 6 doesnt use sprockets but use webpack
/node_modules

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml

# Ignore master key for decrypting credentials and more.
/config/master.key

config/settings.local.yml
config/settings/*.local.yml
config/environments/*.local.yml

# Ignore dockerrun file and zip
/docker/Dockerrun.aws.json
/docker/runchise.zip

# Ignore Code Editor setting
.vscode/*
.idea/*
.solargraph.yml

# ignore environments
.env
dump.rdb
/config/credentials/staging.key
/config/credentials/production.key


/metadata/*
/preprocessed_configs/
/data/*
/store/*

status
uuid

