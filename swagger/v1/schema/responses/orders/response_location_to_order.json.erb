{"response_location_to_order": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "initial": {"type": "string", "nullable": true, "description": "Location initial used in transaction number generation (e.g., 'fir' for '#fir-00331'). Null for vendors."}, "branch_type": {"anyOf": [{"type": "string", "enum": ["outlet", "central_kitchen", "warehouse"]}, {"type": "null"}], "description": "Null for vendors."}, "is_franchise": {"type": "boolean", "nullable": true, "description": "Null for vendors."}, "brand_id": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["Location", "<PERSON><PERSON><PERSON>"]}}}}, "paging": {"$ref": "#/components/schemas/model_paging"}}}}