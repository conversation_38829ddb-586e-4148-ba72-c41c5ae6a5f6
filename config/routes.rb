require 'sidekiq/web'
require 'sidekiq-scheduler/web'

# To preview mailers http://localhost:3000/rails/mailers/
Rails.application.routes.draw do
  use_doorkeeper
  mount ActionCable.server => '/cable'

  admin_config = ActiveAdmin::Devise.config
  admin_config[:controllers][:omniauth_callbacks] = 'admin/omniauth_callbacks'
  devise_for :admin_users, admin_config

  begin
    ActiveAdmin.routes(self)
  rescue StandardError
    ActiveAdmin::DatabaseHitDuringLoad
  end

  root to: 'admin/dashboard#index'

  draw(:delivery)
  draw(:gobiz)
  draw(:dine_in)
  draw(:external_order)
  draw(:grab_food)
  draw(:zendesk)
  draw(:shopee_food)
  draw(:messaging)
  draw(:qris_payment)
  draw(:enterprise)
  draw(:payment)
  draw(:price_table)
  draw(:public)
  draw(:dropbox)
  draw(:neobank)
  draw(:shopee_pay)
  draw(:winpay)
  draw(:preorder)
  draw(:dominos)
  draw(:pos)
  draw(:ssk)
  draw(:bca)
  draw(:cloudbed)
  draw(:internal_chatbot)
  draw(:import)

  devise_for :users, controllers: { invitations: 'users/invitations' },
                     skip: %i[registrations invitations]

  devise_scope :user do
    # invitation
    get '/users/invitation/accept', as: 'accept_user_invitation', to: redirect { |_p, request|
                                                                        "#{Rails.application.credentials.dig(:runchise, :backoffice_web_url)}/accept_invitation/?#{request.query_string}"
                                                                      }

    # activation
    get '/users/activation', as: 'users_activation', to: redirect { |_p, request| "#{Rails.application.credentials.dig(:runchise, :backoffice_web_url)}/activation/?#{request.query_string}" }
  end

  authenticate :admin_user do
    mount Flipper::UI.app(Flipper) => '/flipper'
    mount Sidekiq::Web => '/sidekiq'
    mount Rswag::Api::Engine => '/api-docs'
    mount Rswag::Ui::Engine => '/swagger-ui'
  end

  get 'ping/health_check' => 'health_check#index'

  get 'print_test_file' => 'health_check#print_test_file' if Rails.env.development?

  namespace :sidekiq, path: 'sidekiq_control' do
    post :quiet_processes
    post :clear_zombie_queues
  end

  namespace :api, defaults: { format: :json } do
    get "/whatsapp/webhooks", to: "whatsapp_webhooks#verify"   # Verification endpoint
    post "/whatsapp/webhooks", to: "whatsapp_webhooks#receive"  # Receiving messages

    resources :whitelist_ips, only: %i[index create update destroy]

    resources :pos_logs, only: [] do
      collection do
        post :upload_url
        post :send_email
        post :upload_daily_pos_log_url
      end
    end

    resources :ssk_logs, only: [] do
      collection do
        post :upload_url
        post :send_email
      end
    end

    resources :kds_logs, only: [] do
      collection do
        post :upload_url
        post :send_email
      end
    end

    resources :kds_completed_orders, only: [] do
      collection do
        post :upload_url
        post :send_email
      end
    end

    resources :stock_adjustment_templates, controller: '/restaurant/controllers/stock_adjustment_templates'
    resources :report_export_progresses, only: %i[index show], controller: '/restaurant/controllers/report_export_progresses' do
      collection do
        get :report_types
        delete :bulk_destroy
      end
    end

    resources :mobile_logs, only: [] do
      collection do
        post :upload_url
        post :brand_send_email
      end
    end

    resources :monthly_target_product_sales, only: %i[create] do
      collection do
        get :detail
        get :dashboard
      end
    end

    post :auth, to: 'authentication#create'
    delete :auth, to: 'authentication#destroy'
    get :auth_otp, to: 'authentication#request_otp'
    post :verify_phone_number, to: 'authentication#verify_phone_number'
    post :acknowledge_refresh_token, to: 'authentication#acknowledge_refresh_token'
    post :check_user_permission, to: 'authentication#check_user_permission'
    post 'auth/edot-sso', to: '/edot/controllers/authentication#create'

    namespace :delivery do
      resources :profiles, controller: '/delivery/controllers/profiles' do
        collection do
          post :upload_url
        end
      end
    end

    get :ping, to: 'health_check#index'

    get 'bulk_update_internal_price_logs/:id', to: '/restaurant/controllers/bulk_update_internal_price_logs#show'

    namespace :location_groups do
      get :list_with_locations
    end

    resources :product_category_groups, controller: '/restaurant/controllers/product_category_groups' do
      collection do
        get :list_product_categories
      end
    end

    namespace :users do
      resources :confirmations, only: %i[create]
      get :confirmations, to: 'confirmations#show'

      get :invitations, to: 'invitations#show'
      patch :invitations, to: 'invitations#update'

      resources :passwords, only: %i[create]
      patch :passwords, to: 'passwords#update'

      get :registrations, to: 'registrations#show'
      patch :registrations, to: 'registrations#update'
    end

    resources :billings, only: %i[index update] do
      collection do
        get :info
      end
    end

    get :timezones, to: 'timezones#timezones'
    get :application_versions, to: 'application_versions#show'
    get :countries, to: 'timezones#countries'
    get :phone_countries, to: 'timezones#phone_countries'
    get :country_by_coordinates, to: 'timezones#country_by_coordinates'

    resource :online_delivery_setting, only: %i[show update] do
      member do
        post :upload_url
      end
    end

    resource :notification_setting, only: %i[show update]

    resource :procurement_settings, only: %i[show update]

    resource :qr_order_settings, only: %i[show update]

    resources :all_you_can_eat_settings, only: %i[index show update create destroy] do
      member do
        post :duplicate
      end
    end

    resources :access_pin_locations, only: %i[create] do
      collection do
        post :validate
      end
    end

    resources :scheduled_menus, only: %i[index show update create destroy] do
      member do
        post :duplicate
        patch :deactivate
        patch :reactivate
      end
    end

    resources :devices do
      member do
        get :history
        get :check
        post :request_sync
        patch :acknowledge_sync
        post :generate_ssk_code
        post :validate_ssk_code
      end
    end

    resources :kds_devices, only: %i[show create update destroy] do
      member do
        patch :ping
        get :check
      end
    end

    resources :taxes, only: %i[index create destroy update] do
      member do
        get :history
        patch :archive
        patch :unarchive
      end
    end

    resources :procurements, only: [] do
      collection do
        get :location_filter
        post :ingredient
      end
    end

    resources :promos, only: %i[index create show update destroy] do
      member do
        patch :deactivate
        patch :activate
        get :history
        get :integration_locations
        post :retry_sync
        post :duplicate
      end

      collection do
        post :reserved
        get :checkpoint_usage
        get :dashboard
        get :dashboard_dropdown
        patch :deactivate, to: 'promos#deactivate_bulk'
        post :promotion_code_upload_url
      end

      resources :promo_codes, only: %i[index create] do
        member do
          patch :deactivate
        end

        collection do
          get :download_codes
        end
      end
    end

    resources :promo_codes, only: %i[] do
      collection do
        get :template
        get :validate_code
        post :validate_uploaded_file
        post :upload_url
      end
    end

    resources :procurement_promos, only: %i[index create show update] do
      member do
        patch :deactivate
        get :customers_applicable_to
        get :locations_applicable_to
        get :locations_available_at
      end
      collection do
        patch :deactivate, to: 'procurement_promos#deactivate_bulk'
        post :available_promos_for_procurement
        post :apply_promos
        get :check_all_by_location_type
      end
    end

    resources :order_types, only: %i[index create destroy update] do
      member do
        get :history
        patch :reactivate
        patch :deactivate
      end
    end

    resources :royalty_schemas, only: %i[index create update show destroy] do
      member do
        get :history
      end
    end

    resources :royalty_transactions, only: %i[index create update show destroy] do
      member do
        patch :notes
      end
    end

    resources :royalty_transaction_creation_requests, only: %i[show]

    resources :sales_returns, only: %i[show] do
      member do
        patch :void
        get :history
      end

      collection do
        post :state_acknowledgement
      end
    end

    resources :sale_transactions, only: %i[index show] do
      member do
        patch :change_payment_method
        patch :number_of_guests
        patch :void
        get :allow_void
        get :history
        get :detail_sale_order
      end

      collection do
        post :sales_receipt
        get :store_performance
        get :average_sale
        get :total_sales
        get :monthly_sales
        get :total_void
        get :total_refund
        get :top_selling_menu
        get :performance
        get :payment_type
        get :order_type
        get :weekly_sales
        get :hourly_sales
        get :cashiers_list
        get :runchise_payments_history
        post :state_acknowledgement, to: 'sale_transactions#state_acknowledgement'
        get :daily_revenue
        get :total_item_production
      end

      resources :sale_transaction_pay_later_payments, only: %i[] do
        collection do
          get :detail
          patch :paid
          post :upload_url
        end
      end
    end

    resources :daily_sales, only: %i[index show destroy] do
      member do
        get :history
      end
    end

    resources :brand_otp_credit_transactions, only: %i[index] do
      collection do
        get :total
      end
    end

    resources :stock_adjustments, only: %i[index show create destroy update] do
      member do
        get :history
      end
      collection do
        get :show_warning
        post :upload_url
      end
    end

    resources :stock_in_or_outs, except: %i[new edit destroy], controller: '/restaurant/controllers/stock_in_or_outs' do
      member do
        patch :void
        get :history
      end
    end

    resources :stock_transfers, except: %i[index new edit destroy], controller: '/restaurant/controllers/stock_transfers' do
      member do
        patch :void
        get :history
      end
    end

    resources :business, only: %i[index update] do
      member do
        patch :deposit_setting
        get :users
      end

      collection do
        post :upload_url
      end
    end

    resources :cancel_reasons, only: %i[index]

    resources :wastes, only: %i[index show create update] do
      collection do
        get :weekly_summary
        get :top_waste_reason
        get :top_waste_product
        get :waste_reason
        post :upload_url
        get :reason_list
      end

      member do
        patch :void
        get :history
      end
    end

    namespace :payment_methods do
      post :duplicate
      get :grouped_by
    end

    resources :payment_methods, except: %i[new edit] do
      member do
        get :history
        patch :archive
        patch :unarchive
      end

      resources :payment_method_custom_fees, only: %i[index create update destroy] do
        member do
          get :history
        end
      end
    end

    resources :voucher_payment_methods, except: %i[new edit destroy] do
      member do
        patch :active
        patch :inactive
      end
    end

    resources :courses, except: %i[new edit] do
      member do
        patch :activate
        patch :deactivate
      end
    end

    resources :online_delivery, only: [] do
      collection do
        get :open_close_stores
        get 'today_disbursements/:location_id', to: 'online_delivery#today_disbursements'
        get 'revenue/:location_id', to: 'online_delivery#revenue'
        get 'top_selling_menu/:location_id', to: 'online_delivery#top_selling_menu'
        get 'total_disbursements_amount/:location_id', to: 'online_delivery#total_disbursements_amount'
      end
    end

    namespace :report do
      namespace :informations, controllers: :informations do
        get :last_updated
      end
      resources :orders, only: [:index]
      resources :order_fulfillments, only: [:index]
      resources :deliveries, only: [:index]
      resources :stock_wastes, only: [:index]
      resources :stock_wastes_per_section, only: [:index]
      resources :product_stocks, only: [:index]
      resources :product_stocks_per_section, only: [:index]
      resources :usage_variances, only: [:index]
      resources :multi_brand_usage_variances, only: [:index]
      resources :pars, only: [:index]
      resources :location_disbursements, only: [:index]
      resources :vendor_price_history, only: [:index]
      namespace :royalties, controllers: :royalties do
        get :monthly
      end
      resources :procurement_profits, only: [:index]
      resources :stock_adjustments, only: [:index]

      resources :production_costs, only: %i[index]

      resources :production_summaries, only: [:index]

      # sales report
      resources :net_sales, only: [:index]
      resources :sales_summaries, only: [:index]
      resources :report_taxes, only: [:index]
      resources :sales_payment_methods, only: [:index]
      resources :sales_promotions, only: [:index]
      resources :sales_types, only: [:index]
      resources :sales_by, only: [:index]
      resources :sales_by_summary, only: [:index]
      resources :money_movements, only: [:index]
      resources :money_movement_balances, only: [:index]
      resources :sales_feeds, only: [:index]
      resources :gross_profit_orders, only: [:index]

      resources :demand_predictions, only: [:index]
      resources :account_transactions, only: %i[index]
      resources :profit_loss, only: %i[index]
      resources :hourly_sales, only: %i[index]
      resources :hourly_sales_by_product, only: %i[index]
      resources :sale_transactions_report, only: %i[index]

      # inventory
      resources :inventory_movements, only: [:index]
      resources :stock_movements_summary, only: [:index]
      resources :inventory_movements_per_section, only: [:index]
      resources :stock_movements_summary_per_section, only: [:index]

      resources :sales_by_customer, only: %i[index]
      resources :pay_later_payment, only: %i[index]

      # customer_deposit
      resources :customer_deposit_movement, only: %i[index] # will deprecate this API
      resources :customer_database, only: %i[index]
      resources :pos_activity_logs, only: %i[index] do
        collection do
          get :activity_types
        end
      end

      resources :daily_customer_profile, only: %i[index]
      resources :hourly_guest_visit, only: %i[index]
      resources :closing, only: %i[index]
      resources :cash_closing, only: %i[index]
      resources :pre_order, only: %i[index]
      resources :customer_point_movement, only: %i[index]

      # aliases
      get :inventory_movement_per_sections, to: "inventory_movements_per_section#index"
      get :stock_wastes_by_section, to: "stock_wastes_per_section#index"
      get :product_stocks_by_section, to: "product_stocks_per_section#index"
    end

    resources :notifications, only: ['index', 'show'] do
      collection do
        patch :read
        get :total_active_notification
        get :locations
      end
    end

    resources :access_lists, only: [:index] do
      collection do
        post :check
        get :order_menu
        post :check_all_by_location_type
      end
    end

    resource :profile, only: %i[update] do
      get :detail
      get :brands
      post :switch_user
      patch :switch_brand
      patch :update_password
      patch :update_pin
      post :reset_pin
      get :send_activation_email
      post :brands, to: 'profiles#new_brand'
      post :upload_url
    end

    resources :option_sets, only: %i[index create show update destroy] do
      member do
        get :history
      end

      resources :option_set_custom_price_locations, param: :location_id, only: %i[index show update destroy] do
        member do
          post :create
        end
      end
    end

    resources :product_option_sets, only: %i[index]

    resources :products, except: %i[new edit] do
      collection do
        get :backoffice_index
        get :group_by_category
        post :upload_url
        get :last_price
        post :state_acknowledgement, to: 'products#state_acknowledgement'
        get :export_recipe
        get :export_menu
      end

      resources :variance_attributes, only: %i[index create destroy] do
        member do
          patch :change_order
        end
      end

      resources :variance_details, only: %i[index create update destroy] do
        collection do
          delete :destroy_all
        end
      end

      resources :customize_product_locations, only: %i[index show update destroy], param: :location_id

      resources :product_setting_locations, only: %i[index create destroy update]
      resources :product_expiries, only: %i[index create update destroy] do
        collection do
          post :bulk_create
        end
      end

      member do
        get :variances
        get :show_recent_orders
        patch :change_smallest_unit
        patch :deactivate
        patch :reactivate
        patch :toggle_auto_prompt_option_set
        patch :update_option_sets
        get :history
        get :stock_and_price
        get :locations
      end

      collection do
        get :sell_price_bulk
        get :generate_barcode
        patch :bulk_update_option_sets
        patch :update_sell_price_bulk
        patch :update_internal_price_bulk
        patch :update_sell_tax_bulk
        patch :bulk_deactivate
        patch :bulk_reactivate
        get :inventories_processing_status
        get :inventories_quantities
        get :multibrand
        get :dashboard_product_out_of_stock
      end

      resources :product_option_sets, only: %i[index create update destroy]
      resources :product_maximum_orders, only: %i[index create update destroy], controller: '/restaurant/controllers/product_maximum_orders'
    end

    resources :product_groups do
      member do
        patch :deactivate
        patch :reactivate
      end
    end

    resources :product_coupons, except: %i[new edit] do
      collection do
        get :export
      end
    end

    resources :delivery_returns, except: %i[new edit], controller: '/restaurant/controllers/delivery_returns' do
      collection do
        post :upload_url
      end

      member do
        patch :confirm_waste
        patch :confirm_return
        patch :reject
        get :history
      end
    end

    resources :deliveries, except: %i[new edit] do
      member do
        get :history
        patch :receive
        get :return
        get :put_backs
        post :confirm_waste
        post :confirm_return
        get :acceptance_form
      end

      collection do
        post :upload_url
        get :storage_section_suggestion
      end
    end

    resources :orders, except: %i[new edit destroy] do
      collection do
        post 'new', to: 'orders#new'
        get :location_from
        get :location_to
        get :available_delivery_location_to
        get :available_delivery_location_from
        post :upload_url
        post :bulk_create
        get :unavailable_units
        post :order_attachments_upload_url
        get :customers
      end

      member do
        get :fulfillment # Should be the form to create order fulfillment (want to create order fulfillment)
        get :fulfillment_edit # Want to edit the order fulfillment
        get :open_quantity_order_lines
        get :check_validity_before_close
        patch :payment_status
        get :invoice
        patch :update_shipping_fee
        patch :approve
        patch :void
        patch :close
        get :show_with_delivery
        get :related_transactions
        get :history
        post :pay_detail
        post :pay
        post :manual_payment
        post :shipping_fee_pay_detail
        post :shipping_fee_pay
        get :payment_info
        patch :hide_online_payment_display
        patch :hide_online_shipping_fee_payment_display
        patch :hide_online_shipping_fee_added_display
        post :remind_payment
        get :online_payment_invoices
        get :order_lines
        get :unavailable_products
        get :multibrand_unavailable_products
        get :approvals
        post :remind_approvals
        get :approval_users
      end
    end

    namespace :procurement_payment_settings do
      get '/', to: '/restaurant/controllers/procurement_payment_settings#show'
      patch '/', to: '/restaurant/controllers/procurement_payment_settings#update'
      patch '/payment_methods', to: '/restaurant/controllers/procurement_payment_settings#update_payment_methods'
    end

    get '/multibrand_procurement_payment_settings', to: '/restaurant/controllers/procurement_payment_settings#multibrand_procurement_payment_settings'

    resource :production_settings, only: %i[show update], controller: '/restaurant/controllers/production_settings'

    resource :dine_in_fee_settings, only: %i[show update], controller: '/restaurant/controllers/dine_in_fee_settings'

    resource :report_settings, only: %i[show update], controller: '/restaurant/controllers/report_settings'

    resource :online_ordering_fee_settings, only: %i[show update], controller: '/restaurant/controllers/online_ordering_fee_settings'

    resources :product_categories, except: %i[new edit] do
      collection do
        get :filter_with_products
        get :multibrand_filter_with_products
      end

      member do
        patch :deactivate
        patch :reactivate
        get :history
        get :with_products
        get :products, to: 'product_category_products#index'
        delete '/products/:product_id', to: 'product_category_products#destroy'
      end
    end

    resources :product_units, except: %i[new edit] do
      member do
        get :history
      end
      collection do
        get :product_unit_conversions
        get :procurement_units
      end
    end

    resources :object_layouts, only: [:index]

    resources :location_groups do
      member do
        get :history
      end
    end

    resources :costings, only: [:index] do
      collection do
        post :bulk_create
        delete :bulk_destroy
        get :bulk_check
      end
    end

    namespace :sales_targets do
      patch 'bulk_update', to: '/restaurant/controllers/sales_targets#bulk_update'
      delete 'bulk_destroy', to: '/restaurant/controllers/sales_targets#bulk_destroy'
    end

    resources :loyalties, except: %i[new edit destroy], controller: '/loyalty/controllers/loyalties' do
      collection do
        get :dashboard
        get :products
        get :product_categories
      end
    end

    resources :customer_categories, only: %i[index show create update destroy] do
      member do
        get :customers
        get :customer_count
      end
    end

    resources :customer_point, except: %i[new edit destroy update create index], controller: '/loyalty/controllers/customer_point' do
      member do
        get :history
        post :reserved
        post :unreserved
        post :adjust_point
        post :send_otp
        post :verify_otp
        post 'void_point/:customer_point_history_id', to: '/loyalty/controllers/customer_point#void_point'
      end

      collection do
        post :calculate_earn_redeem_point
      end
    end

    resources :sub_brands, except: %i[new edit] do
      member do
        get :history
        get :locations_setting
      end
      collection do
        post :upload_url
      end
    end

    resources :locations_sub_brands, only: %i[index update]

    resources :money_movements, only: %i[index create update show] do
      member do
        patch :void
        get :history
      end

      collection do
        post :upload_url
        post :state_acknowledgement
      end
    end

    resources :money_movement_categories, controller: '/restaurant/controllers/money_movement_categories' do
      member do
        patch :reactivate
        patch :deactivate
      end
    end

    resources :customers, only: %i[index show] do
      collection do
        get :dashboard_weekly_customers
      end

      member do
        get :average_spending
        get :recent_purchases
        get :last_spending
      end

      resource :customer_deposits, only: %i[] do
        collection do
          get :balance
          get :deposit_history
          get :customer_balance_locations
          post :refund_deposit
        end
      end
    end

    resources :open_orders, only: %i[index show] do
      collection do
        get :total_open_orders
      end
    end

    resources :productions, only: %i[new index create show] do
      collection do
        post :bulk_create
      end

      member do
        patch :void
        get :history
        get :sticker
      end
    end

    resources :locations, except: %i[new edit destroy] do
      collection do
        patch :opening_hour
        get :royalty_schemas
        patch :update_all_user_locations_cogs_include_tax
        get :can_access_all_internal_locations
      end

      resources :productions, only: %i[new index create show], controller: '/restaurant/controllers/deprecated_productions' do
        collection do
          post :bulk_create
        end

        member do
          patch :void
          get :history
          get :sticker
        end
      end

      resource :customer_display, except: %i[create new edit destroy] do
        post :upload_url
      end

      resource :costings, only: [:show]

      resources :stock_openings, only: %i[index new create update destroy] do
        collection do
          get :new_stock_opening_lines
        end

        member do
          get :history
        end
      end

      member do
        get :payment_method_custom_fees, to: 'payment_method_custom_fees#by_location'
        get :show_recent_orders
        patch :deactivate
        patch :reactivate
        patch :override_online_delivery_settings
        get :history
        get :store_statuses
        patch :update_store_statuses
        patch :closed_store
        patch :open_store

        get :queue_display_settings, to: 'queue_display_settings#show'
        patch :queue_display_settings, to: 'queue_display_settings#update'
        put :queue_display_settings, to: 'queue_display_settings#update'
        post 'queue_display_settings/upload_url', to: 'queue_display_settings#upload_url'
        post 'queue_display_settings/duplicate', to: 'queue_display_settings#duplicate'

        get :pos_setting, to: 'pos_settings#show'
        patch :pos_setting, to: 'pos_settings#update'
        post :lock_devices, to:'pos_settings#lock_devices'
        post :unlock_devices, to:'pos_settings#unlock_devices'
        post :duplicate_pos_setting, to: 'pos_settings#duplicate'
        post :duplicate_printer_setting, to: 'pos_settings#duplicate_printer_setting'
        put :printer_settings, to: 'pos_settings#printer_settings'
        get :disbursement_setting
        get :printer_settings, to: 'pos_settings#show_printer_settings'
        get :printers, to: 'pos_settings#printers'
        put :printers, to: 'pos_settings#update_printers'
        put :royalty_schemas, to: 'locations#apply_royalty_schemas'
        get :royalty_schemas, to: 'locations#detailed_royalty_schemas'
        get :stock_by_date, to: 'products#stock_by_date'
        post 'printer_settings/:upload_url', to: 'pos_settings#upload_url'
        get :integration_store_status_history
        get :integration_store_status_chart

        get :table_session_setting, to: '/reservation/controllers/table_session_settings#show'
        patch :table_session_setting, to: '/reservation/controllers/table_session_settings#update'
        get "table_session_setting/printers", to: '/reservation/controllers/table_session_settings#printers'
        patch "table_session_setting/printers", to: '/reservation/controllers/table_session_settings#update_printers'
      end

      resources :table_sessions, controller:'/reservation/controllers/table_sessions', only: %i[create update index show] do
        collection do
          get :timeslot_capacities
          get :table_capacities
        end
      end

      resources :pos_product_layouts, only: %i[create index] do
        collection do
          post :duplicate
          patch :update
          patch :update_sequence
        end
      end

      resources :delivery_product_layouts, only: %i[index show create] do
        collection do
          post :duplicate
          patch :update
          patch :update_sequence
        end
      end

      resources :popular_menu, only: %i[index] do
        collection do
          patch :update
        end
      end

      resources :production_schedules, only: %i[index show create update destroy] do
        collection do
          get :today
          get :watchlist
          get :by_date
        end

        member do
          patch :deactivate
          patch :reactivate
        end
      end

      resources :disassemble_transactions, only: %i[new index create show] do
        member do
          patch :void
          get :history
        end
      end

      resource :open_orders, only: %i[] do
        collection do
          get :checkpoint
        end
      end

      resources :customers, only: %i[index create update show] do
        collection do
          # NOTE: Only Dashboard Weekly Customers is requested
          # get :dashboard_average_spending
          # get :dashboard_top_spending
          get :dashboard_weekly_customers
          get :balance_checkpoint
          get :find, to: 'customers#find'

          # customer otp
          post :send_otp
          post :verify_otp
        end

        member do
          patch :archive
          patch :unarchive
          get :average_spending
          get :last_spending
          get :recent_purchases
          get :history

          #customer deposit
          get :balance
          get :deposit_history
          post :add_deposit
          post :refund_deposit
          post :send_otp_use_deposit
          post :verify_otp_use_deposit
          post :use_deposit
          post :release_deposit
        end

        resources :customer_account_transactions, only: [] do
          member do
            patch :void_add_deposit
            patch :void_refund_deposit
          end
        end
      end

      resources :customer_account_transactions, only: [] do
        collection do
          get :checkpoint
        end
      end

      resources :products, only: [] do
        collection do
          get :export_menu
          get :export_buy_price
          get :favorite_ids
          patch :bulk_mark_available_stock
          get :list_stock_availability
        end

        member do
          patch :out_of_stock
          patch :available
          patch :mark_available_stock
          patch :favorite
          patch :unfavorite
          get :stock_availability
          get 'unit/:unit_id', to: 'products#converted_unit'
          get 'unit/:unit_id/available_stock', to: 'products#available_stock'
        end

        resources :recipes, only: %i[create show update destroy] do
          member do
            get :history
            patch :deactivate
            patch :reactivate
          end
        end
      end

      resources :order_types, only: [] do
        collection do
          patch :service_charge, to: 'service_charge_locations#update_service_charge'
        end
      end

      resources :service_charge_locations, only: [:index] do
        member do
          get :history
        end
      end

      resources :service_charge_location_print_names, only: [:index, :create, :update, :destroy]

      resources :sales_queues, only: [:create] do
        collection do
          post :receipt
        end
      end

      resources :sale_transactions, only: [] do
        collection do
          get :checkpoint
          get :checkpoint_all_devices
          get :last_receipt_sequence
        end
      end

      resources :sales_returns, only: [] do
        collection do
          get :checkpoint
        end
      end

      # still used by POS
      resources :money_movements, only: %i[] do
        collection do
          get :checkpoint
          post :upload_url
        end
      end

      resources :takings, only: %i[index create show] do
        member do
          patch :void
          get :history
        end
        collection do
          get :checkpoint
        end
      end

      resources :section_layouts, only: %i[index create update destroy] do
        member do
          patch :hide
          patch :unhide
          get :history
        end

        collection do
          post :upload_url
        end

        resources :section_object_details, only: %i[index create update destroy] do
          collection do
            post :bulk
          end
        end
      end

      resources :product_expiries, only: %i[index]

      resource :customer_deposits, only: %i[] do
        collection do
          get :deposit_history_location
        end
      end

      resources :users, except: %i[new edit] do
        post :invite, on: :member
        post :resend_invitation, on: :member
        get :invited_list, on: :collection
        patch :change_pin, on: :member
      end

      resources :storage_sections, only: %i[index create update destroy] do
        collection do
          get :exists
        end
        member do
          patch :deactivate
          patch :reactivate
          patch :set_default_in
          patch :set_default_out
        end
      end

      resources :category_storage_sections, param: :product_category_id, only: %i[index create destroy]
    end

    resources :disassemble_transactions, only: %i[new index create show] do
        member do
          patch :void
          get :history
        end
      end

    resources :stock_openings, only: %i[show]

    resources :vendors, only: %i[index create update show] do
      collection do
        get :export_product
      end

      member do
        patch :archive
        patch :unarchive
        get :history
        get :recent_orders
      end

      get :existing_products, controller: '/restaurant/controllers/vendor_products'
      resources :vendor_products, controller: '/restaurant/controllers/vendor_products', only: %i[index]
    end

    resources :vendor_products, only: %i[index] do
      collection do
        put :bulk_create, controller: '/restaurant/controllers/vendor_products'
      end
    end

    resources :web_push_tokens, only: %i[create] do
      collection do
        delete :destroy
      end
    end

    resources :customer_orders, only: %i[index show update] do
      collection do
        post :confirm
        post :cancel
        post :wait_driver_to_pickup
        post :driver_pickup
        post :pickup
        post :complete_delivery
        post :complete
        post :ready
        post :state_acknowledgement
        get :grab_food_cancellable
        get :dine_in_checkpoint
        get :count
        post :simulate_order
        get :total_ongoing_online_orders
        post :upload_url
      end

      member do
        patch :set_receipt_no
        patch :set_reprint_user
        patch :set_kds_data
        post :calculate_split_payment
        post :receive_split_payment
        post :release_payment
      end
    end

    resources :products_stock_limits, only: %i[index] do
      collection do
        post :bulk_create
        post :mark_available_stock_product
      end
    end

    resources :food_integrations, only: %i[index] do
      collection do
        get :grab_food
        get :go_food
        get :shopee_food
        get :locations
        get :location_detail
        get :check_integration_types
        get :sync_menu_logs
        get :sync_menu_schedule

        post :sync_menus
        post :retry_sync_menu
        post :sync_promos
        delete :remove_sync_menu_schedule
      end
    end

    resources :money_movement_category_groups, controller: '/restaurant/controllers/money_movement_category_groups'

    get 'push_notifications', to: '/restaurant/controllers/push_notifications#index'

    resources :approval_settings, only: %i[index create]

    namespace :demand_prediction do
      resources :ingestions, only: [:create], controller: "/demand_prediction/controllers/ingestions"
    end
  end
end
