# SQLite version 3.x
#   gem install pg
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem 'mysql2'
#
default: &default
  adapter: postgresql
  encoding: utf8
  pool: <%= ENV.fetch("RAILS_MAX_THREADS", 5) %>
  port: 5432
  timeout: 5000
default_clickhouse: &default_clickhouse
  adapter: clickhouse
  pool: <%= ENV.fetch("RAILS_MAX_THREADS", 5) %>
  read_timeout: 300 # change network timeouts, by default 60 seconds
  write_timeout: 300
  keep_alive_timeout: 300
  migrations_paths: db/other_db/clickhouse/migrate

development:
  primary:
    <<: *default
    database: <%= ENV['RDS_DEV_DATABASE'] %>
    username: <%= ENV['RDS_DEV_USERNAME'] %>
    password: <%= ENV['RDS_DEV_PASSWORD'] %>
    host: <%= ENV['RDS_DEV_HOSTNAME'] %>
    port: <%= ENV['RDS_DEV_PORT'] %>
  report_readonly:
    <<: *default
    replica: true
    database: <%= ENV['RDS_DEV_DATABASE'] %>
    username: <%= ENV['RDS_DEV_REPLICA_USERNAME'].presence || ENV['RDS_DEV_USERNAME'] %>
    password: <%= ENV['RDS_DEV_REPLICA_PASSWORD'].presence || ENV['RDS_DEV_PASSWORD'] %>
    host: <%= ENV['RDS_DEV_REPLICA_HOSTNAME'].presence || ENV['RDS_DEV_HOSTNAME'] %>
    port: <%= ENV['RDS_DEV_REPLICA_PORT'].presence || ENV['RDS_DEV_PORT'] %>
  audit_database:
    <<: *default
    database: <%= ENV['RDS_DEV_AUDIT_DATABASE'] %>
    username: <%= ENV['RDS_DEV_AUDIT_USERNAME'].presence %>
    password: <%= ENV['RDS_DEV_AUDIT_PASSWORD'].presence %>
    host: <%= ENV['RDS_DEV_AUDIT_HOSTNAME'].presence %>
    migrations_paths: db/other_db/audit_db/migrate
    pool: <%= ENV.fetch("RAILS_MAX_THREADS_AUDIT", 5) %>
  rf_database:
    adapter: nulldb
  clickhouse:
    <<: *default_clickhouse
    database: <%= ENV.fetch('CLICKHOUSE_DEV_DATABASE', 'runchise_clickhouse_dev') %>
    host: <%= ENV.fetch('CLICKHOUSE_DEV_HOST', 'localhost') %>
    port: <%= ENV.fetch('CLICKHOUSE_DEV_PORT', 8123) %>
    username: <%= ENV.fetch('CLICKHOUSE_DEV_USERNAME', 'default') %>
    password: <%= ENV['CLICKHOUSE_DEV_PASSWORD'] %>

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  primary:
    <<: *default
    database: <%= ENV['RDS_DATABASE_TEST'] %><%= ENV['TEST_ENV_NUMBER'] %>
    username: <%= ENV['RDS_USERNAME_TEST'] %>
    password: <%= ENV['RDS_PASSWORD_TEST'] %>
    host: <%= ENV['RDS_HOST_TEST'] %>
    port: <%= ENV['RDS_PORT_TEST'] %>
    idle_timeout: <%= ENV['RDS_IDLE_TIMEOUT'].presence || 300 %>
  clickhouse:
    <<: *default_clickhouse
    database: <%= ENV.fetch('CLICKHOUSE_TEST_DATABASE', 'runchise_clickhouse_test') %><%= ENV['TEST_ENV_NUMBER'] %>
    host: <%= ENV.fetch('CLICKHOUSE_TEST_HOST', 'localhost') %>
    port: <%= ENV.fetch('CLICKHOUSE_TEST_PORT', 8123) %>
    username: <%= ENV.fetch('CLICKHOUSE_TEST_USERNAME', 'default') %>
    password: <%= ENV['CLICKHOUSE_TEST_PASSWORD'] %>

production:
  primary:
    <<: *default
    database: <%= ENV['RDS_DATABASE'] %>
    username: <%= ENV['RDS_USERNAME'] %>
    password: <%= ENV['RDS_PASSWORD'] %>
    host: <%= ENV['RDS_HOSTNAME'] %>
    statement_limit: <%= ENV['RDS_STATEMENT_LIMIT'].presence || 500 %> # NOTE: put default to 500
    idle_timeout: <%= ENV['RDS_IDLE_TIMEOUT'].presence || 300 %>
  backup:
    <<: *default
    database: <%= ENV['RDS_DATABASE_BACKUP'] %>
    username: <%= ENV['RDS_USERNAME_BACKUP'] %>
    password: <%= ENV['RDS_PASSWORD_BACKUP'] %>
    host: <%= ENV['RDS_HOSTNAME_BACKUP'] %>
    statement_limit: <%= ENV['RDS_STATEMENT_LIMIT'].presence || 500 %> # NOTE: put default to 500
    idle_timeout: <%= ENV['RDS_IDLE_TIMEOUT'].presence || 300 %>
  report_readonly:
    <<: *default
    replica: true
    # Replica database name must be the same like the primary
    database: <%= ENV['RDS_DATABASE'] %>
    username: <%= ENV['RDS_REPLICA_USERNAME'].presence || ENV['RDS_USERNAME'] %>
    password: <%= ENV['RDS_REPLICA_PASSWORD'].presence || ENV['RDS_PASSWORD'] %>
    host: <%= ENV['RDS_REPLICA_HOSTNAME'].presence || ENV['RDS_HOSTNAME'] %>
  rf_database:
    adapter: sqlserver
    host: <%= Rails.application.credentials.dig(:richeese_factory, :database_host) %>
    port: <%= Rails.application.credentials.dig(:richeese_factory, :database_port) %>
    database: <%= Rails.application.credentials.dig(:richeese_factory, :database_name) %>
    username: <%= Rails.application.credentials.dig(:richeese_factory, :database_username) %>
    password: <%= Rails.application.credentials.dig(:richeese_factory, :database_password) %>
    appname: <%= "myapp_#{Process.pid}" %>
    migrations_paths: db/other_db/rf_db/migrate
  audit_database:
    <<: *default
    database: <%= ENV['RDS_AUDIT_DATABASE'] %>
    username: <%= ENV['RDS_AUDIT_USERNAME'].presence %>
    password: <%= ENV['RDS_AUDIT_PASSWORD'].presence %>
    host: <%= ENV['RDS_AUDIT_HOSTNAME'].presence %>
    migrations_paths: db/other_db/audit_db/migrate
  clickhouse:
    <<: *default_clickhouse
    database: <%= ENV['CLICKHOUSE_DATABASE'].presence || 'runchise' %>
    host: <%= ENV['CLICKHOUSE_HOST'].presence %>
    port: <%= ENV['CLICKHOUSE_PORT'].presence || 8123 %>
    username: <%= ENV['CLICKHOUSE_USERNAME'].presence || 'default' %>
    password: <%= ENV['CLICKHOUSE_PASSWORD'] %>