id:
  notification:
    title: "Notifikasi"
    all_up_to_date: "semua sudah dibaca!"
    new_feeds_count: "%{count} berita baru"
    patch_read_error: "Gagal untuk membaca notifikasi"
    empty_notification: "Anda tidak memiliki notifikasi"
    fetch_notifications_error: "Gagal mendapatkan daftar notifikasi"
    fetch_active_notification_error: "Gagal mendapatkan daftar notifikasi terbaru"
    mark_all: "Baca semua"
    import_data:
      product: "Impor produk sudah selesai, %{count}/%{total_count} berhasil terbuat."
      customer: "Impor pelanggan sudah selesai, %{count}/%{total_count} berhasil terbuat."
      customer_point: "Impor poin pelanggan sudah selesai, %{count}/%{total_count} berhasil dirubah."
      daily_sale: "Impor penjualan %{date} sudah selesai."
      daily_sale_failed: "Impor penjualan %{date} gagal."
      order_transaction:
        create: "Impor pesanan %{date} sudah selesai."
        failed: "Impor pesanan %{date} gagal."
      stock_adjustment: "Impor hitung stock di %{location_name} selesai dengan status %{status}"
      stock_opening:
        create: "Impor stok awal di %{location_name} selesai dengan status %{status}"
        failed: "gagal dengan alasan: Impor stok awal memiliki error"
      recipe_storage_sections: "Impor area penyimpanan resep di %{location_name} selesai"
      recipe_storage_sections_with_error: "Impor area penyimpanan resep di %{location_name} ada yang salah"
      money_movement: "Pergerakan uang impor telah selesai"
      money_movement_with_error: "Pergerakan uang impor mengalami kesalahan"
      recipe: "Berhasil import semua resep"
      recipe_with_error: "Impor resep gagal"
      stock_in: "Berhasil mengimpor semua stok masuk"
      stock_in_with_error: "Impor stok masuk gagal"
      stock_out: "Berhasil mengimpor semua stok keluar"
      stock_out_with_error: "Impor stok keluar gagal"
    order_transaction:
      create: "Ada pesanan baru %{order_no} dari %{location_name}"
      update: "Pesanan %{order_no} dari %{location_name} telah diubah"
      remind_payment: "Pengingat pembayaran untuk %{order_no} %{location_name}, untuk melanjutkan pengiriman barang."
      remind_shipping_fee_payment: "Pengingat biaya pengiriman untuk %{order_no} untuk segera dibayar"
      added_shipping_fee: "%{order_no} telah ditambahkan biaya pengiriman"
      paid_shipping_fee: "%{order_no} biaya pengiriman telah dibayarkan"
      paid: "Pembayaran berhasil untuk %{order_no} ke %{location_name}"
      buyer_paid: "Pembayaran berhasil untuk %{order_no} ke %{location_name}"
      remind_limit: "Pengingat pembayaran untuk %{order_no} %{location_name} sudah melewati batas. Coba lagi Besok."
      update_shipping_fee: "Biaya Pengiriman pesanan %{order_no} dari %{location_name} berhasil diubah"
      update_prices: "Pesanan %{order_no}: Harga telah diperbarui."
      approve: "Pesanan %{order_no} dari %{location_from_name} ke %{location_to_name} telah disetujui."
      void: "Pesanan %{order_no} telah dibatalkan."
      close: "Order %{order_no} ke %{location_name} telah berhasil ditutup"
      manual_refund: "Transaksi %{order_no} telah dibayar dan dibatalkan. Harap selesaikan pengembalian dana secara manual."
      order_approval_reminder: "Ada pesanan %{order_no} ke %{location_name} yang menunggu persetujuan Anda."
      autovoid_reminder: "%{order_no} akan kadaluwarsa dalam %{minutes} menit. Selesaikan pembayaran sekarang untuk menghindari pembatalan pesanan."
      autovoid_completion_alert: "%{order_no} dibatalkan pada %{autovoided_at} karena pesanan belum dibayar setelah %{minutes} menit."
    delivery:
      create: "Pengiriman %{delivery_no} ke %{location_name} telah dikirim"
      update: "Pengiriman %{delivery_no} ke %{location_name} telah diubah"
      receive: "Pengiriman %{delivery_no} dari %{location_name} telah diterima"
      incomplete: "Pengiriman %{delivery_no} dari %{location_name} telah diterima namun tidak lengkap"
      destroy: "Pengiriman %{delivery_no} ke %{location_name} telah dihapus"
    delivery_return:
      create:
        header: "Pengembalian Masuk"
        content: "Pengembalian %{return_no} %{location_action}"
        send_from_location: "dari %{location_name} telah diajukan"
        send_to_vendor: "ke %{vendor_name} telah dikirim"
      create_fulfillment:
        content: "Pengembalian %{return_no} dari pengiriman %{deliveries_no} %{location_action}"
      rejected:
        header: "Pengembalian ditolak"
        content: "Permohonan pengembalian barang %{return_no} telah ditolak."
    incoming_delivery:
      receive: "Pengiriman %{delivery_no} dari %{location_from_name} telah diterima"
    stock:
      par: "%{location_name} memiliki %{count} produk di bawah kuantitas minimum. Silahkan review!"
      stock_transfer: "Perpindahan stok %{id} dari %{location_from_name} ke %{location_to_name} telah dibuat."
    taking:
      create: "Outlet %{location_name} sudah melakukan closing penjualan."
    grab_food:
      failed_menu_sync: "Gagal menyinkronkan menu ke Grab Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync: "Gagal membuat promosi %{promo_name} ke Grab Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync: "Gagal membuat Grab Food order #{order_id} di system runchise. Mohon cek Grab Food app di %{location_name}"
    go_food:
      failed_menu_sync: "Gagal menyinkronkan menu ke Go Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync: "Gagal membuat promosi %{promo_name} ke Go Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync: "Gagal membuat Go Food order %{order_id} di system runchise. Mohon cek Go Food app di %{location_name}"
    shopee_food:
      failed_menu_sync: "Gagal menyinkronkan menu ke Shopee Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync: "Gagal membuat promosi %{promo_name} ke Shopee Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync: "Gagal membuat Shopee Food order %{order_id} di system runchise. Mohon cek Shopee Food app di %{location_name}"
    royalty_transaction:
      creation_failed: "Gagal membuat skema royalti"
      creation_completed: "Seluruh skema royalti telah berhasil dibuat."
    bulk_product_activation:
      activation_success: "%{success_count} item telah berhasil di aktifkan"
      activation_failed: "%{success_count} item telah berhasil di aktifkan, %{failed_count} item gagal untuk di aktifkan"
      deactivation_success: "%{success_count} item telah berhasil di non-aktifkan"
      deactivation_failed: "%{success_count} item telah berhasil di non-aktifkan, %{failed_count} item gagal untuk di non-aktifkan"
  push_notif:
    import_data:
      product:
        header: "Impor produk sudah selesai"
        content: "%{count}/%{total_count} berhasil terbuat."
      customer:
        header: "Impor pelanggan sudah selesai"
        content: "%{count}/%{total_count} berhasil terbuat."
      customer_point:
        header: "Impor point pelanggan sudah selesai"
        content: "%{count}/%{total_count} berhasil dirubah."
      stock_adjustment:
        header: "Impor hitung stok di %{location_name} %{status}"
        content_success: "%{count} produk berhasil dicatat"
        content_failure: "alasan: %{reason}"
      stock_opening:
        header: "Impor stok awal di %{location_name} %{status}"
        content_success: "%{count} produk berhasil dicatat"
        content_failure: "alasan: %{reason}"
      daily_sale:
        header: "Impor penjualan sudah selesai"
        content: "Penjualan tanggal %{date} telah terbuat."
      daily_sale_failed:
        header: "Impor penjualan gagal"
        content: "Cek rekapan untuk memperbaiki yang salah."
      order_transaction:
        header: "Impor pesanan sudah selesai"
        content: "Pesanan tanggal %{date} telah terbuat."
      order_transaction_failed:
        header: "Impor pesanan gagal"
        content: "Cek rekapan untuk memperbaiki yang salah."
      recipe_storage_sections:
        header: "Import area penyimpanan resep di %{location_name} selesai"
        content: "%{count}/%{total_count} resep diperbarui"
      recipe_storage_sections_with_error:
        header: "Import area penyimpanan resep di %{location_name} ada yang salah"
        content: "%{count}/%{total_count} resep diperbarui, cek rekapan untuk memperbaiki yang salah."
      money_movement:
        header: "Impor pergerakan uang telah selesai"
        content: "%{count}/%{total_count} pergerakan uang telah diperbarui"
      money_movement_with_error:
        header: "Impor pergerakan uang mengalami kesalahan"
        content: "%{count}/%{total_count} pergerakan uang telah diperbarui, mari kita periksa ringkasan untuk memperbaiki kesalahannya"
      recipe:
        header: "Berhasil impor semua resep"
        content: "%{count}/%{total_count} telah dibuat."
      recipe_with_error:
        header: "Impor resep gagal"
        content: "Impor resep gagal. Periksa kesalahan."
      stock_in:
        header: "Impor stok masuk selesai"
        content: "%{count}/%{total_count} stok masuk diperbarui"
      stock_in_with_error:
        header: "Impor stok masuk ada kesalahan"
        content: "%{count}/%{total_count} stok masuk diperbarui, mari kita periksa ringkasan untuk memperbaiki kesalahan"
      stock_out:
        header: "Impor stok keluar selesai"
        content: "%{count}/%{total_count} stok keluar diperbarui"
      stock_out_with_error:
        header: "Impor stok keluar ada kesalahan"
        content: "%{count}/%{total_count} stok keluar diperbarui, mari kita periksa ringkasan untuk memperbaiki kesalahan"
    order_transaction:
      autovoid_completion_alert:
        header: "Pesanan Dibatalkan – Waktu Habis!"
        content: "%{order_no} dibatalkan pada %{autovoided_at} karena pesanan belum dibayar setelah %{minutes} menit."
      autovoid_reminder:
        header: "Waktu Hampir Habis! Segera Selesaikan Pembayaranmu"
        content: "%{order_no} akan kadaluwarsa dalam %{minutes} menit. Selesaikan pembayaran sekarang untuk menghindari pembatalan pesanan."
      create:
        header: "Pesanan baru masuk"
        content: "Pesanan %{order_no} dari %{location_name}."
      update:
        header: "Pesanan telah diubah"
        content: "Pesanan %{order_no} dari %{location_name} telah diubah."
      update_shipping_fee:
        header: "Biaya pengiriman pesanan telah diubah"
        content: "Biaya pengiriman pesanan %{order_no} dari %{location_name} telah diubah."
      update_prices:
        header: "Pesanan %{order_no}: Harga telah diperbarui."
        content: "Pesanan %{order_no}: Harga telah diperbarui."
      remind_payment:
        header: "Pengingat pembayaran untuk %{order_no}, %{location_name}"
        content: "Pengingat pembayaran untuk %{order_no}, %{location_name} untuk melanjutkan pengiriman barang."
      remind_shipping_fee_payment:
        header: "Pengingat pembayaran biaya pengiriman untuk %{order_no}"
        content: "Pengingat pembayaran biaya pengiriman untuk %{order_no} untuk segera dibayar"
      added_shipping_fee:
        header: "%{order_no} telah ditambahkan biaya pengiriman"
        content: "%{order_no} telah ditambahkan biaya pengiriman"
      paid_shipping_fee:
        header: "%{order_no} biaya pengiriman telah dibayarkan"
        content: "%{order_no} biaya pengiriman telah dibayarkan"
      remind_limit:
        header: "Pengingat pembayaran untuk %{order_no}, %{location_name} mencapai batas harian"
        content: "Pengingat pembayaran untuk %{order_no}, %{location_name} mencapai batas harian."
      paid:
        header: "Pembayaran berhasil untuk %{order_no} ke %{location_name}"
        content: "Pembayaran berhasil untuk %{order_no} ke %{location_name}, memproses order."
      buyer_paid:
        header: "Pembayaran berhasil untuk %{order_no} ke %{location_name}"
        content: "Pembayaran berhasil untuk %{order_no} ke %{location_name}. Barang akan segera dikirim."
      approve:
        header: "Pesanan telah disetujui"
        content: "Pesanan %{order_no} dari %{location_from_name} ke %{location_to_name} telah disetujui."
      void:
        header: "Pesanan telah dibatalkan"
        content: "Pesanan %{order_no} telah dibatalkan."
      close:
        header: "Pesanan telah diselesaikan secara manual"
        content: "Pesanan %{order_no} ke %{location_name} telah diselesaikan secara manual."
      manual_refund:
        header: "Pembatalan transaksi yang telah dibayar"
        content: "Transaksi %{order_no} telah dibayar dan dibatalkan. Harap selesaikan pengembalian dana secara manual."
      order_approval_reminder:
        header: "Pesanan sedang menunggu persetujuan anda"
        content: "Ada pesanan %{order_no} ke %{location_name} yang menunggu persetujuan Anda."
    delivery:
      create:
        header: "Pengiriman sudah dikirim"
        content: "Pengiriman %{delivery_no} ke %{location_name} sudah dikirim."
      update:
        header: "Pengiriman sudah diubah"
        content: "Pengiriman %{delivery_no} ke %{location_name} sudah diubah."
      receive:
        header: "Pengiriman sudah diterima"
        content: "Pengiriman %{delivery_no} dari %{location_name} sudah diterima."
      incomplete:
        header: "Pengiriman tidak lengkap, tindakan diperlukan"
        content: "Pengiriman %{delivery_no} dari %{location_name} sudah diterima namun tidak lengkap."
      destroy:
        header: "Pengiriman sudah dihapus"
        content: "Pengiriman %{delivery_no} ke %{location_name} sudah dihapus."
    incoming_delivery:
      receive:
        header: "Pengiriman sudah diterima"
        content: "Pengiriman %{delivery_no} dari %{location_from_name} sudah diterima."
      incomplete:
        header: "Pengiriman tidak lengkap, tindakan diperlukan"
        content: "Pengiriman %{delivery_no} dari %{location_from_name} sudah diterima namun tidak lengkap."
    stock:
      par:
        header: "Stok hampir habis!"
        content: "%{location_name} memiliki %{count} produk dengan stok dibawah minimum. Silahkan review."
    taking:
      create:
        header: "Closing penjualan!"
        content: "Outlet %{location_name} sudah selesai melakukan closing penjualan."
    grab_food:
      failed_menu_sync:
        header: "Gagal menyikronkan menu ke Grab Food"
        content: "Gagal menyinkronkan menu ke Grab Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync:
        header: "Gagal membuat promosi ke Grab Food"
        content: "Gagal membuat promosi %{promo_name} ke Grab Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync:
        header: "Gagal membuat Grab Food order di system Runchise"
        content: "Gagal membuat Grab Food order %{order_id} di system runchise. Mohon cek Grab Food app di %{location_name}"
    go_food:
      failed_menu_sync:
        header: "Gagal menyikronkan menu ke Go Food"
        content: "Gagal menyinkronkan menu ke Go Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync:
        header: "Gagal membuat promosi ke Go Food"
        content: "Gagal membuat promosi %{promo_name} ke Go Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync:
        header: "Gagal membuat order Go Food di system Runchise"
        content: "Gagal membuat order Go Food %{order_id} di system runchise. Mohon cek Go Food app di %{location_name}"
    shopee_food:
      failed_menu_sync:
        header: "Gagal menyikronkan menu ke Shopee Food"
        content: "Gagal menyinkronkan menu ke Shopee Food. Silahkan menyinkronkan ulang %{location_name}"
      failed_promo_sync:
        header: "Gagal membuat promosi ke Shopee Food"
        content: "Gagal membuat promosi %{promo_name} ke Shopee Food. Silahkan, buat promo baru di %{location_name}"
      failed_order_sync:
        header: "Gagal membuat order Shopee Food di system Runchise"
        content: "Gagal membuat order Shopee Food %{order_id} di system runchise. Mohon cek Shopee Food app di %{location_name}"
    royalty_transaction:
      creation_failed:
        header: "Gagal membuat skema royalti"
        content: "Tidak dapat membuat skema royalti"
      creation_completed:
        header: "Seluruh skema royalti telah berhasil dibuat."
        content: "Seluruh skema royalti telah berhasil dibuat."
    delivery_return:
      create:
        header: "Pengembalian Masuk"
        content: "Pengembalian %{return_no} %{location_action}"
      create_fulfillment:
        content: "Pengembalian %{return_no} dari pengiriman %{deliveries_no} %{location_action}"
      rejected:
        header: "Permohonan ditolak"
        content: "Permohonan pengembalian barang %{return_no} telah ditolak."
    device:
      request_sync:
        title: "Pengaturan POS baru saja diubah."
        body: "Harap melakukan sinkronisasi ulang untuk mendapatkan pembaharuan sistem. Tolong pastikan POS sedang dalam mode diam."
    bulk_update_internal_price:
      completed:
        single_location_id: "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga."
        multiple_location_ids: "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga."
  notification_setting:
    when_app_notif_incoming_order_off_email_must_be_off: "Jika app notif order masuk nonaktif, email harus nonaktif"
    when_app_notif_order_approve_void_off_email_must_be_off: "Jika app notif order approve void nonaktif, email harus nonaktif"
    when_app_notif_report_par_off_email_must_be_off: "Jika app notif report par nonaktif, email harus nonaktif"
