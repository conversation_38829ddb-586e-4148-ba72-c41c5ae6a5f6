en:
  notification:
    title: "Notification"
    all_up_to_date: "all up to date!"
    new_feeds_count: "%{count} new feeds"
    patch_read_error: "Failed to read notification"
    empty_notification: "You have no notification"
    fetch_notifications_error: "Failed to fetch list of notifications"
    fetch_active_notification_error: "Failed to update notifications list"
    mark_all: "Mark read all"
    import_data:
      product: "Import product is done, %{count}/%{total_count} has been created."
      customer: "Import customer is done, %{count}/%{total_count} has been created."
      customer_point: "Import customer point is done, %{count}/%{total_count} has been updated."
      daily_sale: "Import sales %{date} is done"
      daily_sale_failed: "Import sales %{date} has failed"
      order_transaction:
        create: "Import order %{date} is done"
        failed: "Import order %{date} has failed"
      stock_adjustment: "Import stock take at %{location_name} done with status %{status}"
      stock_opening:
        create: "Import stock opening at %{location_name} done with status %{status}"
        failed: "failed with reason: Import stock opening have errors"
      recipe_storage_sections: "Import recipe storage sections at %{location_name} done"
      recipe_storage_sections_with_error: "Import recipe storage sections at %{location_name} have errors"
      money_movement: "Import money movements done"
      money_movement_with_error: "Import money movements have errors"
      recipe: "Successfully imported all recipes"
      recipe_with_error: "Import recipes has failed"
      stock_in: "Successfully imported all stock in"
      stock_in_with_error: "Import stock in has failed"
      stock_out: "Successfully imported all stock out"
      stock_out_with_error: "Import stock out has failed"
    order_transaction:
      create: "New order %{order_no} from %{location_name}"
      update: "Order %{order_no} from %{location_name} has been updated"
      update_shipping_fee: "Shipping fee's order %{order_no}, %{location_name} changed successfully"
      update_prices: "Order %{order_no}: Price has been updated."
      remind_payment: "Reminder Payment for %{order_no}, %{location_name}, to proceed the delivery goods"
      remind_shipping_fee_payment: "Remind shipping fee payment for %{order_no} has to be paid"
      added_shipping_fee: "%{order_no} shipping fee has been added"
      paid_shipping_fee: "%{order_no} shipping fee has been paid"
      paid: "Payment successful for %{order_no} to %{location_name}"
      buyer_paid: "Payment successful for %{order_no} to %{location_name}"
      remind_limit: "Reminder Payment for %{order_no}, %{location_name} already exceeds the limit. please try again tomorrow"
      approve: "Order %{order_no} from %{location_from_name} to %{location_to_name} has been approved."
      void: "Order %{order_no} has been voided."
      close: "Order %{order_no} to %{location_name} has been closed"
      manual_refund: "Transaction %{order_no} has been paid and is voided. Please settle refund payment manually."
      order_approval_reminder: "There's a new order %{order_no} to %{location_name} awaiting for your approval."
      autovoid_reminder: "%{order_no} will expire in %{minutes} minutes. Complete payment now to avoid cancellation."
      autovoid_completion_alert: "%{order_no} is voided at %{autovoided_at} due to unpaid status after %{minutes} minutes."
    delivery:
      create: "Delivery %{delivery_no} to %{location_name} has been shipped"
      update: "Delivery %{delivery_no} to %{location_name} has been updated"
      receive: "Delivery %{delivery_no} from %{location_name} has been received"
      incomplete: "Delivery %{delivery_no} from %{location_name} has been received but incomplete"
      destroy: "Delivery %{delivery_no} to %{location_name} has been deleted"
    incoming_delivery:
      receive: "Delivery %{delivery_no} from %{location_from_name} has been received"
    delivery_return:
      create:
        header: "Incoming Return"
        content: "Return %{return_no} %{location_action}"
        send_from_location: "from %{location_name} has been submitted"
        send_to_vendor: "to %{vendor_name} has been sent"
      create_fulfillment:
        content: "Return %{return_no} from delivery %{deliveries_no} %{location_action}"
      rejected:
        header: "Return Rejected"
        content: "Return request %{return_no} has been rejected"
    stock:
      par: "You have %{count} products below required quantity. Please review!"
      stock_transfer: "Stock transfer %{id} from %{location_from_name} to %{location_to_name} has been created."
    taking:
      create: "Outlet %{location_name} has finished sales taking."
    grab_food:
      failed_menu_sync: "Failed Sync Menu to Grab Food. Retry sync menu again %{location_name}"
      failed_promo_sync: "Failed create promotion %{promo_name} to Grab Food. Please, create new promo again in %{location_name}"
      failed_order_sync: "Failed to create Grab Food order %{order_id} on Runchise. Please, check your Grab Food App in %{location_name}"
    go_food:
      failed_menu_sync: "Failed Sync Menu to Go Food. Retry sync menu again %{location_name}"
      failed_promo_sync: "Failed create promotion %{promo_name} to Go Food. Please, create new promo again in %{location_name}"
      failed_order_sync: "Failed to create Go Food order %{order_id} on Runchise. Please, check your Go Food App in %{location_name}"
    shopee_food:
      failed_menu_sync: "Failed Sync Menu to Shopee Food. Retry sync menu again %{location_name}"
      failed_promo_sync: "Failed create promotion %{promo_name} to Shopee Food. Please, create new promo again in %{location_name}"
      failed_order_sync: "Failed to create Shopee Food order %{order_id} on Runchise. Please, check your Shopee Food App in %{location_name}"
    royalty_transaction:
      creation_failed: "Run royalty scheme failed."
      creation_completed: "All royalty schemas has been run successfully."
    bulk_product_activation:
      activation_success: "%{success_count} items have been successfully activated"
      activation_failed: "%{success_count} items have been successfully activated, %{failed_count} items failed to be activated"
      deactivation_success: "%{success_count} items have been successfully deactivated"
      deactivation_failed: "%{success_count} items have been successfully deactivated, %{failed_count} items failed to be deactivated"
  push_notif:
    import_data:
      product:
        header: "Import product is done"
        content: "%{count}/%{total_count} has been created."
      customer:
        header: "Import customer is done"
        content: "%{count}/%{total_count} has been created."
      customer_point:
        header: "Import customer point is done"
        content: "%{count}/%{total_count} has been updated."
      stock_adjustment:
        header: "Import stock take at %{location_name} %{status}"
        content_success: "%{count} products has been recorded"
        content_failure: "reason: %{reason}"
      stock_opening:
        header: "Import stock opening at %{location_name} %{status}"
        content_success: "%{count} products has been recorded"
        content_failure: "reason: %{reason}"
      daily_sale:
        header: "Import sales is done"
        content: "Sales for %{date} has been created."
      daily_sale_failed:
        header: "Import sales has failed"
        content: "Let's check summary to fix what went wrong."
      order_transaction:
        header: "Import order is done"
        content: "Order for %{date} has been created."
      order_transaction_failed:
        header: "Import order has failed"
        content: "Let's check summary to fix what went wrong."
      recipe_storage_sections:
        header: "Import recipe storage sections at %{location_name} done"
        content: "%{count}/%{total_count} recipes updated"
      recipe_storage_sections_with_error:
        header: "Import recipe storage sections at %{location_name} have errors"
        content: "%{count}/%{total_count} recipes updated, let's check summary to fix what went wrong"
      money_movement:
        header: "Import money movements done"
        content: "%{count}/%{total_count} money movements updated"
      money_movement_with_error:
        header: "Import money movements have errors"
        content: "%{count}/%{total_count} money movements updated, let's check summary to fix what went wrong"
      recipe:
        header: "Successfully imported all recipes"
        content: "%{count}/%{total_count} has been created."
      recipe_with_error:
        header: "Recipe Import Failed"
        content: "All recipes failed to import. Please review the errors below."
      stock_in:
        header: "Import stock in done"
        content: "%{count}/%{total_count} stock in updated"
      stock_in_with_error:
        header: "Import stock in have errors"
        content: "%{count}/%{total_count} stock in updated, let's check summary to fix what went wrong"
      stock_out:
        header: "Import stock out done"
        content: "%{count}/%{total_count} stock out updated"
      stock_out_with_error:
        header: "Import stock out have errors"
        content: "%{count}/%{total_count} stock out updated, let's check summary to fix what went wrong"
    order_transaction:
      autovoid_completion_alert:
        header: "Time's Up! Order is voided!"
        content: "%{order_no} is voided at %{autovoided_at} due to unpaid status after %{minutes} minutes."
      autovoid_reminder:
        header: "Order Expiring Soon – Pay Now!"
        content: "%{order_no} will expire in %{minutes} minutes. Complete payment now to avoid cancellation."
      create:
        header: "New order coming!"
        content: "Order %{order_no} from %{location_name}."
      update:
        header: "An order gets updated"
        content: "Order %{order_no} from %{location_name} has been updated."
      update_shipping_fee:
        header: "Order's shipping fee gets updated"
        content: "Shipping fee's order %{order_no}, %{location_name} has been updated."
      update_prices:
        header: "Order %{order_no}: Price has been updated."
        content: "Order %{order_no}: Price has been updated."
      remind_payment:
        header: "Reminder Payment for %{order_no}, %{location_name}"
        content: "Reminder Payment for %{order_no}, %{location_name} to proceed the delivery goods."
      remind_shipping_fee_payment:
        header: "Reminder shipping fee payment for %{order_no}"
        content: "Reminder shipping fee payment for %{order_no} has to be paid."
      added_shipping_fee:
        header: "%{order_no} shipping fee has been added"
        content: "%{order_no} shipping fee has been added"
      paid_shipping_fee:
        header: "%{order_no} shipping fee has been paid"
        content: "%{order_no} shipping fee has been paid"
      paid:
        header: "Payment successful for %{order_no} to %{location_name}"
        content: "Payment successful for %{order_no} to %{location_name}, proceed the order."
      buyer_paid:
        header: "Payment successful for %{order_no} to %{location_name}"
        content: "Payment successful for %{order_no} to %{location_name}, the goods will be shipping."
      remind_limit:
        header: "Reminder Payment for %{order_no}, %{location_name} has reached daily limit"
        content: "Reminder Payment for %{order_no}, %{location_name} has reached daily limit."
      approve:
        header: "An order gets approved"
        content: "Order %{order_no} from %{location_from_name} to %{location_to_name} has been approved."
      void:
        header: "An order has been voided"
        content: "Order %{order_no} has been voided."
      close:
        header: "An order has been closed by user"
        content: "Order %{order_no} to %{location_name} has been closed by user."
      manual_refund:
        header: "Voided paid transaction"
        content: "Transaction %{order_no} has been paid and is voided. Please settle refund payment manually."
      order_approval_reminder:
        header: "An order is waiting for your approval"
        content: "There's a new order %{order_no} to %{location_name} awaiting for your approval."
    delivery:
      create:
        header: "Delivery is getting shipped"
        content: "Delivery %{delivery_no} to %{location_name} has been shipped."
      update:
        header: "Delivery has been updated "
        content: "Delivery %{delivery_no} to %{location_name} has been updated."
      receive:
        header: "Delivery has been received"
        content: "Delivery %{delivery_no} from %{location_name} has been received."
      incomplete:
        header: "Incomplete delivery needs action"
        content: "Delivery %{delivery_no} from %{location_name} has been received but incomplete."
      destroy:
        header: "Delivery has been deleted"
        content: "Delivery %{delivery_no} to %{location_name} has been deleted"
    incoming_delivery:
      receive:
        header: "Delivery has been received"
        content: "Delivery %{delivery_no} from %{location_from_name} has been received."
      incomplete:
        header: "Incomplete delivery needs action"
        content: "Delivery %{delivery_no} from %{location_from_name} has been received but incomplete."
    stock:
      par:
        header: "Low stock alert!"
        content: "%{location_name} has %{count} products below required quantity. Please review."
    taking:
      create:
        header: "Closing Time!"
        content: "Outlet %{location_name} has finished sales taking."
    grab_food:
      failed_menu_sync:
        header: "Failed sync Menu to Grab Food"
        content: "Failed Sync Menu to Grab Food. Retry sync menu again %{location_name}"
      failed_promo_sync:
        header: "Failed create promotion to Grab Food"
        content: "Failed create promotion %{promo_name} to Grab Food. Please, create new promo again in %{location_name}"
      failed_order_sync:
        header: "Failed to create Grab Food order on Runchise"
        content: "Failed to create Grab Food order %{order_id} on Runchise. Please, check your Grab Food App in %{location_name}"
    go_food:
      failed_menu_sync:
        header: "Failed sync Menu to Go Food"
        content: "Failed Sync Menu to Go Food. Retry sync menu again %{location_name}"
      failed_promo_sync:
        header: "Failed create promotion to Go Food"
        content: "Failed create promotion %{promo_name} to Go Food. Please, create new promo again in %{location_name}"
      failed_order_sync:
        header: "Failed to create Go Food order on Runchise"
        content: "Failed to create Go Food order %{order_id} on Runchise. Please, check your Go Food App in %{location_name}"
    shopee_food:
      failed_menu_sync:
        header: "Failed sync Menu to Shopee Food"
        content: "Failed Sync Menu to Shopee Food. Retry sync menu again %{location_name}"
      failed_promo_sync:
        header: "Failed create promotion to Shopee Food"
        content: "Failed create promotion %{promo_name} to Shopee Food. Please, create new promo again in %{location_name}"
      failed_order_sync:
        header: "Failed to create Shopee Food order on Runchise"
        content: "Failed to create Shopee Food order %{order_id} on Runchise. Please, check your Shopee Food App in %{location_name}"
    royalty_transaction:
      creation_failed:
        header: "Run royalty scheme failed"
        content: "Unable to run royalty schemes"
      creation_completed:
        header: "All royalty schema has been run successfully."
        content: "All royalty schema has been run successfully."
    delivery_return:
      create:
        header: "Incoming Return"
        content: "Return %{return_no} %{location_action}"
      create_fulfillment:
        content: "Return %{return_no} from delivery %{deliveries_no} %{location_action}"
      rejected:
        header: "Return Rejected"
        content: "Return request %{return_no} has been rejected"
    device:
      request_sync:
        title: "POS Setting has been updated."
        body: "Please re-sync your POS to get the last updated system. Make sure your POS is in idle mode."
    bulk_update_internal_price:
      completed:
        single_location_id: "%{count} order to %{location_to_name} location has a price change."
        multiple_location_ids: "%{count} orders to several locations has a price change."
  notification_setting:
    when_app_notif_incoming_order_off_email_must_be_off: "When app notif incoming order off, email must be off"
    when_app_notif_order_approve_void_off_email_must_be_off: "When app notif order approve void off, email must be off"
    when_app_notif_report_par_off_email_must_be_off: "When app notif report par off, email must be off"
