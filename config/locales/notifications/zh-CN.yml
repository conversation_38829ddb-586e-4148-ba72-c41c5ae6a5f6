zh-CN:
  notification:
    title: "通知"
    all_up_to_date: "全部都是最新的！"
    new_feeds_count: "%{count} 条新动态"
    patch_read_error: "无法读取通知"
    empty_notification: "您尚未收到任何通知"
    fetch_notifications_error: "无法获取通知列表"
    fetch_active_notification_error: "无法更新通知列表"
    mark_all: "标记为已读"
    import_data:
      product: "导入产品已完成，%{count}/%{total_count} 已创建。"
      customer: "导入客户已完成，%{count}/%{total_count} 已创建。"
      customer_point: "导入客户积分已完成，%{count}/%{total_count} 已更新。"
      daily_sale: "导入销售 %{date} 已完成"
      daily_sale_failed: "导入销售 %{date} 失败"
      order_transaction:
        create: "导入订单 %{date} 已完成"
        failed: "导入订单 %{date} 失败"
      stock_adjustment: "%{location_name} 的导入库存盘点已完成，状态为 %{status}"
      stock_opening:
        create: "已导入 %{location_name} 开仓的库存，状态为 %{status}"
        failed: "失败原因：导入库存开仓有错误"
      recipe_storage_sections: "已完成导入 %{location_name} 的配方存储部分"
      recipe_storage_sections_with_error: "%{location_name} 处的导入配方存储部分出现错误"
      money_movement: "进口资金转移完成"
      money_movement_with_error: "进口资金转移有错误"
      recipe: "成功导入所有食谱"
      recipe_with_error: "导入食谱失败"
      stock_in: "所有入库记录已成功导入"
      stock_in_with_error: "导入入库记录失败"
      stock_out: "所有出库记录已成功导入"
      stock_out_with_error: "导入出库记录失败"
    order_transaction:
      create: "来自 %{location_name} 的新订单 %{order_no}"
      update: "来自 %{location_name} 的订单 %{order_no} 已更新"
      update_shipping_fee: "运费的订单 %{order_no}, %{location_name} 修改成功"
      update_prices: "订单 %{order_no}：价格已更新。"
      remind_payment: "提醒您支付 %{order_no}, %{location_name} 的费用，以便继续发货"
      remind_shipping_fee_payment: "提醒必须支付 %{order_no} 的运费"
      added_shipping_fee: "已添加 %{order_no} 运费"
      paid_shipping_fee: "%{order_no} 运费已支付"
      paid: "%{order_no} 付款至 %{location_name} 已成功"
      buyer_paid: "%{order_no} 付款至 %{location_name} 已成功"
      remind_limit: "提醒 %{order_no}、%{location_name} 的付款已超出限制。请明天再试"
      approve: "发往 %{location_name} 的订单 %{order_no} 已获批准"
      void: "发往 %{location_name} 的订单 %{order_no} 已取消"
      close: "发往 %{location_name} 的订单 %{order_no} 已关闭"
      manual_refund: "交易 %{order_no} 已付款并已作废。请手动结算退款。"
      autovoid_reminder: "您的订单 %{order_no} 将在 {minutes} 分钟后过期。请立即完成支付，以免订单被取消。"
      autovoid_completion_alert: "%{order_no}由于超过%{minutes}分钟未支付，已于%{autovoided_at}作废。"
    delivery:
      create: "发往 %{location_name} 的包裹 %{delivery_no} 已发货"
      update: "配送至 %{location_name} 的配送 %{delivery_no} 已更新"
      receive: "已收到来自 %{location_name} 的送货 %{delivery_no}"
      incomplete: "来自 %{location_name} 的送货 %{delivery_no} 已收到，但尚未完成"
      destroy: "发往 %{location_name} 的配送 %{delivery_no} 已删除"
    incoming_delivery:
      receive: "已收到来自 %{location_from_name} 的送货 %{delivery_no}"
    delivery_return:
      create:
        header: "传入返回"
        content: "返回 %{return_no} %{location_action}"
        send_from_location: "来自 %{location_name} 已提交"
        send_to_vendor: "已发送至 %{vendor_name}"
      create_fulfillment:
        content: "从配送地点 %{deliveries_no} %{location_action} 返回 %{return_no}"
      rejected:
        header: "退货被拒绝"
        content: "退货请求 %{return_no} 已被拒绝"
    stock:
      par: "您有 %{count} 件商品未达到所需数量。请审核！"
      stock_transfer: "已创建从 %{location_from_name} 到 %{location_to_name} 的库存转移 %{id}。"
    taking:
      create: "门店 %{location_name} 已结束销售。"
    grab_food:
      failed_menu_sync: "同步菜单到 Grab Food 失败。请重试同步菜单 %{location_name}"
      failed_promo_sync: "无法创建 Grab Food 促销活动 %{promo_name}。请在 %{location_name} 重新创建新促销活动"
      failed_order_sync: "无法在 Runchise 上创建 Grab Food 订单 %{order_id}。请检查 %{location_name} 中的 Grab Food 应用"
    go_food:
      failed_menu_sync: "无法将菜单同步至 Go Food。请重试同步菜单 %{location_name}"
      failed_promo_sync: "无法为 Go Food 创建促销活动 %{promo_name}。请在 %{location_name} 重新创建新的促销活动"
      failed_order_sync: "无法在 Runchise 上创建 Go Food 订单 %{order_id}。请在 %{location_name} 检查您的 Go Food 应用"
    shopee_food:
      failed_menu_sync: "无法将菜单同步至 Shopee Food。请重试同步菜单 %{location_name}"
      failed_promo_sync: "无法为 Shopee Food 创建促销活动 %{promo_name}。请在 %{location_name} 重新创建新的促销活动"
      failed_order_sync: "无法在 Runchise 上创建 Shopee Food 订单 %{order_id}。请在 %{location_name} 检查您的 Shopee Food App"
    royalty_transaction:
      creation_failed: "运行版税计划失败。"
      creation_completed: "所有版税方案均已成功运行。"
    bulk_product_activation:
      activation_success: "%{success_count} 件商品已成功激活"
      activation_failed: "%{success_count} 件商品已成功激活，%{failed_count} 件商品激活失败"
      deactivation_success: "%{success_count} 个商品已成功停用"
      deactivation_failed: "%{success_count} 个项目已成功停用，%{failed_count} 个项目停用失败"
  push_notif:
    import_data:
      product:
        header: "导入产品已完成"
        content: "%{count}/%{total_count} 已创建。"
      customer:
        header: "导入客户已完成"
        content: "%{count}/%{total_count} 已创建。"
      customer_point:
        header: "导入客户点已完成"
        content: "%{count}/%{total_count} 已更新。"
      stock_adjustment:
        header: "在 %{location_name} %{status} 进行进口库存盘点"
        content_success: "已记录 %{count} 个产品"
        content_failure: "原因： %{reason}"
      stock_opening:
        header: "导入在 %{location_name} %{status} 开盘的库存"
        content_success: "已记录 %{count} 个产品"
        content_failure: "原因： %{reason}"
      daily_sale:
        header: "进口销售已完成"
        content: "已创建 %{date} 的销售。"
      daily_sale_failed:
        header: "进口销售失败"
        content: "让我们检查一下摘要来修复错误。"
      order_transaction:
        header: "导入订单已完成"
        content: "%{date} 的订单已创建。"
      order_transaction_failed:
        header: "导入订单失败"
        content: "让我们检查一下摘要来修复错误。"
      recipe_storage_sections:
        header: "已完成导入 %{location_name} 的配方存储部分"
        content: "%{count}/%{total_count} 个食谱已更新"
      recipe_storage_sections_with_error:
        header: "%{location_name} 处的导入配方存储部分出现错误"
        content: "%{count}/%{total_count} 个菜谱已更新，让我们检查摘要以修复错误"
      money_movement:
        header: "Impor pergerakan uang telah selesai"
        content: "%{count}/%{total_count} pergerakan uang telah diperbarui"
      money_movement_with_error:
        header: "Impor pergerakan uang mengalami kesalahan"
        content: "%{count}/%{total_count} pergerakan uang telah diperbarui, mari kita periksa ringkasan untuk memperbaiki kesalahannya"
      recipe:
        header: "成功导入所有食谱"
        content: "已创建 %{count}/%{total_count}。"
      recipe_with_error:
        header: "食谱导入失败"
        content: "所有食谱导入失败。请查看以下错误。"
      stock_in:
        header: "入库导入完成"
        content: "%{count}/%{total_count} 条入库记录已更新"
      stock_in_with_error:
        header: "入库导入出现错误"
        content: "%{count}/%{total_count} 条入库记录已更新，请查看摘要以修复错误"
      stock_out:
        header: "出库导入完成"
        content: "%{count}/%{total_count} 条出库记录已更新"
      stock_out_with_error:
        header: "出库导入出现错误"
        content: "%{count}/%{total_count} 条出库记录已更新，请查看摘要以修复错误"
    order_transaction:
      autovoid_completion_alert:
        header: "时间到！订单已作废！"
        content: "%{order_no}由于超过%{minutes}分钟未支付，已于%{autovoided_at}作废。"
      autovoid_reminder:
        header: "订单即将过期，请立即支付！"
        content: "您的订单%{order_no}将在%{minutes}分钟后过期。请立即完成支付，以免订单被取消。"
      create:
        header: "新订单来了！"
        content: "从 %{location_name} 订购 %{order_no}。"
      update:
        header: "订单已更新"
        content: "来自 %{location_name} 的订单 %{order_no} 已更新。"
      update_shipping_fee:
        header: "订单的运费已更新"
        content: "运费的订单 %{order_no}, %{location_name} 已更新。"
      update_prices:
        header: "订单 %{order_no}：价格已更新。"
        content: "订单 %{order_no}：价格已更新。"
      remind_payment:
        header: "提醒 %{order_no}、%{location_name} 付款"
        content: "提醒您支付 %{order_no}、%{location_name} 的费用以进行货物配送。"
      remind_shipping_fee_payment:
        header: "提醒支付 %{order_no} 的运费"
        content: "提醒您需支付 %{order_no} 的运费。"
      added_shipping_fee:
        header: "已添加 %{order_no} 运费"
        content: "已添加 %{order_no} 运费"
      paid_shipping_fee:
        header: "%{order_no} 运费已支付"
        content: "%{order_no} 运费已支付"
      paid:
        header: "%{order_no} 付款至 %{location_name} 已成功"
        content: "%{order_no} 付款成功至 %{location_name}，继续订单。"
      buyer_paid:
        header: "%{order_no} 付款至 %{location_name} 已成功"
        content: "%{order_no} 已成功支付至 %{location_name}，商品即将发货。"
      remind_limit:
        header: "提醒 %{order_no}、%{location_name} 的付款已达到每日限额"
        content: "提醒 %{order_no}、%{location_name} 的付款已达到每日限额。"
      approve:
        header: "订单获得批准"
        content: "发往 %{location_name} 的订单 %{order_no} 已获批准。"
      void:
        header: "订单已作废"
        content: "发往 %{location_name} 的订单 %{order_no} 已取消。"
      close:
        header: "用户已关闭订单"
        content: "发往 %{location_name} 的订单 %{order_no} 已被用户关闭。"
      manual_refund:
        header: "已付款交易已作废"
        content: "交易 %{order_no} 已付款并已作废。请手动结算退款。"
    delivery:
      create:
        header: "货物正在发货"
        content: "发往 %{location_name} 的包裹 %{delivery_no} 已发货。"
      update:
        header: "配送已更新 "
        content: "发往 %{location_name} 的送货 %{delivery_no} 已更新。"
      receive:
        header: "已收到货"
        content: "已收到来自 %{location_name} 的快递 %{delivery_no}。"
      incomplete:
        header: "交付不完整需要采取行动"
        content: "来自 %{location_name} 的快递 %{delivery_no} 已收到，但尚未完成。"
      destroy:
        header: "发货已删除"
        content: "发往 %{location_name} 的配送 %{delivery_no} 已删除"
    incoming_delivery:
      receive:
        header: "已收到货"
        content: "已收到来自 %{location_from_name} 的快递 %{delivery_no}。"
      incomplete:
        header: "交付不完整需要采取行动"
        content: "来自 %{location_from_name} 的快递 %{delivery_no} 已收到，但尚未完成。"
    stock:
      par:
        header: "库存不足警报！"
        content: "%{location_name} 有 %{count} 件商品，数量低于要求。请审核。"
    taking:
      create:
        header: "關閉時間了！"
        content: "门店 %{location_name} 已结束销售。"
    grab_food:
      failed_menu_sync:
        header: "同步菜单至 Grab Food 失败"
        content: "同步菜单到 Grab Food 失败。请重试同步菜单 %{location_name}"
      failed_promo_sync:
        header: "无法创建 Grab Food 促销活动"
        content: "无法创建 Grab Food 促销活动 %{promo_name}。请在 %{location_name} 重新创建新促销活动"
      failed_order_sync:
        header: "无法在 Runchise 上创建 Grab Food 订单"
        content: "无法在 Runchise 上创建 Grab Food 订单 %{order_id}。请检查 %{location_name} 中的 Grab Food 应用"
    go_food:
      failed_menu_sync:
        header: "无法将菜单同步至 Go Food"
        content: "无法将菜单同步至 Go Food。请重试同步菜单 %{location_name}"
      failed_promo_sync:
        header: "无法创建针对 Go Food 的促销活动"
        content: "无法为 Go Food 创建促销活动 %{promo_name}。请在 %{location_name} 重新创建新的促销活动"
      failed_order_sync:
        header: "无法在 Runchise 上创建 Go Food 订单"
        content: "无法在 Runchise 上创建 Go Food 订单 %{order_id}。请在 %{location_name} 检查您的 Go Food 应用"
    shopee_food:
      failed_menu_sync:
        header: "无法将菜单同步至 Shopee Food"
        content: "无法将菜单同步至 Shopee Food。请重试同步菜单 %{location_name}"
      failed_promo_sync:
        header: "无法创建针对 Shopee Food 的促销活动"
        content: "无法为 Shopee Food 创建促销活动 %{promo_name}。请在 %{location_name} 重新创建新的促销活动"
      failed_order_sync:
        header: "无法在 Runchise 上创建 Shopee Food 订单"
        content: "无法在 Runchise 上创建 Shopee Food 订单 %{order_id}。请在 %{location_name} 检查您的 Shopee Food App"
    royalty_transaction:
      creation_failed:
        header: "运行特许权使用费计划失败"
        content: "无法实施特许权使用费计划"
      creation_completed:
        header: "所有版税方案均已成功运行。"
        content: "所有版税方案均已成功运行。"
    delivery_return:
      create:
        header: "传入返回"
        content: "返回 %{return_no} %{location_action}"
      create_fulfillment:
        content: "从配送地点 %{deliveries_no} %{location_action} 返回 %{return_no}"
      rejected:
        header: "退货被拒绝"
        content: "退货请求 %{return_no} 已被拒绝"
    device:
      request_sync:
        title: "POS 设置已更新。"
        body: "请重新同步您的 POS 以获取最新更新的系统。确保您的 POS 处于空闲模式。"
    bulk_update_internal_price:
      completed:
        single_location_id: "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。"
        multiple_location_ids: "发往多个地点的 %{count} 个订单的价格发生了变化。"
  notification_setting:
    when_app_notif_incoming_order_off_email_must_be_off: "当应用程序关闭通知订单到货时，电子邮件必须关闭"
    when_app_notif_order_approve_void_off_email_must_be_off: "当应用程序通知订单批准无效时，电子邮件必须关闭"
    when_app_notif_report_par_off_email_must_be_off: "当应用程序通知报告关闭时，电子邮件必须关闭"
