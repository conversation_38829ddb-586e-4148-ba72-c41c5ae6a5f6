en:
  products:
    title: "Product"
    product_name: "Product Name"
    name: "Name"
    sku: "SKU"
    category: "Category"
    stock_qty: "Stock Qty"
    unit: "Unit"
    description: "Description"
    location: "Location"
    locations: "Location"
    exclude_locations: "Exclude Location"
    edit_product: "Edit Product"
    info: "Info"
    recent_orders: "Recent Orders"
    create_product_success: "Success created a product"
    create_product_error: "Failed to create a product"
    update_product_success: "Successfully update a product"
    update_product_error: "Failed to update a product"
    activated: "Active"
    deactivated: "Inactive"
    manage_product_units: "Manage Units"
    manage_product_categories: "Manage Categories"
    manage_product_stocks: "Manage Stocks"
    manage_product_expiries: "Manage Expiry"
    manage_product_recipes: "Manage Recipes"
    manage_option_sets: "Option Sets"
    fetch_all_error: "Failed fetch list of products"
    fetch_detail_error: "Failed to fetch product detail"
    deactivate_product_success: "Successfully deactivate product"
    deactivate_product_error: "Failed to deactivate product"
    reactivate_product_success: "Successfully reactivate product"
    reactivate_product_error: "Failed to reactivate product"
    delete_product_success: "Successfully delete product"
    delete_product_error: "Failed to delete product"
    categories: "Categories"
    import_products: "Import Products"
    fetch_import_success: "Import products is being processed."
    fetch_import_error: "Failed to import products"
    import_products_title_1: "1. Download .csv template"
    import_products_desc_1: "Make sure to use the format that we have provided to ensure data will be read correctly."
    import_products_title_2: "2. Open file to populate data"
    import_products_desc_2: "Open downloaded file using spreadsheet application such as Excel or GoogleSheet."
    import_products_title_3: "3. Save in .csv format"
    import_products_desc_3: "Be sure to save it in .csv format, using separator of comma (,). "
    import_products_title_4: "4. Upload file here"
    fetch_recent_orders_error: "Failed to fetch list of recent orders"
    back_office_unit: "Back Office Unit"
    back_office_unit_hint: "Used as default unit when displaying stocks"
    sell_unit: "Sell Unit"
    sell_unit_hint: "Used as default unit for things related to POS"
    par_level: "PAR level"
    par_level_placeholder: "000,000"
    par_level_hint: "Quantity for each product that should be on hand at all times. A general formula for estimating PAR level = (weekly inventory use + Safety stock) / Deliveries per week"
    unit_and_price: "Unit & Price"
    upc: "UPC"
    upc_or_barcode: "UPC / Barcode"
    smallest_unit: "Smallest Unit"
    type_internal_distribution: "Internal Distribution"
    type_external_vendor: "External Vendor"
    type_internal_vendor-product: "Internal Produce"
    type_sell_to_customer: "Sell to Customer"
    all_product_selected: "All Product"
    multiple_product_selected: "(Multiple Product)"
    empty_product_selected: "Select Product(s)"
    expiry_date: "Expiry Date"
    save_product: "Save Product"
    pos: "POS"
    modifier: "Modifier"
    modifier_label: "Product is a modifier"
    modifier_hint: "Option to menu, cannot be purchased alone (must select sell to customer)"
    filter_modifier: "Product Modifier"
    modifier_show: "All"
    modifier_show_only: "Show"
    modifier_hide: "Hide"
    export_menu_success: "Your product menu will be processed and sent to %{email}"
    export_buy_price_success: "Your export buy price request will be processed and sent to %{email}"
    system_stock: "System stock"
    category_group: "Group Category"
    no_tax: "No Tax"
    default_tax: "Default"
    exclude_tax: "Price exclude tax '%{name}'"
    include_tax: "Price include tax '%{name}'"
    barcode:
      product_image: "Product Image"
      product_name: "Product Name"
      product_sku: "Product SKU"
      product_upc: "Product UPC"
      qr_code: "QR Code"
    zero_stock_type_labels:
      recipe_made_to_order: "recipe made to order"
      is_service: "service"
      parent_variance: "parent variance"
    push_notifications:
      available_with_channels: "%{product_name} is available at %{channels}"
      unavailable_with_channels: "%{product_name} is unavailable at %{channels}"
      available: "%{product_name} is available"
      not_available: "%{product_name} is unavailable"
    channels:
      pos: POS
      online_ordering: Online Ordering
      grab_food: Grab Food
      go_food: Go Food
      shopee_food: Shopee Food
    errors:
      must_sell_to_one_of_platform: "Product must be sell to one of: Dine In, Grab Food, Go Food, Shopee Food, or Online Ordering"
      cant_use_reserved_names: "Can't use reserved product names"
      cant_update_global_brand_product: "Can't update changes on this product"
      cant_use_reserved_skus: "Can't use reserved sku names"
      owner_location_must_exist: "owner_location must exists"
      invalid_push_notif_state: "state is invalid"
      device_not_found: "device_id is invalid"
      must_be_active: "must be active"
      must_be_sell_to_procurement_from_customer: "must be 'Sell to customer - procurement to customer'"
      variance_parent_has_stock: "Variance parent product has stock"
      variance_parent_has_recipe: "Variance parent product has recipe"
      variance_parent_is_deactivated: "Variance parent product is deactivated"
      has_stock: "Unable to deactivate, please ensure the stock is 0"
      cannot_be_parent_variance: "cannot be parent variance"
      sku_has_been_taken: "has already been taken"
      selected_products_available_in_platform: "All selected product must all available in channel: %{channel}"
      block_deactivation_associated_ingredients: "Product %{product_name} can't be deactivated because it is used in the following recipes: %{recipe_names}%{more}"
      block_deactivation_associated_ingredients_more_than_10:  " and many more"
      keyword_length: "Keyword too long (max 100 characters)"
    import:
      name: "Name"
      sku: "SKU"
      description: "Description"
      upc: "UPC"
      product_category_name: "Category"
      owner_location_name: "Belongs to location"
      is_select_all_location: "Available to all location"
      location_type: "Available to all location with type"
      location_names: "Available to location names"
      exclude_location_names: "Not available to location names"
      internal_distribution_type: "Internal Distribution"
      external_vendor_type: "External Vendor"
      internal_produce_type: "Internal Produce"
      sell_to_customer_type: "Sell to Customer"
      sell_to_pos: "Sell to Customer POS"
      sell_to_kiosk: "Sell to Kiosk"
      sell_to_dine_in: "Sell to Customer Dine In"
      sell_to_grab_food: "Sell to Customer Grab Food"
      sell_to_go_food: "Sell to Customer Go Food"
      sell_to_shopee_food: "Sell to Customer Shopee"
      sell_to_online_ordering: "Sell to Customer Online Ordering"
      par_quantity: "PAR Qty"
      par_unit_name: "PAR Unit"
      product_unit_name: "Smallest Unit"
      back_office_unit_name: "Back Office Unit"
      procurement_unit_names: "Procurement Unit"
      sell_unit_name: "Sell Unit"
      modifier: "Modifier"
      tax_name: "Sell Tax"
      sell_price: "Sell Price"
      sell_tax_setting: "Tax Nature"
      internal_price: "Internal Price"
      allow_custom_sell_price: "Allow custom price"
      internal_tax_name: "Internal Tax"
      no_stock: "Layanan"
      image_url: "Image URL"
      storage_section: "Storage Section"
