id:
  products:
    title: "Produk"
    product_name: "Nama Produk"
    name: "<PERSON><PERSON>"
    sku: "SKU"
    category: "Kategori"
    stock_qty: "Stok Qty"
    unit: "Unit"
    location: "Lokasi"
    locations: "Lokasi"
    exclude_locations: "Kecuali Lokasi"
    description: "Deskripsi"
    edit_product: "Ubah Produk"
    info: "Informasi"
    recent_orders: "Pesanan Terbaru"
    create_product_success: "Sukses membuat sebuah produk"
    create_product_error: "Gagal membuat sebuah produk"
    update_product_success: "Sukses mengubah sebuah produk"
    update_product_error: "Gagal mengubah sebuah produk"
    activated: "Aktif"
    deactivated: "Tidak Aktif"
    manage_product_units: "Atur Unit"
    manage_product_categories: "Atur Kategori"
    manage_product_stocks: "Atur Stok"
    manage_product_expiries: "Atur Kadaluarsa"
    manage_product_recipes: "Atur Resep"
    manage_option_sets: "Pilihan Set"
    fetch_all_error: "Gagal mendapatkan daftar produk"
    fetch_detail_error: "Gagal mendapatkan detail produk"
    deactivate_product_success: "Sukses menon-aktifkan produk"
    deactivate_product_error: "Gagal menon-aktifkan produk"
    reactivate_product_success: "Sukses meng-aktifkan produk"
    reactivate_product_error: "Gagal meng-aktifkan produk"
    delete_product_success: "Sukses menghapus produk"
    delete_product_error: "Gagal menghapus produk"
    categories: "Kategori"
    import_products: "Impor Produk"
    fetch_import_success: "Impor produk sedang diproses"
    fetch_import_error: "Terjadi kesalahan dalam impor produk"
    import_products_title_1: "1. Unduh contoh .csv"
    import_products_desc_1: "Pastikan untuk menggunakan format yang sudah kita sediakan agar data dapat dibaca dengan benar."
    import_products_title_2: "2. Buka file dan isi"
    import_products_desc_2: "Buka file tersebut dengan aplikasi sesuai seperti Excel atau GoogleSheet"
    import_products_title_3: "3. Simpan dalam format .csv"
    import_products_desc_3: "Pastikan untuk menyimpan dalam format .csv, dengan separator koma (,)."
    import_products_title_4: "4. Unggah file disini"
    fetch_recent_orders_error: "Gagal mendapatkan daftar orderan terbaru"
    back_office_unit: "Back Office Unit"
    back_office_unit_hint: "Digunakan sebagai unit default untuk menampilkan jumlah stok"
    sell_unit: "Sell Unit"
    sell_unit_hint: "Digunakan sebagai unit default untuk hal yang berhubungan dengan POS"
    par_level: "PAR level"
    par_level_placeholder: "000,000"
    par_level_hint: "Kuantitas untuk setiap produk yang harus tersedia setiap saat. Rumus umum untuk memperkirakan tingkat PAR = (penggunaan inventaris mingguan + Stok pengaman) / Pengiriman per minggu"
    unit_and_price: "Unit & Harga"
    upc: "UPC"
    upc_or_barcode: "UPC / Barcode"
    smallest_unit: "Unit Terkecil"
    type_internal_distribution: "Distribusi Internal"
    type_external_vendor: "External Vendor"
    type_internal_vendor-product: "Produksi Internal"
    type_sell_to_customer: "Jual ke Pelanggan"
    all_product_selected: "Semua Produk"
    multiple_product_selected: "(Sebagian Produk)"
    empty_product_selected: "Pilih Produk"
    expiry_date: "Tanggal Kadaluarsa"
    save_product: "Simpan Produk"
    pos: "POS"
    modifier: "Pengubah"
    modifier_label: "Produk adalah pengubah"
    modifier_hint: "Pilihan menu, tidak bisa dibeli sendiri (harus pilih Jual ke Pelanggan)"
    filter_modifier: "Pengubah Produk"
    modifier_show: "Semua"
    modifier_show_only: "Tampilkan"
    modifier_hide: "Sembunyikan"
    export_menu_success: "Menu produk Anda akan diproses dan dikirim ke %{email}"
    export_buy_price_success: "Permintaan ekspor harga beli anda sedang diproses dan dikirim ke %{email}"
    system_stock: "Stok sistem"
    category_group: "Grup Kategori"
    no_tax: "Tanpa Pajak"
    default_tax: "Default"
    exclude_tax: "Harga tidak termasuk pajak '%{name}'"
    include_tax: "Harga termasuk pajak '%{name}'"
    barcode:
      product_image: 'Foto Produk'
      product_name: 'Nama Produk'
      product_sku: 'SKU Produk'
      product_upc: 'UPC Produk'
      qr_code: 'Kode QR'
    zero_stock_type_labels:
      recipe_made_to_order: "resep per pesanan"
      is_service: "layanan"
      parent_variance: "induk varian"
    push_notifications:
      available_with_channels: "%{product_name} tersedia di %{channels}"
      unavailable_with_channels: "%{product_name} tidak tersedia di %{channels}"
      available: "%{product_name} sudah tersedia kembali"
      not_available: "%{product_name} tidak tersedia"
    channels:
      pos: POS
      online_ordering: Pesan Online
      grab_food: Grab Food
      go_food: Go Food
      shopee_food: Shopee Food
    errors:
      must_sell_to_one_of_platform: "Produk harus dijual ke salah satu dari: Dine In, Grab Food, Go Food, Shopee Food, atau Online Ordering"
      cant_use_reserved_names: "Nama produk tidak dapat digunakan"
      cant_update_global_brand_product: "Tidak boleh melakukan perubahan pada produk ini"
      cant_use_reserved_skus: "Sku produk tidak dapat digunakan"
      owner_location_must_exist: "owner_location harus ada"
      invalid_push_notif_state: "state tidak valid"
      device_not_found: "device_id tidak valid"
      must_be_active: 'harus aktif'
      must_be_sell_to_procurement_from_customer: "tipe harus berupa 'Jual ke pelanggan - Pengadaan ke pelanggan'"
      variance_parent_has_stock: "Produk induk varian punya stok"
      variance_parent_has_recipe: "Produk induk varian punya resep"
      variance_parent_is_deactivated: "Produk induk varian tidak aktif"
      has_stock: "Gagal menonaktifkan, pastikan stok berjumlah 0"
      cannot_be_parent_variance: 'tak boleh berupa induk varian'
      sku_has_been_taken: "sudah digunakan"
      selected_products_available_in_platform: "Semua product terpilih harus available pada channel: %{channel}"
      block_deactivation_associated_ingredients: "Produk %{product_name} tidak dapat dinonaktifkan karena digunakan dalam resep berikut: %{recipe_names}%{more}"
      block_deactivation_associated_ingredients_more_than_10: " dan masih banyak lagi"
      keyword_length: "Kata kunci terlalu panjang (maksimal 100 karakter)"
    import:
      name: "Nama"
      sku: "SKU"
      description: "Deskripsi"
      upc: "UPC"
      product_category_name: "Kategori"
      owner_location: "Milik lokasi"
      is_select_all_location: "Tersedia untuk semua lokasi"
      location_type: "Tersedia untuk semua lokasi dengan tipe"
      locations: "Tersedia untuk lokasi dengan nama"
      exclude_locations: "Tidak tersedia untuk lokasi dengan nama"
      internal_distribution_type: "Distribusi Internal"
      external_vendor_type: "External Vendor"
      internal_produce_type: "Produksi Internal"
      sell_to_customer_type: "Jual ke Pelanggan"
      sell_to_pos: "Jual ke Pelanggan POS"
      sell_to_kiosk: "Jual ke Kios"
      sell_to_dine_in: "Jual ke Pelanggan Dine In"
      sell_to_grab_food: "Jual ke Pelanggan Grab Food"
      sell_to_go_food: "Jual ke Pelanggan Go Food"
      sell_to_shopee_food: "Jual ke Pelanggan Shopee"
      sell_to_online_ordering: "Jual ke Pelanggan Online Ordering"
      par_quantity: "PAR Qty"
      par_unit_name: "PAR Unit"
      product_unit_name: "Unit Terkecil"
      back_office_unit_name: "Back Office Unit"
      procurement_unit_names: "Unit Pengadaan"
      sell_unit_name: "Unit Jual"
      modifier: "Pengubah"
      tax_name: "Pajak Jual"
      sell_price: "Harga Jual"
      sell_tax_setting: "Sifat Pajak"
      internal_price: "Harga Internal"
      allow_custom_sell_price: "Izinkan kustom harga"
      internal_tax_name: "Pajak Internal"
      no_stock: "Service"
      image_url: "URL Foto"
      storage_section: "Area Penyimpanan"
