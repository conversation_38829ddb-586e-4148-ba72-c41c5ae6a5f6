zh-CN:
  products:
    title: "产品"
    product_name: "产品名称"
    name: "姓名"
    sku: "库存单位"
    category: "类别"
    stock_qty: "库存数量"
    unit: "单元"
    description: "描述"
    location: "地点"
    locations: "地点"
    exclude_locations: "排除位置"
    edit_product: "編輯產品"
    info: "信息"
    recent_orders: "近期订单"
    create_product_success: "成功创造了产品"
    create_product_error: "未能创建产品"
    update_product_success: "成功更新产品"
    update_product_error: "更新产品失败"
    activated: "积极的"
    deactivated: "不活跃"
    manage_product_units: "管理单位"
    manage_product_categories: "管理类别"
    manage_product_stocks: "管理股票"
    manage_product_expiries: "管理到期"
    manage_product_recipes: "管理食谱"
    manage_option_sets: "选项集"
    fetch_all_error: "获取产品列表失败"
    fetch_detail_error: "无法获取产品详情"
    deactivate_product_success: "成功停用产品"
    deactivate_product_error: "无法停用产品"
    reactivate_product_success: "成功重新激活产品"
    reactivate_product_error: "无法重新激活产品"
    delete_product_success: "成功删除商品"
    delete_product_error: "删除商品失败"
    categories: "类别"
    import_products: "进口产品"
    fetch_import_success: "进口产品正在加工中。"
    fetch_import_error: "导入产品失败"
    import_products_title_1: "1. 下载 .csv 模板"
    import_products_desc_1: "确保使用我们提供的格式以确保正确读取数据。"
    import_products_title_2: "2. 打开文件填充数据"
    import_products_desc_2: "使用电子表格应用程序（例如 Excel 或 GoogleSheet）打开下载的文件。"
    import_products_title_3: "3. 以 .csv 格式保存"
    import_products_desc_3: "确保将其保存为 .csv 格式，并使用逗号 (,) 分隔符。 "
    import_products_title_4: "4. 在此处上传文件"
    fetch_recent_orders_error: "无法获取最近订单列表"
    back_office_unit: "后勤部门"
    back_office_unit_hint: "用作显示股票时的默认单位"
    sell_unit: "出售单位"
    sell_unit_hint: "用作与 POS 相关的默认单位"
    par_level: "平均能效水平"
    par_level_placeholder: "000,000"
    par_level_hint: "每种产品应随时备货的数量。估算 PAR 水平的一般公式 =（每周库存使用量 + 安全库存）/每周交货量"
    unit_and_price: "单位及价格"
    upc: "统一专利证书 (UPC)"
    upc_or_barcode: "UPC/条形码"
    smallest_unit: "最小单位"
    type_internal_distribution: "内部分布"
    type_external_vendor: "外部供应商"
    type_internal_vendor-product: "内部生产"
    type_sell_to_customer: "销售给客户"
    all_product_selected: "全部产品"
    multiple_product_selected: "(多個產品)"
    empty_product_selected: "选择产品"
    expiry_date: "到期日"
    save_product: "保存产品"
    pos: "销售点"
    modifier: "修改器"
    modifier_label: "产品是修饰符"
    modifier_hint: "菜单选项，不能单独购买（必须选择出售给顾客）"
    filter_modifier: "产品修改器"
    modifier_show: "全部"
    modifier_show_only: "展示"
    modifier_hide: "隐藏"
    export_menu_success: "您的产品菜单将被处理并发送至 %{email}"
    export_buy_price_success: "您的出口购买价格请求将被处理并发送至 %{email}"
    system_stock: "系统库存"
    category_group: "团体类别"
    no_tax: "无税"
    default_tax: "默认"
    exclude_tax: "不含税价格 '%{name}'"
    include_tax: "含税价格 '%{name}'"
    barcode:
      product_image: "产品图像"
      product_name: "产品名称"
      product_sku: "产品 SKU"
      product_upc: "产品UPC"
      qr_code: "二维码"
    zero_stock_type_labels:
      recipe_made_to_order: "按订单制作的菜谱"
      is_service: "服务"
      parent_variance: "父母差异"
    push_notifications:
      available_with_channels: "%{product_name} 可在 %{channels} 购买"
      unavailable_with_channels: "%{product_name} 不可用 %{channels} 购买"
      available: "%{product_name} 可用"
      not_available: "%{product_name} 不可用"
    channels:
      pos: POS
      online_ordering: Online Ordering
      grab_food: Grab Food
      go_food: Go Food
      shopee_food: Shopee Food
    errors:
      must_sell_to_one_of_platform: "产品必须销售给以下之一: Dine In、Grab Food、Go Food、Shopee Food 或 Online Ordering"
      cant_use_reserved_names: "不能使用保留的产品名称"
      cant_update_global_brand_product: "无法更新此产品的变更"
      cant_use_reserved_skus: "不能使用保留的 SKU 名称"
      owner_location_must_exist: "Owner_location 必须存在"
      invalid_push_notif_state: "状态无效"
      device_not_found: "device_id 无效"
      must_be_active: "必须活跃"
      must_be_sell_to_procurement_from_customer: "产品类型必须为 “销售给客户–采购到客户”"
      variance_parent_has_stock: "差异父产品有库存"
      variance_parent_has_recipe: "差异父产品有配方"
      variance_parent_is_deactivated: "差异父产品已停用"
      has_stock: "无法停用，请确保库存为 0"
      cannot_be_parent_variance: "不能是父方差"
      sku_has_been_taken: "已有人带走了"
      selected_products_available_in_platform: "所有选定的产品必须全部在以下渠道有售: %{channel}"
      block_deactivation_associated_ingredients: "产品 %{product_name} 无法停用，因为它正在以下配方中被使用：%{recipe_names}%{more}"
      block_deactivation_associated_ingredients_more_than_10: " 还有更多"
      keyword_length: "关键词太长（最多 100 个字符）"
    import:
      name: "姓名"
      sku: "库存单位"
      description: "描述"
      upc: "统一专利证书 (UPC)"
      product_category_name: "类别"
      owner_location: "属于位置"
      is_select_all_location: "适用于所有地点"
      location_type: "适用于所有类型的位置"
      locations: "适用于地点名称"
      exclude_locations: "不适用于地点名称"
      internal_distribution_type: "内部分布"
      external_vendor_type: "外部供应商"
      internal_produce_type: "内部生产"
      sell_to_customer_type: "销售给客户"
      sell_to_pos: "销售给客户 POS"
      sell_to_kiosk: "卖给售货亭"
      sell_to_dine_in: "销售给顾客堂食"
      sell_to_grab_food: "向顾客出售抢购食品"
      sell_to_go_food: "销售给客户 Go Food"
      sell_to_shopee_food: "销售给 Shopee 客户"
      sell_to_online_ordering: "销售给客户在线订购"
      par_quantity: "票面数量"
      par_unit_name: "PAR 单位"
      product_unit_name: "最小单位"
      back_office_unit_name: "后勤部门"
      procurement_unit_names: "采购部门"
      sell_unit_name: "出售单位"
      modifier: "修改器"
      tax_name: "销售税"
      sell_price: "卖价"
      sell_tax_setting: "税务性质"
      internal_price: "内部价格"
      allow_custom_sell_price: "允许自定义价格"
      internal_tax_name: "国内税"
      no_stock: "拉亚南"
      image_url: "图片网址"
      storage_section: "储存部分"
