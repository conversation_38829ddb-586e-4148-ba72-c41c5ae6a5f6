# rubocop:disable Metrics/ClassLength
class ProductQuery
  extend SimpleEnum
  include ParamCommon
  include Restaurant::Modules::Product::CategoryGroupable
  include Restaurant::Modules::Product::QuantityGenerable

  PRODUCT_PRESENTATIONS = build_enum(%i[
                                       ids_only
                                       ids_and_names
                                       product_category_and_procurement_units
                                       simple
                                       sku_and_sell_unit_name
                                       variance
                                       option_set
                                       internal_price_with_product_setting_location
                                       all_locations
                                     ])

  VARIANCE_PRESENTATIONS = build_enum([
                                        :internal_price_units
                                      ])

  def self.query_filtered_order_ids(product_id)
    product = Product.find product_id
    product.order_transactions
           .reorder('order_transactions.order_date DESC, order_transactions.order_no DESC')
           .limit(5)
           .pluck(:id)
  end

  def query_product_category_count
    data = { product_categories: [], no_category_count: 0 }

    products = if Product.exists?
                 Product.search(
                   @keyword.presence || '*',
                   fields: ['name^5', 'sku', 'upc', 'variance_names', 'product_category_name^2', 'product_category_group_name'],
                   where: @conditions,
                   order: @elastic_sort,
                   load: false
                 )
               else
                 []
               end

    product_group = products.group_by(&:product_category_id)
    category_ids = product_group.keys

    data[:no_category_count] = product_group[nil].nil? ? 0 : product_group[nil].size
    data[:no_category_product_ids] = product_group[nil].to_a.pluck(:id)

    filtered_categories = if @elastic_sort.key?(:product_category_name)
                            ProductCategory.order(name: @sort_order).where(id: category_ids)
                          else
                            ProductCategory.where(id: category_ids)
                          end

    filtered_categories.map do |x|
      product_ids = product_group[x.id].map(&:id)

      data[:product_categories] << { id: x.id, name: x.name, count: product_ids.size, product_ids: product_ids }
    end

    return data
  end

  def query_aggregate_product_category_ids
    if Product.exists?
      query = Product.search(
        @keyword.presence || '*',
        fields: ['name^5', 'sku', 'upc', 'variance_names', 'product_category_name^2', 'product_category_group_name'],
        where: @conditions,
        page: @current_page,
        per_page: @item_per_page,
        match: :word_middle,
        order: @elastic_sort,
        includes: include_options,
        misspellings: false,
        # NOTE: This aggs will return id 0 if there's product without product_category_id
        aggs: { product_category_id: { where: @conditions, missing: 0, order: { "_key": 'asc' } } }
      )
      return query.aggs['product_category_id']['buckets'].map { |bucket_key| bucket_key['key'] }
    end

    []
  end

  def self.query_filtered_orders(product_id, filtered_order_ids)
    OrderTransactionLine
      .joins(:order_transaction)
      .where("order_transaction_lines.product_id = #{product_id}")
      .where("order_transactions.id IN(#{filtered_order_ids.join(',')})")
      .group('order_transactions.id, order_transaction_lines.id')
      .reorder('order_transactions.order_date DESC, order_transactions.order_no DESC')
      .select(
        [
          'order_transactions.id as id',
          'order_transactions.order_date as order_date',
          'order_transactions.order_no as order_no',
          'order_transactions.status as status',
          'order_transaction_lines.product_unit_id as order_unit_id',
          'order_transaction_lines.product_unit_conversion_qty as order_conversion_qty',
          'order_transaction_lines.product_qty as order_qty'
        ]
      )
  end

  def self.generate_order_data(base_unit_id, order_details)
    order_unit_ids = order_details.map { |detail| detail['order_unit_id'] }.uniq
    current_order = order_details[0]
    base_product_unit_id = base_unit_id

    product_unit_name = ''
    converted_qty = 0

    if order_unit_ids.length > 1
      product_unit_name = ProductUnit.with_deleted.find(base_product_unit_id).name
      converted_qty = order_details.collect { |detail| detail['order_qty'] * detail['order_conversion_qty'] }.sum
    elsif order_unit_ids.length == 1
      product_unit_name = ProductUnit.with_deleted.find(order_unit_ids[0]).name
      converted_qty = order_details.collect { |detail| detail['order_qty'] }.sum
    end

    return {
      id: current_order['id'],
      order_date: current_order['order_date'].strftime('%d/%m/%Y'),
      order_no: current_order['order_no'],
      qty: converted_qty,
      product_unit_name: product_unit_name,
      order_status: OrderTransaction.statuses.key(current_order['status'])
    }
  end

  def self.find_filtered_products(current_user: nil, product_ids: [], product_includes: [])
    return nil if current_user.blank? || product_ids.blank?

    user_location_ids = current_user.available_locations.where(status: 'activated').map(&:id)
    if product_includes.blank?
      product_includes = [
        { locations_products: :location }
      ]
    end

    Product
      .includes(product_includes)
      .where(id: product_ids, brand: current_user.selected_brand, locations: { id: user_location_ids })
      .or(
        Product
          .includes(product_includes)
          .where(is_select_all_location: true, id: product_ids, brand: current_user.selected_brand)
      )
      .reorder(:name)
  end

  def self.find_recent_orders(product_id: nil)
    data = []
    product = Product.find_by(id: product_id)
    if product.present?
      order_ids = ProductQuery.query_filtered_order_ids(product_id)
      return data if order_ids.empty?

      filtered_orders = ProductQuery.query_filtered_orders(product_id, order_ids)

      filtered_orders.group_by { |order| order['id'] }.each do |_order_id, order_details|
        data << ProductQuery.generate_order_data(product.product_unit_id, order_details)
      end
    end

    data
  end

  def initialize(params = {})
    return if params.blank?

    generate_variable(params)
    generate_policies
    generate_include_id_condition
    generate_exclude_id_condition
    generate_categories_condition
    generate_status_condition
    if @valid_for_role_and_outlet
      generate_outlet_recipe_product_type_condition
    elsif @valid_for_production
      generate_outlet_production_product_type_condition
    else
      generate_product_type_condition
    end
    generate_location_condition
    generate_location_group_ids_condition
    generate_availability_stock_condition
    generate_promo_location_condition
    generate_recipe_condition
    generate_modifier_condition
    generate_variance_condition
    generate_hide_because_parent_deactivated_condition
    generate_exclude_product_with_variances_condition
    generate_recipe_line_condition
    generate_no_stock_condition
    generate_available_conditions
    generate_product_layout
    generate_vendor_products_condition
    generate_course
  end

  # rubocop:disable Metrics/MethodLength
  def filter
    return {} if @conditions.blank?

    products = if Product.exists?
                 if @use_scroll_api
                   scroll_products = []

                   scroll_search = Product.search(@keyword.presence || '*',
                                                  fields: ['name^5', 'sku', 'upc', 'variance_names', 'product_category_name^2',
                                                           'product_category_group_name'],
                                                  where: @conditions,
                                                  per_page: @scroll_size,
                                                  match: :word_middle,
                                                  order: @elastic_sort,
                                                  includes: include_options,
                                                  scroll: '1m')

                   while scroll_search.any?
                     scroll_products += scroll_search.results
                     scroll_search = scroll_search.scroll
                   end

                   scroll_products
                 else
                   Product.search(@keyword.presence || '*',
                                  fields: ['name^5', 'sku', 'upc', 'variance_names', 'product_category_name^2', 'product_category_group_name'],
                                  where: @conditions,
                                  page: @current_page,
                                  per_page: @item_per_page,
                                  match: :word_middle,
                                  order: @elastic_sort,
                                  includes: include_options)
                 end
               else
                 []
               end

    data = []
    generate_products_stock_zero_or_below(products) if @check_empty_stock
    if @group_category
      # NOTES: Temporary filtering the products without category since this can raise 500
      products.select(&:product_category_id).group_by(&:product_category).each do |category, product_list|
        data << generate_categories_response(category, product_list) { |each_product| generate_filter_response(each_product) }
      end
    elsif @export_raw
      data = products
    else
      generate_cant_be_deleted_unit_conversion_ids(products)

      products.each do |product|
        data << generate_filter_response(product, false)
      end
    end

    {
      paging: {
        current_page: @current_page,
        total_item: products.try(:total_count) || 0
      },
      data: data
    }
  end
  # rubocop:enable Metrics/MethodLength

  def generate_available_conditions
    @available_locations = @current_user.available_locations
    @available_location_groups = @current_user.available_location_groups
  end

  def generate_cant_be_deleted_unit_conversion_ids(products)
    return if @presentation == PRODUCT_PRESENTATIONS[:ids_only] || @presentation == PRODUCT_PRESENTATIONS[:ids_and_names]

    product_unit_conversion_ids = products.map { |product| product.product_unit_conversions.map(&:id) }.flatten
    @cant_be_deleted_unit_conversion_ids = ProductUnitConversion
                                           .where(id: product_unit_conversion_ids)
                                           .reject(&:valid_for_destroy?)
                                           .map(&:id)
  end

  def generate_products_stock_zero_or_below(products)
    product_ids = products.map(&:id)
    location_id = procurement_location_id

    open_qty = Restaurant::Services::Report::ProductStock::OpenQuantityHashMapGenerator
               .new(product_ids: product_ids, location_ids: [location_id], brand: @brand)
               .call!

    @products_stock_zero_or_below = {}

    products.each do |product|
      dict_key = "#{product.id}_#{location_id}"
      available_stock = product.stock(location_id: location_id, unit_id: product.product_unit_id) - open_qty[dict_key].to_d

      @products_stock_zero_or_below[product.id] = available_stock <= 0
    end
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def generate_filter_response(product, should_generate_cant_be_deleted_unit_conversions = true)
    generate_cant_be_deleted_unit_conversion_ids([product]) if should_generate_cant_be_deleted_unit_conversions

    result = case @presentation
             when PRODUCT_PRESENTATIONS[:ids_only]
               generate_ids_only(product)
             when PRODUCT_PRESENTATIONS[:ids_and_names]
               gneerate_ids_and_names(product)
             when PRODUCT_PRESENTATIONS[:sku_and_sell_unit_name]
               generate_sku_and_sell_unit_name(product)
             when PRODUCT_PRESENTATIONS[:product_category_and_procurement_units]
               generate_product_category_and_procurement_units(product)
             when PRODUCT_PRESENTATIONS[:simple]
               generate_attribute(product)
             when PRODUCT_PRESENTATIONS[:variance]
               generate_attribute(product).merge(generate_units(product))
                                          .merge(generate_variance(product))
             when PRODUCT_PRESENTATIONS[:option_set]
               generate_attribute(product).merge(generate_units(product))
                                          .merge(generate_option_set(product))
             when PRODUCT_PRESENTATIONS[:internal_price_with_product_setting_location]
               generate_attribute(product).merge(generate_order_price_units(product))
                                          .merge(generate_product_internal_price_locations(product))
             when PRODUCT_PRESENTATIONS[:all_locations]
               generate_product_category_and_locations(product)
             else
               generate_attribute(product).merge(generate_units(product))
                                          .merge(generate_par_quantity(product: product, location_id: @location_id))
                                          .merge(generate_locations(product))
                                          .merge(generate_recipe(product))
                                          .merge(generate_product_group(product))
                                          .merge(generate_variance(product))
                                          .merge(generate_option_set(product))
                                          .merge(generate_product_setting_location(product))
                                          .merge(generate_product_internal_price_locations(product))
                                          .merge(generate_product_price_per_order_types(product))
                                          .merge(generate_internal_price_units(product))
             end

    if @show_pos_position
      result = result.merge(generate_sell_custom_sell_price(product))
                     .merge(generate_pos_position(product))

    end

    result = result.merge(generate_course_result(product)) if @show_detail_course
    result = result.merge(generate_permissions(product)) if @show_permissions
    result = result.merge(version: product.version) if @show_version
    result = result.merge(generate_option_set_recipes(product)) if @include_option_set_recipes

    result
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  private

  def select_brand
    @current_user.selected_brand
  end

  def generate_boolean(param, default)
    (param || default).in?([true, 'true'])
  end

  protected

  # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
  def generate_variable(params)
    @current_user = params[:current_user] || nil
    @brand = select_brand
    @order_types = @brand.order_types
    @filter_by_sub_brand = (params[:filter_by_sub_brand] || 'false').eql?('true')

    @procurement_from_customer = (params[:procurement_from_customer].to_s || 'false').eql?('true')
    @location_id = if @procurement_from_customer
                     params[:location_to_id].to_i
                   else
                     params[:location_id].present? ? params[:location_id].to_i : nil
                   end

    @location_to_id = if @procurement_from_customer
                        nil
                      else
                        params[:location_to_id].present? ? params[:location_to_id].to_i : nil
                      end

    @location_from_ids = multiparams_parse(params[:location_from_ids])
    @exclude_location_from_ids = multiparams_parse(params[:exclude_location_from_ids])
    @all_location_froms = (params[:all_location_froms] || false).eql?('true')
    @location = @location_id.present? ? Location.find_by(id: @location_id) : nil
    @location_to = params[:location_to_id].present? ? Location.find_by(id: params[:location_to_id]) : nil
    @keyword = params[:keyword] || ''
    @status = params[:status]
    @modifier = (params[:modifier] || 'true').eql?('true') if params[:modifier].present?
    @show_version = generate_boolean(params[:show_version], false)
    @include_variance = (params[:include_variance] || 'false').eql?('true')
    @ids = multiparams_parse(params[:ids])
    @exclude_product_with_variances = (params[:exclude_product_with_variances] || 'false').eql?('true')
    @exclude_used_in_recipe_line = (params[:exclude_used_in_recipe_line] || 'false').eql?('true')
    @enable_hide_because_parent_deactivated = (params[:enable_hide_because_parent_deactivated] || 'false').eql?('true')
    @skip_child_variances = if params[:skip_child_variances].blank? && @exclude_product_with_variances
                              false
                            else
                              (params[:skip_child_variances] || 'true').eql?('true')
                            end

    @exclude_ids = multiparams_parse(params[:exclude_ids])
    @category_ids = if params[:category_ids]
                      multiparams_parse(params[:category_ids])
                    else
                      @brand.product_categories.pluck(:id) + [0]
                    end
    @exclude_category_ids = multiparams_parse(params[:exclude_category_ids])
    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @only_contain_recipe = ((params[:only_contain_recipe] || 'false').eql?('true') if params[:only_contain_recipe].present?)
    @group_category = (params[:group_category] || 'false').eql?('true') || params[:group_category] == true

    # product types
    # filter_product_type_behaviour can be AND / OR when filtering product types
    @filter_product_type_behaviour = params[:filter_product_type_behaviour].to_s
    @internal_produce_type = params[:internal_produce_type] == 'true'
    @internal_distribution_type = params[:internal_distribution_type] == 'true'
    @external_vendor_type = params[:external_vendor_type] == 'true' # product itself
    @any_external_vendor_type = params[:any_external_vendor_type] # product itself or any product_setting_locations
    @sell_to_customer_type = params[:sell_to_customer_type] == 'true'
    @allow_procurement_out_of_stock_flag = params[:allow_procurement_out_of_stock_flag] == 'true'
    @vendor_id = params[:vendor_id] if @any_external_vendor_type

    # promo locations
    @promo_location_to_ids = multiparams_parse(params[:promo_location_to_ids])
    @promo_is_select_all_location_to_ids = params[:promo_is_select_all_location_to_ids] == 'true'
    @promo_location_to_type = params[:promo_location_to_type]

    # sell to types
    @sell_to_pos = [true, 'true'].include?(params[:sell_to_pos])
    @sell_to_kiosk = [true, 'true'].include?(params[:sell_to_kiosk])
    @sell_to_dine_in = params[:sell_to_dine_in]
    @sell_to_grab_food = params[:sell_to_grab_food]
    @sell_to_go_food = params[:sell_to_go_food]
    @sell_to_shopee_food = params[:sell_to_shopee_food]
    @sell_to_online_ordering = params[:sell_to_online_ordering]
    @sell_to_procurement_from_customer = params[:sell_to_procurement_from_customer] == 'true'
    @sell_to_procurement = params[:sell_to_procurement] == 'true'

    # location availability_flag
    @available_stock_flag_pos = params[:available_stock_flag_pos] == 'true' unless params[:available_stock_flag_pos].nil?
    @available_stock_flag_grab_food = params[:available_stock_flag_grab_food] == 'true' unless params[:available_stock_flag_grab_food].nil?
    @available_stock_flag_go_food = params[:available_stock_flag_go_food] == 'true' unless params[:available_stock_flag_go_food].nil?
    @available_stock_flag_shopee_food = params[:available_stock_flag_shopee_food] == 'true' unless params[:available_stock_flag_shopee_food].nil?
    @available_stock_flag_procurement = params[:available_stock_flag_procurement] == 'true' unless params[:available_stock_flag_procurement].nil?
    unless params[:available_stock_flag_online_ordering].nil?
      @available_stock_flag_online_ordering = params[:available_stock_flag_online_ordering] == 'true'
    end

    @location_ids_from_group = generate_location_ids_from_group(params)

    @out_of_stock_flag = params[:out_of_stock_flag]
    @recipe_status = params[:recipe_status] || nil
    @recipe_type = params[:recipe_type] || nil
    @show_detail_course = (params[:show_detail_course] || 'false').eql?('true')
    @no_stock = (params[:no_stock] || 'true').eql?('true') if params[:no_stock].present?
    @show_pos_position = (params[:show_pos_position] || 'false').eql?('true')
    @show_permissions = params.fetch(:show_permissions, 'false').eql?('true')
    @sub_brands = @location.sub_brands if @location.present? && @show_pos_position
    @conditions = { brand_id: @brand.id, food_integration_usage: false, _and: [], _or: [] }

    @export_raw = params[:export_raw]
    @recipe_line_product = (params[:recipe_line_product] || 'false').eql?('true')
    sort_key = populate_sort_key(params[:sort_key])
    @sort_order = sort_key == :_score ? :desc : populate_sort_order(params[:sort_order])
    @elastic_sort = { sort_key => @sort_order }

    @presentation = params[:presentation] || 'all'
    @variance_presentations = (params[:variance_presentations].presence || '').split(',')
    @custom_includes_options = params[:custom_includes_options]

    @use_scroll_api = params[:use_scroll_api] || false
    @scroll_size = params[:scroll_size] || 10_000

    @valid_for_role_and_outlet = params[:valid_for_role_and_outlet] == 'true'
    @valid_for_production = params[:valid_for_production] == 'true'

    @check_empty_stock = params[:check_empty_stock] == 'true'
    @is_select_all_location = params[:is_select_all_location] == 'true'
    @only_active_locations = params[:only_active_locations] == 'true'
    @include_option_set_recipes = params[:include_option_set_recipes] == 'true'
  end
  # rubocop:enable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength

  def generate_course
    return unless @show_detail_course

    @mapped_product_course = {}
    @brand.courses.each do |course|
      next if course.product_ids.blank?

      course.product_ids.each do |product_id|
        @mapped_product_course[product_id] = {
          id: course.id,
          name: course.name
        }
      end
    end
  end

  def populate_sort_key(key)
    return case key
           when 'product_category_name'
             :product_category_name
           when 'sku' || 'code'
             :sku
           when 'name'
             :name
           else
             @keyword.present? ? :_score : :name
           end
  end

  def populate_sort_order(sort_order_value)
    @sort_order = sort_order_value.try(:upcase) == 'DESC' ? :desc : :asc
  end

  def generate_exclude_id_condition
    @conditions = @conditions.merge({ id: { not: @exclude_ids } }) if @exclude_ids.present?
  end

  def generate_include_id_condition
    @conditions = @conditions.merge({ id: @ids }) if @ids.present?
  end

  def generate_exclude_product_with_variances_condition
    @conditions = @conditions.merge({ variance_count: 0 }) if @exclude_product_with_variances
  end

  def generate_recipe_line_condition
    @conditions = @conditions.merge({ recipe_made_to_order: false }) if @recipe_line_product
    @conditions = @conditions.merge({ recipe_lines_count: 0 }) if @exclude_used_in_recipe_line
  end

  def generate_variance_condition
    @conditions = @conditions.merge({ variance_parent_product_id: nil }) if @skip_child_variances
  end

  def generate_hide_because_parent_deactivated_condition
    @conditions = @conditions.merge({ hide_because_parent_deactivated: false }) if @enable_hide_because_parent_deactivated
  end

  def generate_modifier_condition
    # NOTE: @modifier is a boolean, need to use .nil? to check its presence
    @conditions = @conditions.merge({ modifier: @modifier }) unless @modifier.nil?
  end

  def generate_categories_condition
    if @filter_by_sub_brand
      if @location.present? && @location_to.present? # location to location
        apply_sub_brand_location_to_location_categories_condition
      elsif @external_vendor_type.present?
        apply_sub_brand_external_vendor_categories_condition
      elsif @procurement_from_customer
        apply_sub_brand_from_customer_categories_condition
      elsif @all_location_froms
        location_from_ids = @brand.locations.outlet.active.ids
        apply_sub_brand_bulk_order_to_multiple_locations(location_from_ids)
      elsif @location_from_ids.present?
        apply_sub_brand_bulk_order_to_multiple_locations(@location_from_ids)
      end
    else
      apply_default_categories_condition
    end
  end

  def apply_sub_brand_bulk_order_to_multiple_locations(location_from_ids)
    location_from_categories_ids = {}
    location_from_product_ids = {}
    @brand.locations.outlet.active.where(id: location_from_ids).each do |location|
      location_from_categories_ids[location.id] = location.sub_brands.pluck(:product_category_ids).flatten
      location_from_product_ids[location.id] = location.sub_brands.pluck(:product_ids).flatten
    end

    product_categories_ids = location_from_categories_ids.values
    return if product_categories_ids.all?(&:blank?) && location_from_product_ids.values.all?(&:blank?)

    categories_ids = product_categories_ids[0]
    product_categories_ids.drop(1).each do |each_location_product_categories_ids|
      categories_ids = categories_ids.intersection(each_location_product_categories_ids)
    end

    apply_specific_category_id_conditions(categories_ids, location_from_product_ids.values.flatten.uniq)
  end

  def apply_sub_brand_location_to_location_categories_condition
    if @location.central_kitchen? && @location_to.central_kitchen? # ck to ck
      apply_default_categories_condition
    elsif @location.outlet? && @location_to.central_kitchen? # outlet to ck
      categories_ids = @location.sub_brands.pluck(:product_category_ids).flatten
      if categories_ids.present?
        apply_specific_category_id_conditions(categories_ids, @location.sub_brands.pluck(:product_ids).flatten)
      else
        apply_default_categories_condition(@location.sub_brands.pluck(:product_ids).flatten)
      end
    elsif @location.outlet? && @location_to.outlet? # outlet to outlet
      location_from_categories_ids = @location.sub_brands.pluck(:product_category_ids).flatten
      location_to_categories_ids = @location_to.sub_brands.pluck(:product_category_ids).flatten
      categories_ids = if location_from_categories_ids.present? && location_to_categories_ids.present?
                         location_from_categories_ids & location_to_categories_ids
                       else
                         location_from_categories_ids | location_to_categories_ids
                       end.uniq

      any_category_from_location = location_from_categories_ids.present? || location_to_categories_ids.present?
      if any_category_from_location
        apply_specific_category_id_conditions(categories_ids, @location.sub_brands.pluck(:product_ids).flatten)
      else
        apply_default_categories_condition(@location.sub_brands.pluck(:product_ids).flatten)
      end
    end
  end

  def apply_sub_brand_from_customer_categories_condition
    if @location_to.central_kitchen? # customer to ck
      apply_default_categories_condition
    else # customer to outlet
      categories_ids = @location.sub_brands.pluck(:product_category_ids).flatten
      if categories_ids.present?
        apply_specific_category_id_conditions(categories_ids, @location.sub_brands.pluck(:product_ids).flatten)
      else
        apply_default_categories_condition(@location.sub_brands.pluck(:product_ids).flatten)
      end
    end
  end

  def apply_sub_brand_external_vendor_categories_condition
    if @location.central_kitchen? # ck to vendor
      apply_default_categories_condition
    else # outlet to vendor
      categories_ids = @location.sub_brands.pluck(:product_category_ids).flatten
      if categories_ids.present?
        apply_specific_category_id_conditions(categories_ids, @location.sub_brands.pluck(:product_ids).flatten)
      else
        apply_default_categories_condition(@location.sub_brands.pluck(:product_ids).flatten)
      end
    end
  end

  def apply_specific_category_id_conditions(categories_ids, product_ids = [])
    categories_ids << nil # always include uncategorized
    @conditions = if product_ids.blank?
                    @conditions.merge({ product_category_id: categories_ids })
                  else
                    @conditions[:_and] << { _or: [{ product_category_id: categories_ids }, { id: product_ids }] }
                  end
  end

  def apply_default_categories_condition(product_ids = [])
    # replace 0 to nil (uncategorized)
    categories_ids = (@category_ids - @exclude_category_ids).map { |category_id| category_id.zero? ? nil : category_id }
    @conditions = if product_ids.blank?
                    @conditions.merge({ product_category_id: categories_ids })
                  else
                    @conditions[:_and] << { _or: [{ product_category_id: categories_ids }, { id: product_ids }] }
                  end
  end

  # Filtering by deactivated is a bit different,
  # An active parent variance should show deactivated child variance too, so we need to include them here.
  def generate_status_condition
    if @status.present? && @status == 'activated'
      @conditions = @conditions.merge({ status: 'activated' })
    elsif @status.present? && @status == 'deactivated'
      @conditions[:_and] << { _or: [{ status: 'deactivated' }, { parent_show_deactivated_child: true }] }
    end
  end

  def generate_no_stock_condition
    @conditions = @conditions.merge({ no_stock: @no_stock }) unless @no_stock.nil?
  end

  def generate_policies
    @price_show_policy = Api::ProductsPolicy.new(@current_user, nil).price_show?
    @unit_conversion_show_policy = Api::ProductUnitConversionsPolicy.new(@current_user, nil).show?
  end

  def generate_outlet_recipe_product_type_condition
    @conditions = Restaurant::Services::Product::ElasticSearchConditionsOutletRecipe
                  .new(@conditions, @location, @is_select_all_location, @current_user).call!
  end

  def generate_outlet_production_product_type_condition
    access_list_ids = Restaurant::Services::Locations::LocationUserAccessListIds.new(@location, @current_user).call + [0]

    @conditions = Restaurant::Services::Product::ElasticSearchConditionsProduction
                  .new(@conditions, @location, access_list_ids).call!
  end

  def generate_availability_stock_condition
    return if @location.blank?

    location_id = @location.id
    if @sell_to_pos && !@available_stock_flag_pos.nil?
      @conditions.merge!({ location_ids_pos_out_of_stock: build_availability_condition(location_id, @available_stock_flag_pos) })
    end
    if @sell_to_kiosk && !@available_stock_flag_kiosk.nil?
      @conditions.merge!({ location_ids_kiosk_out_of_stock: build_availability_condition(location_id, @available_stock_flag_kiosk) })
    end
    if @sell_to_grab_food && !@available_stock_flag_grab_food.nil?
      @conditions.merge!({ location_ids_grab_food_out_of_stock: build_availability_condition(location_id, @available_stock_flag_grab_food) })
    end
    if @sell_to_go_food && !@available_stock_flag_go_food.nil?
      @conditions.merge!({ location_ids_go_food_out_of_stock: build_availability_condition(location_id, @available_stock_flag_go_food) })
    end
    if @sell_to_shopee_food && !@available_stock_flag_shopee_food.nil?
      @conditions.merge!({ location_ids_shopee_food_out_of_stock: build_availability_condition(location_id, @available_stock_flag_shopee_food) })
    end
    if @sell_to_online_ordering && !@available_stock_flag_online_ordering.nil?
      @conditions.merge!({ location_ids_online_ordering_out_of_stock: build_availability_condition(location_id,
                                                                                                   @available_stock_flag_online_ordering) })
    end
    if @sell_to_procurement && !@available_stock_flag_procurement.nil?
      @conditions.merge!({ location_ids_procurement_out_of_stock: build_availability_condition(location_id, @available_stock_flag_procurement) })
    end
  end

  def build_availability_condition(location_id, availability_flag)
    if availability_flag
      { not: [location_id] }
    else
      [location_id]
    end
  end

  # Location group for now used on monthly target dashboard
  def generate_location_ids_from_group(params)
    location_group_ids = []
    location_group_ids << params[:location_group_id].to_i if params[:location_group_id].present?
    return [] if location_group_ids.blank?

    @brand.location_groups.get_location_ids_by_ids(location_group_ids)
  end

  def generate_location_group_ids_condition
    return if @location_ids_from_group.blank?

    location_ids_conditions = @location_ids_from_group.map do |location_id|
      { location_sell_to_customer_types: "#{location_id}_#{@sell_to_customer_type}" } if @sell_to_customer_type.present?
    end

    location_ids_conditions = location_ids_conditions.compact

    return if location_ids_conditions.blank?

    @conditions[:_and] << {
      _or: location_ids_conditions
    }
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def generate_product_type_condition
    if @location.present? && @out_of_stock_flag.present?
      @conditions[:_or] << { location_out_of_stock_flags: "#{@location.id}_#{@out_of_stock_flag}" }

      if @out_of_stock_flag == 'true'
        @conditions[:_or] << { location_ids_go_food_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_grab_food_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_shopee_food_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_online_ordering_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_pos_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_kiosk_out_of_stock: @location.id }
        @conditions[:_or] << { location_ids_procurement_out_of_stock: @location.id }
      end
    end

    @conditions = Restaurant::Services::Product::ElasticSearchInternalDistributionTypeConditionsGenerator
                  .new(@conditions, @location, @location_to_id, @internal_distribution_type, @filter_product_type_behaviour).call!

    product_type_conditions = []

    if @location.present?
      product_type_conditions.push({ location_external_vendor_types: "#{@location.id}_#{@external_vendor_type}" }) if @external_vendor_type.present?
      if @internal_produce_type.present?
        product_type_conditions.push({ location_internal_produce_types: "#{@location.id}_#{@internal_produce_type}" })
      end
      if @sell_to_customer_type.present?
        product_type_conditions.push({ location_sell_to_customer_types: "#{@location.id}_#{@sell_to_customer_type}" })
      end

      generate_sell_to_type_location_conditions
    else
      product_type_conditions.push({ internal_distribution_type: @internal_distribution_type }) if @internal_distribution_type.present?
      product_type_conditions.push({ external_vendor_type: @external_vendor_type }) if @external_vendor_type.present?
      product_type_conditions.push({ internal_produce_type: @internal_produce_type }) if @internal_produce_type.present?

      generate_sell_to_type_conditions
    end

    if @any_external_vendor_type.present? # vendor product database
      product_type_conditions.push({ any_external_vendor_type: @any_external_vendor_type })
      product_type_conditions.push({ location_external_vendor_types: "#{@location.id}_true" }) if @location.present?
    end

    @conditions = Restaurant::Services::Product::ElasticSearchConditionsAllProductType
                  .new(@conditions, product_type_conditions, @filter_product_type_behaviour).call!
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  def generate_recipe_condition
    if @only_contain_recipe
      recipe_conditions = { recipe_id: { not: nil } }
      recipe_conditions = recipe_conditions.merge({ recipe_status: @recipe_status }) if @recipe_status.present?
      recipe_conditions = recipe_conditions.merge({ recipe_type: @recipe_type }) if @recipe_type.present?

      access_list_ids = Restaurant::Services::Locations::LocationUserAccessListIds.new(@location, @current_user).call + [0]
      recipe_conditions = recipe_conditions.merge({ search_recipe_access_list_ids: access_list_ids }) unless @valid_for_production

      if @include_option_set_recipes
        @conditions[:_or] << recipe_conditions
        @conditions[:_or] << { option_set_recipe_count: { gt: 0 } }
      else
        @conditions = @conditions.merge(recipe_conditions)
      end
    elsif @only_contain_recipe == false
      @conditions = @conditions.merge({ recipe_id: nil })
    end
  end

  def generate_location_condition
    return if procurement_promos? || (@is_select_all_location && !@only_active_locations)

    @conditions = Restaurant::Services::Product::ElasticSearchLocationsConditionsGenerator
                  .new(
                    conditions: @conditions,
                    location_id: @location_id, current_user: @current_user,
                    brand: @brand, all_location_froms: @all_location_froms,
                    location_from_ids: @location_from_ids, exclude_location_from_ids: @exclude_location_from_ids,
                    procurement_from_customer: @procurement_from_customer, location_to_id: @location_to_id
                  ).call
  end

  def generate_promo_location_condition
    return unless procurement_promos?

    params = {
      promo_location_to_ids: @promo_location_to_ids,
      promo_is_select_all_location_to_ids: @promo_is_select_all_location_to_ids,
      promo_location_to_type: @promo_location_to_type,
      brand: @brand
    }

    @conditions = Restaurant::Services::Product::ElasticSearchPromoLocationsConditionsGenerator.new(@conditions, params).call!
  end

  def procurement_promos?
    @promo_location_to_ids.present? || @promo_is_select_all_location_to_ids
  end

  def generate_vendor_products_condition
    return unless @any_external_vendor_type
    return if @vendor_id.blank?

    @vendor = @brand.vendors.find(@vendor_id)
    @vendor_products = @vendor.vendor_products.includes(:sell_tax, :product_unit)
    return if @vendor_products.blank?

    @use_vendor_products = true
    @conditions = @conditions.merge({ vendor_products_ids: [@vendor.id] })
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def generate_attribute(product)
    sell_price, sell_tax, sell_tax_setting = product.sell_price_sell_tax_and_sell_tax_setting_general(@location_id, nil)

    internal_tax = {
      internal_tax: (product.internal_tax_response if product.internal_tax.present?)
    }

    tax = if sell_tax.nil?
            { tax: nil, sell_tax: nil, sell_tax_setting: 'price_exclude_tax' }
          else
            {
              # TO BE DEPRECATED
              tax: {
                id: sell_tax&.id,
                name: sell_tax&.name,
                rate: sell_tax&.rate
              },
              sell_tax: {
                id: sell_tax&.id,
                name: sell_tax&.name,
                rate: sell_tax&.rate
              },
              sell_tax_setting: sell_tax_setting
            }
          end

    result = {
      id: product.id,
      name: product.name,
      sku: product.sku,
      upc: product.upc,
      description: product.description,
      internal_price: @price_show_policy ? product.internal_price : nil,
      sell_price: sell_price,
      status: product.status,
      modifier: product.modifier,
      no_stock: product.no_stock,
      internal_distribution_type: product.internal_distribution_type(procurement_location_id),
      external_vendor_type: product.external_vendor_type(procurement_location_id),
      internal_produce_type: product.internal_produce_type(procurement_location_id),
      out_of_stock_flag: product.out_of_stock_flag(procurement_location_id),
      available_stock_flag: product.available_stock_flag(@procurement_from_customer || @location_to.blank? ? @location : @location_to,
                                                         @allow_procurement_out_of_stock_flag),
      variance_parent_product_id: product.variance_parent_product_id,
      option_set_auto_prompt: product.option_set_auto_prompt,
      allow_custom_sell_price: product.allow_custom_sell_price,
      image_url: product.thumb_url.presence || product.image_url.presence || '',
      original_image_url: product.image_url.presence || '',
      owner_location: { id: product.owner_location.id, name: product.owner_location.name },
      sell_to_customer_type: product.sell_to_customer_type(procurement_location_id),
      sell_to_pos: product.sell_to_pos(procurement_location_id),
      sell_to_kiosk: product.sell_to_kiosk(procurement_location_id),
      sell_to_dine_in: product.sell_to_dine_in(procurement_location_id),
      sell_to_grab_food: product.sell_to_grab_food(procurement_location_id),
      sell_to_go_food: product.sell_to_go_food(procurement_location_id),
      sell_to_shopee_food: product.sell_to_shopee_food(procurement_location_id),
      sell_to_online_ordering: product.sell_to_online_ordering(procurement_location_id),
      sell_to_procurement_from_customer: product.sell_to_procurement_from_customer(procurement_location_id),
      vendor_products: vendor_products(product),
      order_price_editing_by_franchisor: product.order_price_editing_by_franchisor
    }

    result = result.merge({ stock_zero_or_below: generate_stock_zero_or_below(product) }) if @check_empty_stock
    result.merge(internal_tax).merge(tax)
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  def procurement_location_id
    if @procurement_from_customer || @location_to_id.blank?
      @location_id
    else
      @location_to_id
    end
  end

  def generate_stock_zero_or_below(product)
    @products_stock_zero_or_below[product.id]
  end

  def vendor_products(product)
    return [] unless @use_vendor_products

    @vendor_products
      .select { |vendor_product| vendor_product.product_id == product.id }
      .map do |vendor_product|
        internal_tax = vendor_product.sell_tax
        product_unit = vendor_product.product_unit
        sell_price = vendor_product.sell_price

        {
          product_unit_id: product_unit.id,
          product_unit_name: product_unit.name,
          sell_price: sell_price,
          lock_price: vendor_product.display_lock_price?,
          internal_tax: {
            id: internal_tax&.id,
            name: internal_tax&.name,
            rate: internal_tax&.rate
          },
          sell_tax_setting: vendor_product.sell_tax_setting
        }
      end
  end

  def generate_ids_only(product)
    return {
      id: product.id
    }
  end

  def gneerate_ids_and_names(product)
    return {
      id: product.id,
      name: product.name
    }
  end

  def generate_sku_and_sell_unit_name(product)
    return {
      id: product.id, name: product.name, sku: product.sku, upc: product.upc, sell_unit_name: product.sell_unit.name
    }
  end

  def generate_product_category_and_procurement_units(product)
    {
      id: product.id,
      name: product.name,
      sku: product.sku,
      upc: product.upc,
      product_category: {
        id: product.product_category_id,
        name: product.product_category&.name
      },
      product_procurement_units: build_procurement_units(product, product.procurement_units),
      vendor_procurement_units: build_procurement_units(product, product.vendor_procurement_units, has_no_internal_price: true)
    }
  end

  def generate_product_category_and_locations(product)
    location_count = product.locations.count
    is_multi_location = location_count > 1
    location_name = is_multi_location ? "#{location_count} #{I18n.t('general.locations')}" : product.locations.first.name

    {
      id: product.id,
      name: product.name,
      image_url: product.thumb_url.presence || product.image_url.presence || '',
      sku: product.sku,
      status: product.status,
      product_category: {
        id: product.product_category_id,
        name: product.product_category&.name
      },
      is_multi_location: is_multi_location,
      location_name: location_name
    }
  end

  # rubocop:disable Metrics/MethodLength
  def include_options
    return [] if @export_raw

    list = %i[product_unit tax internal_tax owner_location product_setting_locations brand]

    if @custom_includes_options.present?
      list += @custom_includes_options
      return list
    end

    if @presentation == 'all'
      list << [
        :owner_location,
        :print_category,
        :par_unit,
        :locations_products,
        :product_groups,
        :back_office_unit,
        :outlet_back_office_unit,
        :central_kitchen_back_office_unit,
        :locations,
        { product_group_products: :product_group },
        { product_category: :product_category_group },
        :sell_unit,
        {
          product_option_sets: {
            option_set: [
              option_set_options: [
                {
                  option_set_as_option: {
                    option_set_options: [
                      {
                        product: [
                          :tax,
                          :locations_products, :variances,
                          { product_price_per_order_types: [:sell_tax],
                            product_setting_locations: [:sell_tax] },
                          {
                            product_option_sets: {
                              option_set: [
                                {
                                  option_set_options: [
                                    :product,
                                    { option_set_custom_price_locations: :order_type },
                                    { option_set_price_table_details: { product_price_table: %i[locations] } }
                                  ]
                                }
                              ]
                            }
                          }
                        ]
                      },
                      { option_set_custom_price_locations: :order_type },
                      { option_set_price_table_details: { product_price_table: %i[locations] } }
                    ]
                  }
                },
                {
                  product: [
                    :tax,
                    :locations_products, :variances,
                    { product_price_per_order_types: [:sell_tax],
                      product_setting_locations: [:sell_tax] },
                    {
                      product_option_sets: {
                        option_set: [
                          {
                            option_set_options: [
                              {
                                product: [
                                  :tax,
                                  { product_price_per_order_types: [:sell_tax],
                                    product_setting_locations: [:sell_tax] },
                                  :locations_products, :variances,
                                  {
                                    product_option_sets: {
                                      option_set: [
                                        {
                                          option_set_options: [
                                            :product,
                                            { option_set_custom_price_locations: :order_type },
                                            { option_set_price_table_details: { product_price_table: %i[locations] } }
                                          ]
                                        }
                                      ]
                                    }
                                  }
                                ]
                              },
                              { option_set_custom_price_locations: :order_type },
                              { option_set_price_table_details: { product_price_table: %i[locations] } }
                            ]
                          }
                        ]
                      }
                    }
                  ]
                },
                { option_set_custom_price_locations: :order_type },
                { option_set_price_table_details: { product_price_table: %i[locations] } }
              ]
            ]
          }
        },
        { product_setting_locations: %i[location par_unit sell_tax] },
        { product_internal_price_locations: %i[location product_unit] },
        { product_price_per_order_types: %i[location order_type sell_tax] },
        { procurement_units: %i[product_unit] },
        { outlet_to_outlet_procurement_units: %i[product_unit] },
        { vendor_procurement_units: %i[product_unit] },
        { central_kitchen_procurement_units: %i[product_unit] },
        { recipe: %i[product_unit] },
        { product_unit_conversions: %i[product_unit] },
        { product_price_table_details: [:tax, { product_price_table: %i[locations] }] },
        { internal_price_price_table_details: { product_price_table: %i[locations] } }
      ]
    end

    if @include_variance
      list << [
        {
          variances: [
            { locations_products: :location },
            :product_unit,
            { product_category: :product_category_group },
            :print_category,
            :tax,
            :internal_tax,
            :back_office_unit,
            :outlet_back_office_unit,
            :central_kitchen_back_office_unit,
            :sell_unit,
            :par_unit,
            :owner_location,
            { product_setting_locations: %i[location par_unit sell_tax] },
            { product_internal_price_locations: %i[location product_unit] },
            { procurement_units: %i[product_unit] },
            { outlet_to_outlet_procurement_units: %i[product_unit] },
            { vendor_procurement_units: %i[product_unit] },
            { central_kitchen_procurement_units: %i[product_unit] },
            { product_unit_conversions: %i[product_unit] },
            {
              product_option_sets: {
                option_set: [
                  option_set_options: [
                    { product: :locations_products },
                    { option_set_custom_price_locations: :order_type },
                    { option_set_price_table_details: { product_price_table: %i[locations] } }
                  ]
                ]
              }
            },
            { product_price_per_order_types: %i[order_type location sell_tax] },
            { product_price_table_details: [:tax, { product_price_table: %i[locations] }] },
            { internal_price_price_table_details: { product_price_table: %i[locations] } }
          ]
        }
      ]
    end

    list
  end
  # rubocop:enable Metrics/MethodLength

  def generate_unit_conversion(product_unit_conversions, default_unit, curr_unit)
    unit_id = curr_unit.try(:id)
    unit_name = curr_unit.try(:name)

    if default_unit.nil? || curr_unit.nil? || default_unit.try(:id) == curr_unit.try(:id)
      return {
        id: unit_id,
        name: unit_name
      }
    end

    conversion_unit = product_unit_conversions.detect { |product_unit_conversion| product_unit_conversion.product_unit_id == curr_unit.try(:id) }

    return {
      id: unit_id,
      name: unit_name,
      internal_price: @price_show_policy ? conversion_unit.try(:internal_price) : nil
    }
  end

  def generate_units(product)
    product_unit_conversions = product.product_unit_conversions

    {
      product_category: {
        id: product.product_category.try(:id),
        name: product.product_category.try(:name)
      },
      product_category_group: {
        id: product.product_category&.product_category_group_id,
        name: product.product_category&.product_category_group&.name || ProductCategoryGroup::Uncategorized.instance.name
      },
      print_category: {
        id: product.print_category.try(:id),
        name: product.print_category.try(:name)
      },
      product_unit: {
        id: product.product_unit.try(:id),
        name: product.product_unit.try(:name)
      },
      back_office_unit: generate_unit_conversion(product_unit_conversions, product.product_unit, product.back_office_unit),
      outlet_back_office_unit: if product.outlet_back_office_unit.present?
                                 generate_unit_conversion(product_unit_conversions, product.product_unit,
                                                          product.outlet_back_office_unit)
                               end,
      central_kitchen_back_office_unit: if product.central_kitchen_back_office_unit.present?
                                          generate_unit_conversion(product_unit_conversions, product.product_unit,
                                                                   product.central_kitchen_back_office_unit)
                                        end,
      sell_unit: generate_unit_conversion(product_unit_conversions, product.product_unit, product.sell_unit),
      par_unit: generate_unit_conversion(product_unit_conversions, product.product_unit, product.preloaded_par_unit(@location_id))
    }
  end

  def build_product_location(location)
    { id: location.id, name: location.name, branch_type: location.branch_type }
  end

  def build_product_location_group(location_group)
    { id: location_group.id, name: location_group.name, location_group_details: build_location_group_details(location_group) }
  end

  def build_location_group_details(location_group)
    location_group.location_group_details.map do |location_group_detail|
      { id: location_group_detail.id, location_id: location_group_detail.location_id, name: location_group_detail.location.name }
    end
  end

  def generate_locations(product)
    location_groups = LocationGroup.where(id: product.location_group_ids)

    {
      is_select_all_location: product.is_select_all_location,
      locations: product.locations.map { |location| build_product_location(location) },
      exclude_location_ids: product.exclude_location_ids,
      exclude_locations: @available_locations
        .select { |location| product.exclude_location_ids.include?(location.id) }
        .map { |location| build_product_location(location) },
      location_type: product.location_type,
      is_select_all_location_group: product.is_select_all_location_group,
      location_groups: location_groups.map { |location_group| build_product_location_group(location_group) },
      exclude_location_group_ids: product.exclude_location_group_ids,
      exclude_location_groups: @available_location_groups
        .select { |location_group| product.exclude_location_group_ids.include?(location_group.id) }
        .map { |location_group| build_product_location_group(location_group) }
    }
  end

  def generate_variance(product)
    return {} unless @include_variance

    product_variances = case @status
                        when 'deactivated'
                          product.variances.select(&:deactivated?)
                        else
                          product.variances.select(&:activated?)
                        end

    {
      variances: product_variances.sort_by(&:sequence).filter_map do |variance|
        next unless filter_variance_by_location(variance)

        build_variance(variance)
      end
    }
  end

  def filter_variance_by_location(variance)
    filter_variance_locations_products(variance) &&
      filter_variance_product_setting_locations_sell_to_pos(variance)
  end

  def filter_variance_locations_products(variance)
    return true if @location_id.blank?

    variance.with_specific_locations_product.detect { |locations_product| locations_product.location_id == @location_id }.present?
  end

  def filter_variance_product_setting_locations_sell_to_pos(variance)
    return true if @sell_to_pos.blank?
    return variance.sell_to_pos if @location_id.blank?

    setting = variance.product_setting_locations.detect { |product_setting_location| product_setting_location.location_id == @location_id }
    if setting.nil? || !setting.is_product_type_active?
      variance.sell_to_pos
    else
      setting.sell_to_pos
    end
  end

  def variance_locations_products(variance)
    variance.locations_products
  end

  def generate_product_group(product)
    product_groups = product.product_groups.map do |product_group|
      { id: product_group.id, name: product_group.name, active: product_group.active }
    end

    { product_groups: product_groups }
  end

  def build_variance(variance)
    additional_attributes = {}
    if @variance_presentations.include? VARIANCE_PRESENTATIONS[:internal_price_units]
      additional_attributes.merge!(generate_internal_price_units(variance))
    end

    generate_attribute(variance).merge(generate_units(variance))
                                .merge(generate_option_set(variance))
                                .merge(generate_sell_custom_sell_price(variance))
                                .merge(generate_par_quantity(product: variance, location_id: @location_id))
                                .merge(generate_internal_price_units(variance))
                                .merge(additional_attributes)
  end

  def generate_option_set(product)
    {
      product_option_sets: product.product_option_sets.map do |product_option_set|
        product_option_set.attributes.merge({
                                              option_set: generate_option_set_option(product_option_set.option_set)
                                            })
      end
    }
  end

  def generate_option_set_option(option_set)
    Products::Services::OptionSetOptionsGenerator.new(option_set, @location_id, @order_types).call
  end

  def generate_product_setting_location(product)
    {
      product_setting_locations: product.product_setting_locations.filter_map do |product_setting_location|
        next if @location_id.present? && product_setting_location.location_id != @location_id

        build_product_setting_location(product_setting_location)
      end
    }
  end

  def build_product_setting_location(data)
    setting_detail = data.attributes
    setting_detail[:location] = {
      id: data.location.id,
      name: data.location.name,
      branch_type: data.location.branch_type
    }
    setting_detail[:sell_tax] = if data.sell_tax.present?
                                  {
                                    id: data.sell_tax.id,
                                    name: data.sell_tax.name,
                                    rate: data.sell_tax.rate
                                  }
                                end
    setting_detail[:par_unit] = if data.par_unit.present?
                                  {
                                    id: data.par_unit.id,
                                    name: data.par_unit.name
                                  }
                                end
    setting_detail.with_indifferent_access
  end

  def generate_internal_price_units(product)
    price_show = @price_show_policy

    if @unit_conversion_show_policy
      {
        product_unit_conversions: product.product_unit_conversions.map { |conversion| build_product_unit_conversion(conversion, price_show) },
        product_procurement_units: build_procurement_units(product, product.procurement_units),
        outlet_to_outlet_procurement_units: build_procurement_units(product, product.outlet_to_outlet_procurement_units),
        vendor_procurement_units: build_procurement_units(product, product.vendor_procurement_units),
        central_kitchen_procurement_units: build_procurement_units(product, product.central_kitchen_procurement_units),
        product_internal_price_table_units: product.internal_price_table_units(@location_id, product.procurement_units.map(&:product_unit_id))
      }
    else
      {}
    end
  end

  def build_procurement_units(product, procurement_units, has_no_internal_price: false)
    procurement_units.sort_by(&:sequence).map do |procurement_unit|
      build_procurement_unit(product, procurement_unit, has_no_internal_price: has_no_internal_price)
    end
  end

  def build_internal_price_table_units(product, _procurement_unit_ids)
    product.internal_price_table_units
  end

  def generate_order_price_units(product)
    internal_price = @price_show_policy ? product.internal_price(@location_id) : nil

    return { internal_price: internal_price } unless @unit_conversion_show_policy

    smallest_product_unit = [{
      id: nil,
      product_unit: {
        id: product.product_unit.id,
        name: product.product_unit.name
      },
      product_unit_id: product.product_unit_id,
      converted_qty: '1',
      internal_price: internal_price
    }.with_indifferent_access]

    product_unit_conversions = smallest_product_unit + product.product_unit_conversions.map do |conversion|
                                                         build_product_unit_conversion(conversion, @price_show_policy)
                                                       end

    product_procurement_units = smallest_product_unit + product.procurement_units.sort_by(&:sequence).map do |procurement_unit|
                                                          build_procurement_unit(product, procurement_unit)
                                                        end

    {
      internal_price: internal_price,
      product_unit_conversions: product_unit_conversions,
      product_procurement_units: product_procurement_units
    }
  end

  def build_product_unit_conversion(unit_conversion, price_show)
    conversion = unit_conversion.attributes
    conversion[:product_unit] = unit_conversion.product_unit
    conversion[:internal_price] = price_show ? unit_conversion.internal_price : nil

    # also check valid_for_destroy? in app/models/product_unit_conversion.rb
    conversion[:can_delete] = !@cant_be_deleted_unit_conversion_ids.to_a.include?(unit_conversion.id)
    conversion.with_indifferent_access
  end

  def build_procurement_unit(product, procurement_unit, has_no_internal_price: false)
    procurement_detail = {
      id: procurement_unit.id,
      sequence: procurement_unit.sequence,
      product_unit: {
        id: procurement_unit.product_unit.id,
        name: procurement_unit.product_unit.name
      }
    }

    if !has_no_internal_price && @price_show_policy
      procurement_detail[:internal_price] =
        product.internal_price(@location_id, procurement_unit.product_unit.id)
    end

    procurement_detail.with_indifferent_access
  end

  def generate_product_internal_price_locations(product)
    return {} unless @price_show_policy

    {
      product_internal_price_locations: product.product_internal_price_locations.filter_map do |product_internal_price_locations|
        next if @location_id.present? && product_internal_price_locations.location_id != @location_id

        build_product_internal_price_location(product_internal_price_locations)
      end
    }
  end

  def build_product_internal_price_location(data)
    setting_detail = data.attributes
    setting_detail[:location] = {
      id: data.location.id,
      name: data.location.name,
      branch_type: data.location.branch_type
    }
    setting_detail[:product_unit] = {
      id: data.product_unit.id,
      name: data.product_unit.name
    }
    setting_detail.with_indifferent_access
  end

  def generate_product_price_per_order_types(product)
    product_price_per_order_types = product_price_per_order_types_filter(product)

    {
      product_price_per_order_types: product_price_per_order_types.map do |product_price_per_order_type|
        next if @location_id.present? && product_price_per_order_type.location_id.present? &&
                product_price_per_order_type.location_id != @location_id

        build_product_price_per_order_type(product_price_per_order_type)
      end.compact
    }
  end

  def build_product_price_per_order_type(data)
    setting_detail = data.attributes
    setting_detail[:location] = if data.location.present?
                                  {
                                    id: data.location.id,
                                    name: data.location.name,
                                    branch_type: data.location.branch_type
                                  }
                                end
    setting_detail[:sell_tax] = if data.sell_tax.present?
                                  {
                                    id: data.sell_tax.id,
                                    name: data.sell_tax.name,
                                    rate: data.sell_tax.rate
                                  }
                                end
    setting_detail[:order_type] = {
      id: data.order_type.id,
      name: data.order_type.name
    }
    setting_detail.with_indifferent_access
  end

  def product_price_per_order_types_filter(product)
    product_price_per_order_types = product.product_price_per_order_types.filter do |product_price_per_order_type|
      product_price_per_order_type.product_id == product.id
    end

    if @location_id.present?
      product_price_per_order_types = product_price_per_order_types.filter do |product_price_per_order_type|
        product_price_per_order_type.location_id == @location_id || product_price_per_order_type.location_id.nil?
      end
    end

    product_price_per_order_types
  end

  def generate_sell_custom_sell_price(product)
    Products::Services::CustomSellPriceGenerator
      .new(product, @location, @order_types).call
  end

  def generate_recipe(product)
    recipe = product.recipe

    return {} if recipe.nil?

    {
      recipe: {
        id: recipe.id,
        status: recipe.status,
        recipe_type: recipe.recipe_type,
        product_unit: {
          id: recipe.product_unit.id,
          name: recipe.product_unit.name
        },
        expected_yield: recipe.expected_yield
      }
    }
  end

  def generate_option_set_recipes(product)
    option_set_recipes = product.option_set_recipes

    return { option_set_recipes: [] } if option_set_recipes.blank?

    {
      option_set_recipes: option_set_recipes.map do |recipe|
        {
          id: recipe.id,
          status: recipe.status,
          recipe_type: recipe.recipe_type,
          product_unit: {
            id: recipe.product_unit.id,
            name: recipe.product_unit.name
          },
          expected_yield: recipe.expected_yield,
          option_set_options: recipe.recipe_product_option_set.option_set_options.map do |opt|
            { product_name: opt.product.name }
          end
        }
      end
    }
  end

  # TODO: deprecated pos_product_layout after POS fully migrated to sub_brand type
  def generate_pos_position(product)
    {
      pos_product_layout: product.pos_product_layout(@pos_product_layout),
      pos_product_layout_per_sub_brand: product.pos_sub_brand_product_layout(@sub_brands, @pos_product_layout_per_sub_brand)
    }
  end

  def generate_permissions(product)
    policy = Api::ProductsPolicy.new(@current_user, product)
    {
      can_update: policy.update?,
      can_reactivate: policy.reactivate?,
      can_deactivate: policy.deactivate?
    }
  end

  def generate_course_result(product)
    return { course: nil } if @mapped_product_course.blank?

    found_mapped = @mapped_product_course[product.id]
    return { course: nil } if found_mapped.blank?

    return { course: found_mapped }
  end

  def generate_sell_to_type_conditions
    @conditions[:_or] << { sell_to_dine_in: @sell_to_dine_in } if @sell_to_dine_in.present?
    @conditions[:_or] << { sell_to_grab_food: @sell_to_grab_food } if @sell_to_grab_food.present?
    @conditions[:_or] << { sell_to_go_food: @sell_to_go_food } if @sell_to_go_food.present?
    @conditions[:_or] << { sell_to_shopee_food: @sell_to_shopee_food } if @sell_to_shopee_food.present?
    @conditions[:_or] << { sell_to_online_ordering: @sell_to_online_ordering } if @sell_to_online_ordering.present?
    @conditions[:_or] << { sell_to_procurement_from_customer: @sell_to_procurement_from_customer } if @sell_to_procurement_from_customer.present?
    @conditions[:_or] << { sell_to_customer_type: @sell_to_customer_type } if @sell_to_customer_type.present?
    @conditions[:_or] << { sell_to_pos: @sell_to_pos } if @sell_to_pos.present?
    @conditions[:_or] << { sell_to_kiosk: @sell_to_kiosk } if @sell_to_kiosk.present?
  end

  def generate_sell_to_type_location_conditions
    @conditions[:_or] << { location_sell_to_pos_types: "#{@location.id}_#{@sell_to_pos}" } if @sell_to_pos.present?
    @conditions[:_or] << { location_sell_to_kiosk_types: "#{@location.id}_#{@sell_to_kiosk}" } if @sell_to_kiosk.present?
    @conditions[:_or] << { location_sell_to_dine_in_types: "#{@location.id}_#{@sell_to_dine_in}" } if @sell_to_dine_in.present?
    @conditions[:_or] << { location_sell_to_grab_food_types: "#{@location.id}_#{@sell_to_grab_food}" } if @sell_to_grab_food.present?
    @conditions[:_or] << { location_sell_to_go_food_types: "#{@location.id}_#{@sell_to_go_food}" } if @sell_to_go_food.present?
    @conditions[:_or] << { location_sell_to_shopee_food_types: "#{@location.id}_#{@sell_to_shopee_food}" } if @sell_to_shopee_food.present?
    if @sell_to_procurement_from_customer.present?
      @conditions[:_or] << { location_sell_to_procurement_from_customer_types: "#{@location.id}_#{@sell_to_procurement_from_customer}" }
    end

    if @sell_to_online_ordering.present?
      @conditions[:_or] << { location_sell_to_online_ordering_types: "#{@location.id}_#{@sell_to_online_ordering}" }
    end
  end

  def generate_product_layout
    @pos_product_layout = ProductLayout.find_by(location_id: @location_id, layout_type: 'pos').try(:payload)

    @pos_product_layout_per_sub_brand = ProductLayout.find_by(
      location_id: @location_id,
      layout_type: ProductLayout.layout_types[:sub_brand_pos]
    ).try(:payload)
  end
end
# rubocop:enable Metrics/ClassLength
