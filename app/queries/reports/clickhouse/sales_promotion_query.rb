class Reports::Clickhouse::SalesPromotionQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable
  include Restaurant::Modules::Report::SalesPromotions::Clickhouse::BaseQuery

  def initialize(params, is_report = false)
    @current_user = params[:current_user]
    @brand = params[:current_brand]
    @is_report = is_report

    generate_variables(params)
  end

  def generate_variables(params)
    @model = Restaurant::Constants::REPORT_MODEL
    @action_type = Restaurant::Constants::PROMOTION_ACTION

    @keyword = params[:keyword].to_s
    @export_mode = params[:export_mode] || 'combined'
    @is_select_all_location = (params[:is_select_all_location] || 'false').eql?('true')
    @is_select_all_location = @export_mode == 'combined' ? @is_select_all_location : false

    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @start_date = if params[:start_date].present?
                    params[:start_date].to_date
                  else
                    Time.zone.now.in_time_zone(@brand.timezone).to_date
                  end
    @end_date = if params[:end_date].present?
                  params[:end_date].to_date
                else
                  Time.zone.now.in_time_zone(@brand.timezone).to_date
                end

    generate_location_and_location_group_ids(params)
  end

  def filter
    location_ids = allowable_location_ids
    @data, @total_data = generate_data(location_ids)

    {
      paging: {
        current_page: @current_page,
        total_item: @total_data
      },
      report_data: generate_report_data(@data),
      report_headers: generate_headers,
      report_filters: generate_filters
    }
  end

  def build_promo_data(promo)
    {
      id: promo.id,
      name: promo.name,
      number_of_used: 0,
      in_house: 0,
      external: 0,
      promo_amount: 0
    }
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def populate_data_promo(data, promo_ids, location_ids)
    subquery_sales = ::Clickhouse::Models::SaleTransaction.with_deleted
                                                          .where(location_id: location_ids)
                                                          .by_datetime_range(@start_date.to_date.beginning_of_day.strftime('%Y-%m-%d %H:%M:%S'),
                                                                             @end_date.to_date.end_of_day.strftime('%Y-%m-%d %H:%M:%S'))
                                                          .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    sales_list = ::Clickhouse::Models::SaleTransaction
                 .from("(#{subquery_sales}) AS public_sale_transactions")
                 .where('notEmpty(public_sale_transactions.applied_promos)')
                 .joins(Arel.sql("ARRAY JOIN JSONExtractArrayRaw(COALESCE(public_sale_transactions.applied_promos, '[]')) AS i"))
                 .where('public_sale_transactions.status = 0')
                 .select(<<~SQL)
                   sum(if(JSONHas(i, 'used_quota'), JSONExtractInt(i, 'used_quota'), 1))             AS count,
                   JSONExtractInt(i, 'id')                                                           AS promo_id,
                   sum(if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0))                 AS amount,
                   sum( (if(JSONHas(i, 'discount_in_house_cost'), JSONExtractFloat(i, 'discount_in_house_cost'), 0.0) / 100.0)
                       * if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0) )              AS in_house,
                   sum( (if(JSONHas(i, 'discount_external_cost'), JSONExtractFloat(i, 'discount_external_cost'), 0.0) / 100.0)
                       * if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0) )              AS external
                 SQL
                 .group(Arel.sql('promo_id'))

    refund_subquery = ::Clickhouse::Models::SalesReturn.with_deleted
                                                       .where(location_id: location_ids)
                                                       .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    sales_return_list = ::Clickhouse::Models::SaleTransaction
                        .from("(#{subquery_sales}) AS public_sale_transactions")
                        .joins(Arel.sql("JOIN (#{refund_subquery}) AS public_sales_returns
                                                  ON public_sales_returns.sale_transaction_id
                                                  = public_sale_transactions.id and public_sales_returns.deleted = FALSE"))
                        .joins(Arel.sql("ARRAY JOIN JSONExtractArrayRaw(COALESCE(public_sales_returns.applied_promos_refund, '[]')) AS i"))
                        .where('public_sale_transactions.status = 0')
                        .where('notEmpty(public_sales_returns.applied_promos_refund)')
                        .where('public_sales_returns.status = 0')
                        .select(<<~SQL)
                          sum(if(JSONHas(i, 'used_quota'), JSONExtractInt(i, 'used_quota'), 1))             AS count,
                          JSONExtractInt(i, 'id')                                                           AS promo_id,
                          sum(if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0))                 AS amount,
                          sum( (if(JSONHas(i, 'discount_in_house_cost'), JSONExtractFloat(i, 'discount_in_house_cost'), 0.0) / 100.0)
                              * if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0) )              AS in_house,
                          sum( (if(JSONHas(i, 'discount_external_cost'), JSONExtractFloat(i, 'discount_external_cost'), 0.0) / 100.0)
                              * if(JSONHas(i, 'amount'), JSONExtractFloat(i, 'amount'), 0.0) )              AS external
                        SQL
                        .group(Arel.sql('promo_id'))

    promo_ids.each do |promo_id|
      promo = data[:promos].find { |pro| pro[:id] == promo_id }
      sales = sales_list.find { |list| list['promo_id'] == promo_id }
      sales_return = sales_return_list.find { |list| list['promo_id'] == promo_id }
      promo[:number_of_used] += sales.try(:[], 'count').to_d
      promo[:promo_amount] += sales.try(:[], 'amount').to_d - sales_return.try(:[], 'amount').to_d
      promo[:in_house] += sales.try(:[], 'in_house').to_d - sales_return.try(:[], 'in_house').to_d
      promo[:external] += sales.try(:[], 'external').to_d - sales_return.try(:[], 'external').to_d
    end
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  def list_transaction_manual_promo(location_ids, batch_size: 5000)
    offset = 0
    result = []

    total_discount_string = '(COALESCE(public_sale_transactions.discount_fee,0) -
          public_sale_transactions.loyalty_discount_fee + COALESCE(modifier_discounts.discount_line,0))'

    loop do
      query = base_query_transaction_manual_promo(location_ids)
              .select("#{total_discount_string} as total_discount", 'COUNT(1) as count')
              .group('total_discount')
              .order('total_discount')
              .limit(batch_size)
              .offset(offset).to_sql

      rows = ::Clickhouse::Models::SaleTransaction.find_by_sql(query).as_json
      break if rows.blank?

      offset += batch_size
      result += rows
    end

    result
  end

  def list_transaction_manual_promo_sales_with_applied_promos(location_ids, batch_size: 5000)
    offset = 0
    result = []

    total_discount_string = '(COALESCE(public_sale_transactions.discount_fee,0) -
          public_sale_transactions.loyalty_discount_fee + COALESCE(modifier_discounts.discount_line,0))'

    loop do
      query = base_query_transaction_manual_promo_with_applied_promos(location_ids)
              .select("#{total_discount_string} as total_discount", 'COUNT(1) as count')
              .group('total_discount')
              .order('total_discount')
              .limit(batch_size)
              .offset(offset).to_sql

      rows = ::Clickhouse::Models::SaleTransaction.find_by_sql(query).as_json
      break if rows.blank?

      offset += batch_size
      result += rows
    end

    result
  end

  def populate_data_manual_promo(data, manual_promos, promo_count)
    if @is_report
      start_offset = nil
      end_offset = nil
    else
      total_promo_count = promo_count
      if @current_page * @item_per_page <= total_promo_count
        start_offset = nil
        end_offset = nil
      else
        start_offset = (@current_page - 1) * @item_per_page - total_promo_count + data[:promos].count
        end_offset = @current_page * @item_per_page - total_promo_count - 1
      end
    end

    manual_promos.each_with_index do |manual_promo, index|
      next unless @is_report || (!start_offset.nil? && !end_offset.nil? && index >= start_offset && index <= end_offset)

      data[:promos] << {
        id: nil,
        name: "#{I18n.t('report.sales_promotion.manual_discount')} #{manual_promo['total_discount']}",
        number_of_used: manual_promo['count'],
        in_house: manual_promo['count'].to_d * manual_promo['total_discount'].to_d,
        external: 0,
        promo_amount: manual_promo['count'].to_d * manual_promo['total_discount'].to_d
      }
    end
  end

  private

  def generate_data(location_ids)
    data = { promos: [], total_number_of_used: 0, total_in_house: 0, total_external: 0, total_promo_amount: 0 }
    return [data, 0] if location_ids.blank?

    promos = Report::Models::Promo
             .with_deleted
             .where(brand_id: @brand.id)
             .where("start_date <= '#{@end_date}'")
             .where("end_date is NULL OR end_date >='#{@start_date}'")
    promos = promos.where('name ilike ?', "%#{@keyword}%") if @keyword.present?

    manual_promos = list_transaction_manual_promo(location_ids)
    manual_promos += list_transaction_manual_promo_sales_with_applied_promos(location_ids)

    promo_count = promos.count('id')
    manual_promo_size = manual_promos.size
    loyalty_discounts_count = count_list_transaction_loyalty_discounts(location_ids)

    total_data = promo_count + manual_promo_size + loyalty_discounts_count

    promos = promos.order(id: :desc)
    promos = promos.limit(@item_per_page).offset((@current_page - 1) * @item_per_page) unless @is_report

    promos.each do |promo|
      data[:promos] << build_promo_data(promo)
    end

    promo_ids = promos.map(&:id)
    populate_data_promo(data, promo_ids, location_ids)
    populate_data_manual_promo(data, manual_promos, promo_count)
    populate_transaction_loyalty_discounts(data, location_ids, promo_count, manual_promo_size)

    [data, total_data]
  end

  def list_transaction_loyalty_discounts(location_ids)
    SaleLoyaltyDiscount
      .ok
      .where(brand_id: @brand.id, location_id: location_ids)
      .where('local_sales_time >= ?', @start_date.to_date.beginning_of_day.strftime('%Y-%m-%d %H:%M:%S'))
      .where('local_sales_time <= ?', @end_date.to_date.end_of_day)
      .select(
        'loyalty_discount_id, point_needed',
        'sum(discounted_amount) as total_discount',
        'sum(quantity) as quantity'
      )
      .group('loyalty_discount_id, point_needed')
  end

  def count_list_transaction_loyalty_discounts(location_ids)
    sql = "select count(1) from (#{list_transaction_loyalty_discounts(location_ids).to_sql}) as total_rows"
    ActiveRecord::Base.connection.execute(sql).first['count']
  end

  def populate_transaction_loyalty_discounts(data, location_ids, promo_count, manual_promo_size)
    total_promo_count = promo_count + manual_promo_size
    return if @is_report == false && @current_page * @item_per_page <= total_promo_count

    loyalty_discounts = list_transaction_loyalty_discounts(location_ids)
    unless @is_report
      generated_promo_row_count = data[:promos].size

      page = ((@current_page * @item_per_page - total_promo_count).to_d / @item_per_page.to_d).ceil
      limit = (@item_per_page - generated_promo_row_count)

      loyalty_discounts = loyalty_discounts.limit(limit).offset((page - 1) * limit)
    end

    loyalty_discounts.order('point_needed').each do |loyalty_discount|
      data[:promos] << {
        id: nil,
        name: "Loyalty Discount #{loyalty_discount['point_needed']} points",
        number_of_used: loyalty_discount['quantity'],
        in_house: loyalty_discount['total_discount'].to_d,
        external: 0,
        promo_amount: loyalty_discount['total_discount'].to_d
      }
    end
  end

  def generate_report_data(data)
    report_data = []

    data[:promos].each do |promo|
      number_of_used = ApplicationHelper.format_amount_by_brand(promo[:number_of_used], @brand, is_export: @is_report)
      in_house_cost = ApplicationHelper.format_amount_by_brand(promo[:in_house], @brand, is_export: @is_report, is_money: true)
      external_cost = ApplicationHelper.format_amount_by_brand(promo[:external], @brand, is_export: @is_report, is_money: true)
      promo_amount = ApplicationHelper.format_amount_by_brand(promo[:promo_amount], @brand, is_export: @is_report, is_money: true)

      money_styles = { alignment: :right, size: 14, cell_format: :money }
      promo_column = []
      promo_column << ReportHelper.build_text_cell(text: promo[:name], size: 14, weight: 500)
      promo_column << ReportHelper.build_text_cell(text: number_of_used, alignment: 'center', size: 14, weight: 500, cell_format: :integer)
      promo_column << ReportHelper.build_text_cell(text: in_house_cost, **money_styles)
      promo_column << ReportHelper.build_text_cell(text: external_cost, **money_styles)
      promo_column << ReportHelper.build_text_cell(text: promo_amount, **money_styles)

      report_data << promo_column
    end

    report_data
  end

  def generate_headers
    [
      ReportHelper.build_text_cell(text: I18n.t('report.sales_promotion.header.promotion_name'), weight: 500),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_promotion.header.no_of_used'), weight: 500),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_promotion.header.in_house'), weight: 500, alignment: 'right'),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_promotion.header.external'), weight: 500, alignment: 'right'),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_promotion.header.promo_amount'), weight: 500, alignment: 'right')
    ]
  end

  def generate_filters
    location_text = ReportHelper.generate_filter_location(**filter_params)
    [
      [{ text: I18n.t('general.period') }, { text: "#{@start_date} - #{@end_date}" }],
      [{ text: I18n.t('locations.title') }, { text: location_text }]
    ]
  end

  def filter_params
    {
      user: @current_user,
      is_select_all_location: @is_select_all_location,
      location_ids: @location_ids,
      exclude_location_ids: @exclude_location_ids,
      location_group_ids: @location_group_ids,
      exclude_location_group_ids: @exclude_location_group_ids
    }
  end
end
