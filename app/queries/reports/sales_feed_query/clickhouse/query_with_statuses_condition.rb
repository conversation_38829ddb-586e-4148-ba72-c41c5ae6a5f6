class Reports::SalesFeedQuery::Clickhouse::QueryWithStatusesCondition
  REFUND = 'refund'.freeze
  PAID = 'paid'.freeze
  CANCELLED = 'cancelled'.freeze
  VOID = 'void'.freeze

  def initialize(query, location_ids, start_time, end_time, statuses)
    @query = query
    @statuses = statuses
    @location_ids = location_ids
    @start_time = start_time
    @end_time = end_time
  end

  # rubocop:disable Metrics/MethodLength
  def call
    return @query if @statuses.blank?

    conditions = []
    if @statuses[REFUND]

      subquery_sale_returns = ::Clickhouse::Models::SalesReturn.with_deleted
                                                               .where(location_id: @location_ids)
                                                               .select('sale_transaction_id, deleted')
                                                               .limit_by_to_sql(:id, 1)

      subquery = ::Clickhouse::Models::SalesReturn.with_deleted
                                                  .from("(#{subquery_sale_returns}) AS public_sales_returns")
                                                  .where('public_sales_returns.deleted = false')
                                                  .select('DISTINCT sale_transaction_id AS sale_transaction_id')
                                                  .to_sql

      @query = @query.joins("LEFT JOIN (#{subquery}) filtered_public_sales_returns ON
                            filtered_public_sales_returns.sale_transaction_id = public_sale_transactions.id")

      conditions << 'filtered_public_sales_returns.sale_transaction_id != 0'
    end

    conditions << 'public_sale_transactions.status = 0' if @statuses[PAID]
    conditions << 'public_sale_transactions.status = 1' if @statuses[VOID]

    if @statuses[CANCELLED]
      subquery_sale_details = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                                         .by_datetime_range(@start_time, @end_time)
                                                                         .where(location_id: @location_ids)
                                                                         .select('sale_transaction_id, deleted')
                                                                         .limit_by_to_sql(:id, 1)

      subquery = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                            .from("(#{subquery_sale_details}) AS public_sale_detail_transactions")
                                                            .where('public_sale_detail_transactions.deleted = false')
                                                            .where('public_sale_detail_transactions.cancelled_quantity > 0')
                                                            .select('DISTINCT sale_transaction_id AS sale_transaction_id')
                                                            .to_sql

      @query = @query.joins(
        "LEFT JOIN (#{subquery}) filtered_public_sale_detail_transactions
        ON filtered_public_sale_detail_transactions.sale_transaction_id = public_sale_transactions.id"
      )
      conditions << 'filtered_public_sale_detail_transactions.sale_transaction_id != 0'
    end

    if conditions.present?
      conditions = "(#{conditions.join(' OR ')})"
      @query = @query.where(conditions)
    end

    @query
  end
  # rubocop:enable Metrics/MethodLength
end
