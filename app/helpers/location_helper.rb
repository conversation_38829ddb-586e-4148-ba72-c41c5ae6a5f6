module LocationHelper
  def self.build_order_delivery_location(location:, type:)
    {
      id: location['id'],
      name: location['name'],
      initial: location['initial'],
      branch_type: location['branch_type'],
      is_franchise: location['is_franchise'],
      brand_id: location['brand_id'],
      type: type
    }
  end

  # TODO: Merge 3 functions order location builder
  # rubocop:disable Metrics/MethodLength
  def self.build_order_location_detail_location_to(order, current_user)
    current_location_to = order.location_to
    country = ISO3166::Country.find_country_by_country_code order.location_to_contact_number_country_code
    contact_number_country = TimezoneHelper.build_phone_country(country)

    must_show = !order.order_fulfillment?
    mask_third_party_by_location = order.mask_third_party_location_and_preview_order_fulfillment?(current_user, must_show)

    location_attributes = if order.location_to_type == 'Location' && !mask_third_party_by_location
                            {
                              public_contact_number: current_location_to.public_contact_number,
                              public_contact_number_country_code: current_location_to.public_contact_number_country_code,
                              branch_type: current_location_to.branch_type,
                              is_franchise: current_location_to.is_franchise,
                              franchise_pic_name: current_location_to.franchise_pic_name,
                              tax_company_registration_no: current_location_to.tax_company_registration_no,
                              tax_identification_no: current_location_to.tax_identification_no
                            }
                          else
                            {}
                          end
    {
      id: order.location_to_id,
      type: order.location_to_type,
      brand_id: current_location_to.brand_id,
      name: mask_third_party_by_location ? I18n.t('orders.third_party_location') : order.location_to_name,
      shipping_address: mask_third_party_by_location ? '' : order.location_to_shipping_address,
      city: mask_third_party_by_location ? '' : order.location_to_city,
      province: mask_third_party_by_location ? '' : order.location_to_province,
      country: mask_third_party_by_location ? '' : order.location_to_country,
      postal_code: mask_third_party_by_location ? '' : order.location_to_postal_code,
      contact_number_country: mask_third_party_by_location ? '' : contact_number_country,
      contact_number: mask_third_party_by_location ? '' : order.location_to_contact_number,
      contact_number_country_code: mask_third_party_by_location ? '' : order.location_to_contact_number_country_code
    }.merge(location_attributes)
  end
  # rubocop:enable Metrics/MethodLength

  def self.build_order_location_detail_location_from(order)
    current_location_from = order.location_from
    country = ISO3166::Country.find_country_by_country_code order.location_from_contact_number_country_code
    contact_number_country = TimezoneHelper.build_phone_country(country)

    location_attributes = if order.location_from_type == 'Location'
                            {
                              public_contact_number: current_location_from.public_contact_number,
                              public_contact_number_country_code: current_location_from.public_contact_number_country_code,
                              branch_type: current_location_from.branch_type,
                              is_franchise: current_location_from.is_franchise,
                              franchise_pic_name: current_location_from.franchise_pic_name,
                              tax_company_registration_no: current_location_from.tax_company_registration_no,
                              tax_identification_no: current_location_from.tax_identification_no
                            }
                          else
                            {}
                          end

    LocationHelper.build_object_location_from(order, contact_number_country).merge(location_attributes)
  end

  # rubocop:disable Metrics/MethodLength
  def self.build_order_location_detail_fulfillment_location(order)
    current_location, country_code = if order.order_fulfillment?
                                       [order.fulfillment_location, order.fulfillment_location_contact_number_country_code]
                                     else
                                       [order.location_from, order.location_from_contact_number_country_code]
                                     end
    country = ISO3166::Country.find_country_by_country_code country_code
    contact_number_country = TimezoneHelper.build_phone_country(country)

    location_attributes = if order.location_from_type == 'Location'
                            {
                              public_contact_number: current_location.public_contact_number,
                              public_contact_number_country_code: current_location.public_contact_number_country_code,
                              branch_type: current_location.branch_type,
                              is_franchise: current_location.is_franchise,
                              franchise_pic_name: current_location.franchise_pic_name,
                              tax_company_registration_no: current_location.tax_company_registration_no,
                              tax_identification_no: current_location.tax_identification_no
                            }
                          else
                            {}
                          end

    location = if order.order_fulfillment?
                 {
                   id: order.fulfillment_location_id,
                   type: 'Location',
                   brand_id: order.fulfillment_location.brand_id,
                   name: order.fulfillment_location_name,
                   shipping_address: order.fulfillment_location_shipping_address,
                   city: order.fulfillment_location_city,
                   province: order.fulfillment_location_province,
                   country: order.fulfillment_location_country,
                   postal_code: order.fulfillment_location_postal_code,
                   contact_number_country: contact_number_country,
                   contact_number: order.fulfillment_location_contact_number,
                   contact_number_country_code: order.fulfillment_location_contact_number_country_code
                 }
               else
                 LocationHelper.build_object_location_from(order, contact_number_country)
               end

    location.merge(location_attributes)
  end
  # rubocop:enable Metrics/MethodLength

  def self.build_object_location_from(order, contact_number_country)
    {
      id: order.location_from_id,
      type: order.location_from_type,
      brand_id: order.location_from.brand_id,
      name: order.location_from_name,
      shipping_address: order.location_from_shipping_address,
      city: order.location_from_city,
      province: order.location_from_province,
      country: order.location_from_country,
      postal_code: order.location_from_postal_code,
      contact_number_country: contact_number_country,
      contact_number: order.location_from_contact_number,
      contact_number_country_code: order.location_from_contact_number_country_code
    }
  end
end
