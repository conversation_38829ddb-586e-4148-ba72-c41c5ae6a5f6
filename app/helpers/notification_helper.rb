# rubocop:disable Metrics/ModuleLength, Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity
module NotificationHelper
  IMPORT_ACTION_CUSTOMER = 'customer'.freeze
  IMPORT_ACTION_CUSTOMER_POINT = 'customer_point'.freeze
  IMPORT_ACTION_STOCK_ADJUSTMENT = 'stock_adjustment'.freeze
  IMPORT_ACTION_STOCK_OPENING = 'stock_opening'.freeze
  IMPORT_ACTION_PRODUCT = 'product'.freeze
  IMPORT_ACTION_DAILY_SALE = 'daily_sale'.freeze
  IMPORT_ACTION_ORDER_TRANSACTION = 'order_transaction'.freeze
  IMPORT_ACTION_RECIPE_STORAGE_SECTIONS = 'recipe_storage_sections'.freeze
  IMPORT_ACTION_MONEY_MOVEMENT = 'money_movement'.freeze
  IMPORT_ACTION_RECIPE = 'recipe'.freeze
  IMPORT_ACTION_STOCK_IN = 'stock_in'.freeze
  IMPORT_ACTION_STOCK_OUT = 'stock_out'.freeze

  ORDER_ACTION_CREATE = 'create'.freeze
  ORDER_ACTION_UPDATE = 'update'.freeze
  ORDER_ACTION_REMIND_PAYMENT = 'order_remind_payment'.freeze
  ORDER_ACTION_REMIND_SHIPPING_FEE_PAYMENT = 'order_remind_shipping_fee_payment'.freeze
  ORDER_ACTION_ADDED_SHIPPING_FEE = 'order_added_shipping_fee'.freeze
  ORDER_ACTION_PAID_SHIPPING_FEE = 'order_paid_shipping_fee'.freeze
  AUTOVOID_ORDER_REMINDER = 'autovoid_order_reminder'.freeze
  AUTOVOID_ORDER_COMPLETION_ALERT = 'autovoid_order_completion_alert'.freeze

  ORDER_ACTION_PAID_INTERNAL = 'order_paid'.freeze
  ORDER_ACTION_PAID_EXTERNAL = 'order_paid'.freeze
  ORDER_ACTION_PAID_CUSTOMER = 'order_paid'.freeze
  ORDER_ACTION_BUYER_PAID = 'order_buyer_paid'.freeze
  ORDER_ACTION_REMIND_LIMIT_INTERNAL = 'order_remind_limit'.freeze
  ORDER_ACTION_REMIND_LIMIT_EXTERNAL = 'order_remind_limit'.freeze
  ORDER_ACTION_REMIND_LIMIT_CUSTOMER = 'order_remind_limit'.freeze
  ORDER_ACTION_UPDATE_SHIPPING_FEE = 'update_shipping_fee'.freeze
  ORDER_ACTION_UPDATE_PRICES = 'update_prices'.freeze
  ORDER_ACTION_APPROVE_INTERNAL = 'approve'.freeze
  ORDER_ACTION_APPROVE_EXTERNAL = 'approve'.freeze
  ORDER_ACTION_APPROVE_CUSTOMER = 'approve'.freeze
  ORDER_ACTION_AUTO_APPROVE = 'approve'.freeze
  ORDER_ACTION_VOID_INTERNAL = 'void'.freeze
  ORDER_ACTION_VOID_EXTERNAL = 'void'.freeze
  ORDER_ACTION_VOID_CUSTOMER = 'void'.freeze
  ORDER_ACTION_CLOSE = 'close'.freeze
  ORDER_ACTION_MANUAL_REFUND_INTERNAL = 'manual_refund'.freeze
  ORDER_ACTION_MANUAL_REFUND_EXTERNAL = 'manual_refund'.freeze
  ORDER_ACTION_MANUAL_REFUND_CUSTOMER = 'manual_refund'.freeze
  ORDER_ACTION_ORDER_APPROVAL_REMINDER_CUSTOMER = 'order_approval_reminder_customer'.freeze
  ORDER_ACTION_ORDER_APPROVAL_REMINDER_EXTERNAL = 'order_approval_reminder_external'.freeze
  ORDER_ACTION_ORDER_APPROVAL_REMINDER_INTERNAL = 'order_approval_reminder_internal'.freeze

  COMPLETED_BULK_INTERNAL_PRICE_UPDATE = 'completed_bulk_internal_price_update'.freeze

  INCOMING_DELIVERY_ACTION_RECEIVE = 'incoming_delivery_receive'.freeze

  DELIVERY_ACTION_CREATE = 'create'.freeze
  DELIVERY_ACTION_UPDATE = 'update'.freeze
  DELIVERY_ACTION_RECEIVE = 'receive'.freeze
  DELIVERY_ACTION_DESTROY = 'destroy'.freeze

  DELIVERY_RETURN_ACTION_CREATED = 'accepted'.freeze
  DELIVERY_RETURN_FULFILLMENT_ACTION_CREATED = 'delivery_return_fulfillment_accepted'.freeze
  DELIVERY_RETURN_ACTION_REJECTED = 'delivery_return_rejected'.freeze

  STOCK_ACTION_PAR = 'par'.freeze
  STOCK_TRANSFER = 'stock_transfer'.freeze

  TAKING_ACTION_CREATE = 'create'.freeze

  GRAB_FOOD = 'grab_food'.freeze
  GO_FOOD = 'go_food'.freeze
  SHOPEE_FOOD = 'shopee_food'.freeze

  ROYALTY_TRANSACTION_CREATION_FAILED = 'royalty_transaction_creation_failed'.freeze
  ROYALTY_TRANSACTION_CREATION_COMPLETED = 'royalty_transaction_creation_completed'.freeze

  def self.object_class_by_type(notification_type)
    notif_types = Notification.notification_types

    {
      notif_types.key(notif_types['product_import']) => 'ImportData',
      notif_types.key(notif_types['stock_adjustment_import']) => 'ImportData',
      notif_types.key(notif_types['stock_opening_import']) => 'ImportData',
      notif_types.key(notif_types['daily_sale_import']) => 'ImportData',
      notif_types.key(notif_types['order_transaction_import']) => 'ImportData',
      notif_types.key(notif_types['customer_import']) => 'ImportData',
      notif_types.key(notif_types['customer_point_import']) => 'ImportData',
      notif_types.key(notif_types['recipe_storage_sections_import']) => 'ImportData',
      notif_types.key(notif_types['money_movement_import']) => 'ImportData',
      notif_types.key(notif_types['app_notif_procurement_order_incoming_order']) => 'OrderTransaction',
      notif_types.key(notif_types['app_notif_procurement_order_approve_void']) => 'OrderTransaction',
      notif_types.key(notif_types['procurement_delivery_incoming']) => 'DeliveryTransaction',
      notif_types.key(notif_types['procurement_delivery_received']) => 'DeliveryTransaction',
      notif_types.key(notif_types['product_min_quantity']) => 'Product',
      notif_types.key(notif_types['app_notif_report_par']) => 'Product',
      notif_types.key(notif_types['completed_bulk_update_internal_price']) => 'Restaurant::Models::BulkUpdateInternalPriceLog',
      notif_types.key(notif_types['online_ordering_incoming_order']) => 'CustomerOrder',
      notif_types.key(notif_types['pos_taking']) => 'Taking',
      notif_types.key(notif_types['grab_food_failed_promo_sync']) => 'Promo',
      notif_types.key(notif_types['go_food_failed_promo_sync']) => 'Promo',
      notif_types.key(notif_types['shopee_food_failed_promo_sync']) => 'Promo',
      notif_types.key(notif_types['order_transaction_remind_payment']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_remind_shipping_fee_payment']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_added_shipping_fee']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_paid_shipping_fee']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_paid']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_buyer_paid']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_remind_limit']) => 'OrderTransaction',
      notif_types.key(notif_types['order_transaction_manual_refund']) => 'OrderTransaction',
      notif_types.key(notif_types['procurement_delivery_return_created']) => 'Restaurant::Models::DeliveryReturn',
      notif_types.key(notif_types['procurement_delivery_return_rejected']) => 'Restaurant::Models::DeliveryReturn',
      notif_types.key(notif_types['stock_transfer']) => 'StockTransfer',
      notif_types.key(notif_types['order_approval_reminder']) => 'OrderTransaction',
      notif_types.key(notif_types['autovoid_order_reminder']) => 'OrderTransaction',
      notif_types.key(notif_types['autovoid_order_completion_alert']) => 'OrderTransaction',
      notif_types.key(notif_types['royalty_transaction_creation_failed']) => 'RoyaltyTransactionCreationRequest',
      notif_types.key(notif_types['royalty_transaction_creation_completed']) => 'RoyaltyTransactionCreationRequest',
      notif_types.key(notif_types['recipe_import']) => 'ImportData',
      notif_types.key(notif_types['stock_in_import']) => 'ImportData',
      notif_types.key(notif_types['stock_out_import']) => 'ImportData'
    }[notification_type]
  end

  # rubocop:disable Metrics/PerceivedComplexity
  def self.generate_notification_resource_type(notification)
    if notification.associated_type == ImportData.name
      child_class_name = notification.associated.associated_type
      case child_class_name
      when OrderTransaction.name
        return "#{ImportData.name}#{OrderTransaction.name}"
      when Product.name
        return "#{ImportData.name}#{Product.name}"
      when Customer.name
        return "#{ImportData.name}#{Customer.name}"
      when CustomerPoint.name
        return "#{ImportData.name}#{CustomerPoint.name}"
      when DailySale.name
        return "#{ImportData.name}#{DailySale.name}"
      when StockAdjustment.name
        return "#{ImportData.name}#{StockAdjustment.name}"
      when StockOpening.name
        return "#{ImportData.name}#{StockOpening.name}"
      when 'RecipeStorageSections'
        return "#{ImportData.name}RecipeStorageSections"
      when MoneyMovement.name
        return "#{ImportData.name}#{MoneyMovement.name}"
      when Recipe.name
        return "#{ImportData.name}#{Recipe.name}"
      when StockIn.name
        return "#{ImportData.name}#{StockIn.name}"
      when StockOut.name
        return "#{ImportData.name}#{StockOut.name}"
      end
    end

    if notification.app_notif_report_par?
      'ReportPar'
    elsif notification.grab_food_failed_menu_sync?
      'GrabFoodFailedMenuSync'
    elsif notification.grab_food_failed_promo_sync? || notification.go_food_failed_promo_sync? || notification.shopee_food_failed_promo_sync?
      'Promo'
    elsif notification.grab_food_failed_order_sync?
      'GrabFoodFailedOrderSync'
    elsif notification.go_food_failed_menu_sync?
      'GoFoodFailedMenuSync'
    elsif notification.go_food_failed_order_sync?
      'GoFoodFailedOrderSync'
    elsif notification.shopee_food_failed_menu_sync?
      'ShopeeFoodFailedMenuSync'
    elsif notification.shopee_food_failed_order_sync?
      'ShopeeFoodFailedOrderSync'
    elsif notification.order_transaction_paid? || notification.order_transaction_buyer_paid?
      'ProcurementPayment'
    elsif notification.order_transaction_remind_payment? || notification.order_transaction_remind_shipping_fee_payment?
      'ProcurementPaymentReminder'
    elsif notification.order_transaction_added_shipping_fee?
      'OrderShippingFeeAdded'
    elsif notification.order_transaction_paid_shipping_fee?
      'OrderShippingFeePaid'
    elsif notification.associated_type == 'Restaurant::Models::DeliveryReturn'
      notification.associated_type.demodulize
    elsif notification.associated_type == 'Restaurant::Models::BulkUpdateInternalPriceLog'
      notification.associated_type.demodulize
    elsif notification.bulk_product_activation_success?
      'BulkProductActivationSuccess'
    elsif notification.bulk_product_activation_failed?
      'BulkProductActivationFailed'
    else
      notification.associated_type
    end
  end
  # rubocop:enable Metrics/PerceivedComplexity

  def self.generate_payload(notification, current_user, location)
    active_language = current_user.active_language || 'en'
    message = JSON.parse(notification.notif_message)[active_language]

    {
      title: message,
      description: { data: location&.name },
      filters: notification.generate_filter,
      resource_type: generate_notification_resource_type(notification),
      resource_id: notification.associated_id
    }
  end

  def self.build_notification_detail(notification, current_user, brand_uuid, location = nil)
    location ||= notification.location
    click_action = NotificationHelper.generate_push_notif_url(notification)
    brand = notification.brand

    detail = {
      id: notification.id,
      created_at: notification.created_at,
      active: notification.active,
      brand_uuid: brand_uuid,
      large_icon: nil,
      icon: brand.logo_url,
      click_action: click_action,
      location: location.nil? ? {} : { id: location.id, name: location.name },
      payload: NotificationHelper.generate_payload(notification, current_user, location)
    }

    detail[:payload] = detail[:payload].merge({ no_show: true }) if notification.metadata['creation_success'] == true
    detail
  end

  def self.build_messaging_notification_message_for_web(brand:, brand_uuid:, cid:, message_id: '', payload: nil)
    click_action_url = "#{Rails.configuration.x.client_messaging_url}/#{cid}"
    click_action_url += "?messageId=#{message_id}" if message_id.present?

    {
      click_action: click_action_url,
      icon: brand.logo_url,
      brand_uuid: brand_uuid,
      payload: payload
    }
  end

  def self.generate_push_notif_url(notification)
    return '' if notification.nil?

    "#{Restaurant::Constants::CLIENT_APP_URL}/notifications/#{notification.id}"
  end

  def self.permission_needed(notification_type, action)
    notif_types = Notification.notification_types

    case notification_type
    when notif_types.key(notif_types['product_import'])
      product_import_permission_needed(action)
    when notif_types.key(notif_types['customer_import'])
      customer_import_permission_needed(action)
    when notif_types.key(notif_types['customer_point_import'])
      customer_import_permission_needed(action)
    when notif_types.key(notif_types['daily_sale_import'])
      daily_sale_import_permission_needed(action)
    when notif_types.key(notif_types['order_transaction_import'])
      order_transaction_import_permission_needed(action)
    when notif_types.key(notif_types['stock_adjustment_import'])
      stock_adjustment_import_permission_needed(action)
    when notif_types.key(notif_types['stock_opening_import'])
      stock_opening_import_permission_needed(action)
    when notif_types.key(notif_types['recipe_storage_sections_import'])
      recipe_storage_sections_import_permission_needed
    when notif_types.key(notif_types['money_movement_import'])
      money_movement_import_permission_needed(action)
    when notif_types.key(notif_types['app_notif_procurement_order_incoming_order']),
         notif_types.key(notif_types['app_notif_procurement_order_approve_void']),
         notif_types.key(notif_types['order_transaction_paid']),
         notif_types.key(notif_types['order_transaction_buyer_paid']),
         notif_types.key(notif_types['order_transaction_remind_limit']),
         notif_types.key(notif_types['order_transaction_remind_payment']),
         notif_types.key(notif_types['order_transaction_remind_shipping_fee_payment']),
         notif_types.key(notif_types['order_transaction_added_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid_shipping_fee']),
         notif_types.key(notif_types['order_transaction_manual_refund']),
         notif_types.key(notif_types['order_approval_reminder']),
         notif_types.key(notif_types['autovoid_order_reminder']),
         notif_types.key(notif_types['autovoid_order_completion_alert'])
      order_permission_needed(action)
    when notif_types.key(notif_types['procurement_delivery_incoming']),
         notif_types.key(notif_types['procurement_delivery_received'])
      delivery_permission_needed(action)
    when notif_types.key(notif_types['pos_taking'])
      pos_taking_permission_needed(action)
    when notif_types.key(notif_types['app_notif_report_par']),
         notif_types.key(notif_types['stock_transfer'])
      stock_permission_needed(action)
    when notif_types.key(notif_types['product_min_quantity'])
      product_min_quantity_permission_needed
    when notif_types.key(notif_types['online_ordering_incoming_order'])
      online_ordering_incoming_order_permission_needed
    when notif_types.key(notif_types['grab_food_failed_menu_sync']),
         notif_types.key(notif_types['grab_food_failed_promo_sync']),
         notif_types.key(notif_types['go_food_failed_menu_sync']),
         notif_types.key(notif_types['go_food_failed_promo_sync']),
         notif_types.key(notif_types['shopee_food_failed_menu_sync']),
         notif_types.key(notif_types['shopee_food_failed_promo_sync'])
      food_delivery_integration_permission_needed
    when notif_types.key(notif_types['grab_food_failed_order_sync']),
         notif_types.key(notif_types['go_food_failed_order_sync']),
         notif_types.key(notif_types['shopee_food_failed_order_sync'])
      approve_online_delivery
    when notif_types.key(notif_types['procurement_delivery_return_created']),
      notif_types.key(notif_types['procurement_delivery_return_rejected'])
      delivery_return_permission_needed(action)
    when notif_types.key(notif_types['royalty_transaction_creation_failed']),
      notif_types.key(notif_types['royalty_transaction_creation_completed'])
      royalty_transaction_permission_needed
    when notif_types.key(notif_types['bulk_product_activation_success']),
      notif_types.key(notif_types['bulk_product_activation_failed'])
      product_activation_permission_needed
    when notif_types.key(notif_types['recipe_import'])
      recipe_import_permission_needed(action)
    when notif_types.key(notif_types['stock_in_import'])
      stock_in_or_out_import_permission_needed(action)
    when notif_types.key(notif_types['stock_out_import'])
      stock_in_or_out_import_permission_needed(action)
    end
  end

  def self.royalty_transaction_permission_needed
    '{"royalty_schema":{"create": true}}'
  end

  def self.product_activation_permission_needed
    '{"product":{"deactivate": true, "reactivate": true}}'
  end

  def self.product_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_PRODUCT
      '{"import":{"product":{"create": true }}}'
    end
  end

  def self.money_movement_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_MONEY_MOVEMENT
      '{"import":{"money_movement":{"create": true }}}'
    end
  end

  def self.stock_adjustment_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_STOCK_ADJUSTMENT
      '{"import":{"stock_adjustment":{"create": true }}}'
    end
  end

  def self.stock_opening_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_STOCK_OPENING
      '{"import":{"stock_opening":{"create": true }}}'
    end
  end

  def self.daily_sale_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_DAILY_SALE
      '{"import":{"daily_sale":{"create": true }}}'
    end
  end

  def self.order_transaction_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_ORDER_TRANSACTION
      '{"import":{"order_transaction":{"create": true }}}'
    end
  end

  def self.customer_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_CUSTOMER
      '{"import":{"customer":{"create": true }}}'
    end
  end

  def self.recipe_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_RECIPE
      '{"import":{"recipe":{"create": true}}}'
    end
  end

  def self.recipe_storage_sections_import_permission_needed
    '{"storage_section":{"update": true }}'
  end

  def self.stock_in_or_out_import_permission_needed(action)
    case action
    when NotificationHelper::IMPORT_ACTION_STOCK_IN
      '{"import":{"stock_in":{"create": true}}}'
    when NotificationHelper::IMPORT_ACTION_STOCK_OUT
      '{"import":{"stock_out":{"create": true}}}'
    end
  end

  def self.stock_permission_needed(action)
    case action
    when NotificationHelper::STOCK_ACTION_PAR
      '{"product":{"create": true}}'
    when NotificationHelper::STOCK_TRANSFER
      '{"stock_transfer":{"create": true}}'
    end
  end

  def self.order_permission_needed(action)
    case action
    when NotificationHelper::ORDER_ACTION_CREATE,
         NotificationHelper::AUTOVOID_ORDER_REMINDER,
         NotificationHelper::AUTOVOID_ORDER_COMPLETION_ALERT
      '{"order":{"create": true}}'
    when NotificationHelper::ORDER_ACTION_UPDATE
      '{"order":{"update": true}}'
    when NotificationHelper::ORDER_ACTION_REMIND_PAYMENT
      '{"order":{"index": true}}'
    when NotificationHelper::ORDER_ACTION_REMIND_SHIPPING_FEE_PAYMENT
      '{"order":{"index": true}}'
    when NotificationHelper::ORDER_ACTION_ADDED_SHIPPING_FEE
      '{"order":{"index": true}}'
    when NotificationHelper::ORDER_ACTION_PAID_SHIPPING_FEE
      '{"order":{"index": true}}'
    when NotificationHelper::ORDER_ACTION_PAID_INTERNAL
      '{"order":{"approve_internal": true}}'
    when NotificationHelper::ORDER_ACTION_PAID_EXTERNAL
      '{"order":{"approve_external": true}}'
    when NotificationHelper::ORDER_ACTION_PAID_CUSTOMER
      '{"order":{"approve_customer": true}}'
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_INTERNAL
      '{"order":{"approve_internal": true}}'
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_EXTERNAL
      '{"order":{"approve_external": true}}'
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_CUSTOMER
      '{"order":{"approve_customer": true}}'
    when NotificationHelper::ORDER_ACTION_UPDATE_SHIPPING_FEE,
         NotificationHelper::ORDER_ACTION_UPDATE_PRICES
      '{"order":{"update": true}}'
    when NotificationHelper::ORDER_ACTION_AUTO_APPROVE
      '{"order":{"index": true}}'
    when NotificationHelper::ORDER_ACTION_APPROVE_INTERNAL
      '{"order":{"approve_internal": true}}'
    when NotificationHelper::ORDER_ACTION_APPROVE_EXTERNAL
      '{"order":{"approve_external": true}}'
    when NotificationHelper::ORDER_ACTION_APPROVE_CUSTOMER
      '{"order":{"approve_customer": true}}'
    when NotificationHelper::ORDER_ACTION_VOID_INTERNAL
      '{"order":{"void_internal": true}}'
    when NotificationHelper::ORDER_ACTION_VOID_EXTERNAL
      '{"order":{"void_external": true}}'
    when NotificationHelper::ORDER_ACTION_VOID_CUSTOMER
      '{"order":{"void_customer": true}}'
    when NotificationHelper::ORDER_ACTION_CLOSE
      '{"order":{"close": true}}'
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_INTERNAL
      '{"order":{"void_internal": true}}'
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_EXTERNAL
      '{"order":{"void_external": true}}'
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_CUSTOMER
      '{"order":{"void_customer": true}}'
    when NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_INTERNAL
      '{"order":{"approve_internal": true}}'
    when NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_CUSTOMER
      '{"order":{"approve_customer": true}}'
    when NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_EXTERNAL
      '{"order":{"approve_external": true}}'
    end
  end

  def self.delivery_permission_needed(action)
    case action
    when NotificationHelper::DELIVERY_ACTION_CREATE
      '{"delivery":{"create": true}}'
    when NotificationHelper::DELIVERY_ACTION_UPDATE
      '{"delivery":{"update": true}}'
    when NotificationHelper::DELIVERY_ACTION_DESTROY
      '{"delivery":{"destroy": true}}'
    when NotificationHelper::DELIVERY_ACTION_RECEIVE
      '{"delivery":{"receive": true}}'
    when NotificationHelper::INCOMING_DELIVERY_ACTION_RECEIVE
      '{"delivery":{"receive": true}}'
    end
  end

  def self.delivery_return_permission_needed(action)
    case action
    when NotificationHelper::DELIVERY_RETURN_FULFILLMENT_ACTION_CREATED
      '{"delivery_return":{"create": true}}'
    when NotificationHelper::DELIVERY_RETURN_ACTION_CREATED
      '{"delivery_return":{"create": true}}'
    when NotificationHelper::DELIVERY_RETURN_ACTION_REJECTED
      '{"delivery_return":{"create": true}}'
    end
  end

  def self.pos_taking_permission_needed(action)
    case action
    when NotificationHelper::TAKING_ACTION_CREATE
      '{"report":{"taking": true}}'
    end
  end

  def self.product_min_quantity_permission_needed
    '{"product":{"stock_show": true}}'
  end

  def self.online_ordering_incoming_order_permission_needed
    '{"sale_transaction":{"create": true}}'
  end

  def self.food_delivery_integration_permission_needed
    '{"online_delivery":{"food_delivery_integration": true}}'
  end

  def self.approve_online_delivery
    '{"online_delivery":{"approve": true}}'
  end

  def self.generate_push_notif_message(notification_type, action, locale, params)
    notif_types = Notification.notification_types

    case notification_type
    when notif_types.key(notif_types['product_import']),
         notif_types.key(notif_types['customer_import']),
         notif_types.key(notif_types['customer_point_import']),
         notif_types.key(notif_types['daily_sale_import']),
         notif_types.key(notif_types['stock_adjustment_import']),
         notif_types.key(notif_types['stock_opening_import']),
         notif_types.key(notif_types['order_transaction_import']),
         notif_types.key(notif_types['recipe_storage_sections_import']),
         notif_types.key(notif_types['money_movement_import']),
         notif_types.key(notif_types['recipe_import']),
         notif_types.key(notif_types['stock_in_import']),
         notif_types.key(notif_types['stock_out_import'])
      generate_push_notif_import(action, locale, params)
    when notif_types.key(notif_types['app_notif_procurement_order_incoming_order']),
         notif_types.key(notif_types['app_notif_procurement_order_approve_void']),
         notif_types.key(notif_types['order_transaction_remind_payment']),
         notif_types.key(notif_types['order_transaction_remind_shipping_fee_payment']),
         notif_types.key(notif_types['order_transaction_added_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid']),
         notif_types.key(notif_types['order_transaction_remind_limit']),
         notif_types.key(notif_types['order_transaction_buyer_paid']),
         notif_types.key(notif_types['order_transaction_manual_refund']),
         notif_types.key(notif_types['order_approval_reminder'])
      generate_push_notif_order(action, locale, params)
    when notif_types.key(notif_types['procurement_delivery_incoming']),
         notif_types.key(notif_types['procurement_delivery_received'])
      generate_push_notif_delivery(action, locale, params)
    when notif_types.key(notif_types['completed_bulk_update_internal_price'])
      generate_push_notif_completed_bulk_update_internal_price(action, locale, params)
    when notif_types.key(notif_types['procurement_delivery_return_created']),
      notif_types.key(notif_types['procurement_delivery_return_rejected'])
      generate_push_notif_delivery_return(action, locale, params)
    when notif_types.key(notif_types['app_notif_report_par']),
         notif_types.key(notif_types['stock_transfer'])
      generate_push_notif_stock(action, locale, params)
    when notif_types.key(notif_types['pos_taking'])
      generate_push_notif_taking(action, locale, params)
    when notif_types.key(notif_types['grab_food_failed_menu_sync'])
      generate_push_notif_grab_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['grab_food_failed_promo_sync'])
      generate_push_notif_grab_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['grab_food_failed_order_sync'])
      generate_push_notif_grab_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_menu_sync'])
      generate_push_notif_go_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_promo_sync'])
      generate_push_notif_go_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_order_sync'])
      generate_push_notif_go_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_menu_sync'])
      generate_push_notif_shopee_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_promo_sync'])
      generate_push_notif_shopee_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_order_sync'])
      generate_push_notif_shopee_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['royalty_transaction_creation_failed'])
      generate_push_notif_royalty_creation_failed(locale, params)
    when notif_types.key(notif_types['royalty_transaction_creation_completed'])
      generate_push_notif_royalty_creation_completed(locale, params)
    else
      # TODO: implement the other later
      raise NotImplementedError
    end
  end

  def self.generate_push_notif_import(action, locale, params)
    case action
    when NotificationHelper::IMPORT_ACTION_PRODUCT
      [I18n.t('push_notif.import_data.product.header', locale: locale),
       I18n.t('push_notif.import_data.product.content', count: params[:count], total_count: params[:total_count], locale: locale)]
    when NotificationHelper::IMPORT_ACTION_CUSTOMER
      [I18n.t('push_notif.import_data.customer.header', locale: locale),
       I18n.t('push_notif.import_data.customer.content', count: params[:count], total_count: params[:total_count], locale: locale)]
    when NotificationHelper::IMPORT_ACTION_CUSTOMER_POINT
      [I18n.t('push_notif.import_data.customer_point.header', locale: locale),
       I18n.t('push_notif.import_data.customer_point.content', count: params[:count], total_count: params[:total_count], locale: locale)]
    when NotificationHelper::IMPORT_ACTION_STOCK_ADJUSTMENT
      if params[:fail_reason].present?
        [I18n.t('push_notif.import_data.stock_adjustment.header', status: 'failed', location_name: params[:location_name], locale: locale),
         I18n.t('push_notif.import_data.stock_adjustment.content_failure', reason: params[:fail_reason], locale: locale)]
      else
        [I18n.t('push_notif.import_data.stock_adjustment.header', status: 'success', location_name: params[:location_name], locale: locale),
         I18n.t('push_notif.import_data.stock_adjustment.content_success', count: params[:count], locale: locale)]
      end
    when NotificationHelper::IMPORT_ACTION_STOCK_OPENING
      if params[:success]
        [I18n.t('push_notif.import_data.stock_opening.header', status: 'success', location_name: params[:location_name], locale: locale),
         I18n.t('push_notif.import_data.stock_opening.content_success', count: params[:count], locale: locale)]
      else
        [I18n.t('push_notif.import_data.stock_opening.header', status: 'failed', location_name: params[:location_name], locale: locale),
         I18n.t('push_notif.import_data.stock_opening.content_failure',
                reason: I18n.t('notification.import_data.stock_opening.failed', locale: locale), locale: locale)]
      end
    when NotificationHelper::IMPORT_ACTION_DAILY_SALE
      if params[:success]
        [I18n.t('push_notif.import_data.daily_sale.header', locale: locale),
         I18n.t('push_notif.import_data.daily_sale.content', date: params[:date], locale: locale)]
      else
        [I18n.t('push_notif.import_data.daily_sale_failed.header', locale: locale),
         I18n.t('push_notif.import_data.daily_sale_failed.content', date: params[:date], locale: locale)]
      end
    when NotificationHelper::IMPORT_ACTION_ORDER_TRANSACTION
      if params[:success]
        [I18n.t('push_notif.import_data.order_transaction.header', locale: locale),
         I18n.t('push_notif.import_data.order_transaction.content', date: params[:date], locale: locale)]
      else
        [I18n.t('push_notif.import_data.order_transaction_failed.header', locale: locale),
         I18n.t('push_notif.import_data.order_transaction_failed.content', date: params[:date], locale: locale)]
      end
    when NotificationHelper::IMPORT_ACTION_RECIPE_STORAGE_SECTIONS
      prefix = 'push_notif.import_data.recipe_storage_sections'
      prefix << '_with_error' if params[:count] != params[:total_count]

      [
        I18n.t("#{prefix}.header", location_name: params[:location_name], locale: locale),
        I18n.t("#{prefix}.content", count: params[:count], total_count: params[:total_count], locale: locale)
      ]
    when NotificationHelper::IMPORT_ACTION_MONEY_MOVEMENT
      prefix = 'push_notif.import_data.money_movement'
      prefix << '_with_error' if params[:count] != params[:total_count]

      [
        I18n.t("#{prefix}.header", locale: locale),
        I18n.t("#{prefix}.content", count: params[:count], total_count: params[:total_count], locale: locale)
      ]
    when NotificationHelper::IMPORT_ACTION_RECIPE
      prefix = 'push_notif.import_data.recipe'
      prefix << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      [I18n.t("#{prefix}.header", locale: locale),
       I18n.t("#{prefix}.content", count: params[:count], total_count: params[:total_count], locale: locale)]
    when NotificationHelper::IMPORT_ACTION_STOCK_IN
      prefix = 'push_notif.import_data.stock_in'
      prefix << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      [I18n.t("#{prefix}.header", locale: locale),
       I18n.t("#{prefix}.content", count: params[:count], total_count: params[:total_count], locale: locale)]
    when NotificationHelper::IMPORT_ACTION_STOCK_OUT
      prefix = 'push_notif.import_data.stock_out'
      prefix << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      [I18n.t("#{prefix}.header", locale: locale),
       I18n.t("#{prefix}.content", count: params[:count], total_count: params[:total_count], locale: locale)]
    end
  end

  def self.generate_push_notif_autovoid_order_reminder(locale:, notification_variables:)
    [
      I18n.t('push_notif.order_transaction.autovoid_reminder.header', locale: locale),
      I18n.t('push_notif.order_transaction.autovoid_reminder.content',
             order_no: notification_variables[:order_no],
             minutes: notification_variables[:minutes],
             locale: locale)
    ]
  end

  def self.generate_push_notif_autovoided_order(locale:, notification_variables:)
    [
      I18n.t('push_notif.order_transaction.autovoid_completion_alert.header', locale: locale),
      I18n.t('push_notif.order_transaction.autovoid_completion_alert.content',
             order_no: notification_variables[:order_no],
             autovoided_at: notification_variables[:autovoided_at],
             minutes: notification_variables[:minutes],
             locale: locale)
    ]
  end

  def self.generate_push_notif_order(action, locale, params)
    case action
    when NotificationHelper::ORDER_ACTION_CREATE
      [I18n.t('push_notif.order_transaction.create.header', locale: locale),
       I18n.t('push_notif.order_transaction.create.content', order_no: params[:order_no], location_name: params[:location_from_name], locale: locale)]
    when NotificationHelper::ORDER_ACTION_UPDATE
      [I18n.t('push_notif.order_transaction.update.header', locale: locale),
       I18n.t('push_notif.order_transaction.update.content', order_no: params[:order_no], location_name: params[:location_name], locale: locale)]
    when NotificationHelper::ORDER_ACTION_REMIND_PAYMENT
      [I18n.t('push_notif.order_transaction.remind_payment.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                    locale: locale),
       I18n.t('push_notif.order_transaction.remind_payment.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                     locale: locale)]
    when NotificationHelper::ORDER_ACTION_REMIND_SHIPPING_FEE_PAYMENT
      [I18n.t('push_notif.order_transaction.remind_shipping_fee_payment.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                                 locale: locale),
       I18n.t('push_notif.order_transaction.remind_shipping_fee_payment.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                                  locale: locale)]
    when NotificationHelper::ORDER_ACTION_ADDED_SHIPPING_FEE
      [I18n.t('push_notif.order_transaction.added_shipping_fee.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                        locale: locale),
       I18n.t('push_notif.order_transaction.added_shipping_fee.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                         locale: locale)]
    when NotificationHelper::ORDER_ACTION_PAID_SHIPPING_FEE
      [I18n.t('push_notif.order_transaction.paid_shipping_fee.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                       locale: locale),
       I18n.t('push_notif.order_transaction.paid_shipping_fee.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                        locale: locale)]
    when NotificationHelper::ORDER_ACTION_PAID_INTERNAL
      [I18n.t('push_notif.order_transaction.paid.header', order_no: params[:order_no], location_name: params[:location_to_name],
                                                          locale: locale),
       I18n.t('push_notif.order_transaction.paid.content', order_no: params[:order_no], location_name: params[:location_to_name],
                                                           locale: locale)]
    when NotificationHelper::ORDER_ACTION_PAID_EXTERNAL
      [I18n.t('push_notif.order_transaction.paid.header', order_no: params[:order_no], location_name: params[:location_to_name],
                                                          locale: locale),
       I18n.t('push_notif.order_transaction.paid.content', order_no: params[:order_no], location_name: params[:location_to_name],
                                                           locale: locale)]
    when NotificationHelper::ORDER_ACTION_PAID_CUSTOMER
      [I18n.t('push_notif.order_transaction.paid.header', order_no: params[:order_no], location_name: params[:location_to_name],
                                                          locale: locale),
       I18n.t('push_notif.order_transaction.paid.content', order_no: params[:order_no], location_name: params[:location_to_name],
                                                           locale: locale)]
    when NotificationHelper::ORDER_ACTION_BUYER_PAID
      [I18n.t('push_notif.order_transaction.buyer_paid.header', order_no: params[:order_no], location_name: params[:location_to_name],
                                                                locale: locale),
       I18n.t('push_notif.order_transaction.buyer_paid.content', order_no: params[:order_no], location_name: params[:location_to_name],
                                                                 locale: locale)]
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_INTERNAL
      [I18n.t('push_notif.order_transaction.remind_limit.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                  locale: locale),
       I18n.t('push_notif.order_transaction.remind_limit.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                   locale: locale)]
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_EXTERNAL
      [I18n.t('push_notif.order_transaction.remind_limit.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                  locale: locale),
       I18n.t('push_notif.order_transaction.remind_limit.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                   locale: locale)]
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_CUSTOMER
      [I18n.t('push_notif.order_transaction.remind_limit.header', order_no: params[:order_no], location_name: params[:location_name],
                                                                  locale: locale),
       I18n.t('push_notif.order_transaction.remind_limit.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                   locale: locale)]
    when NotificationHelper::ORDER_ACTION_UPDATE_SHIPPING_FEE
      [I18n.t('push_notif.order_transaction.update_shipping_fee.header', locale: locale),
       I18n.t('push_notif.order_transaction.update_shipping_fee.content', order_no: params[:order_no], location_name: params[:location_name],
                                                                          locale: locale)]
    when NotificationHelper::ORDER_ACTION_UPDATE_PRICES
      [I18n.t('push_notif.order_transaction.update_prices.header', order_no: params[:order_no], locale: locale),
       I18n.t('push_notif.order_transaction.update_prices.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_AUTO_APPROVE,
         NotificationHelper::ORDER_ACTION_APPROVE_INTERNAL,
         NotificationHelper::ORDER_ACTION_APPROVE_EXTERNAL,
         NotificationHelper::ORDER_ACTION_APPROVE_CUSTOMER
      [I18n.t('push_notif.order_transaction.approve.header', locale: locale),
       I18n.t('push_notif.order_transaction.approve.content', order_no: params[:order_no], location_to_name: params[:location_to_name],
                                                              location_from_name: params[:location_from_name], locale: locale)]
    when NotificationHelper::ORDER_ACTION_VOID_INTERNAL
      [I18n.t('push_notif.order_transaction.void.header', locale: locale),
       I18n.t('push_notif.order_transaction.void.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_VOID_EXTERNAL
      [I18n.t('push_notif.order_transaction.void.header', locale: locale),
       I18n.t('push_notif.order_transaction.void.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_VOID_CUSTOMER
      [I18n.t('push_notif.order_transaction.void.header', locale: locale),
       I18n.t('push_notif.order_transaction.void.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_CLOSE
      [I18n.t('push_notif.order_transaction.close.header', locale: locale),
       I18n.t('push_notif.order_transaction.close.content', order_no: params[:order_no], location_name: params[:location_name],
                                                            locale: locale)]
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_INTERNAL
      [I18n.t('push_notif.order_transaction.manual_refund.header', locale: locale),
       I18n.t('push_notif.order_transaction.manual_refund.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_EXTERNAL
      [I18n.t('push_notif.order_transaction.manual_refund.header', locale: locale),
       I18n.t('push_notif.order_transaction.manual_refund.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_CUSTOMER
      [I18n.t('push_notif.order_transaction.manual_refund.header', locale: locale),
       I18n.t('push_notif.order_transaction.manual_refund.content', order_no: params[:order_no], locale: locale)]
    when NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_EXTERNAL,
         NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_CUSTOMER,
         NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_INTERNAL
      location_name = params[:vendor_name].presence || params[:location_name]
      [I18n.t('push_notif.order_transaction.order_approval_reminder.header', locale: locale),
       I18n.t('push_notif.order_transaction.order_approval_reminder.content', order_no: params[:order_no],
                                                                              location_name: location_name,
                                                                              locale: locale)]
    end
  end

  def self.generate_push_notif_completed_bulk_update_internal_price(action, locale, params)
    case action
    when NotificationHelper::COMPLETED_BULK_INTERNAL_PRICE_UPDATE
      message = if params[:user_id_and_location_ids].length == 1
                  I18n.t('push_notif.bulk_update_internal_price.completed.single_location_id',
                         locale: locale,
                         count: params[:user_id_and_location_ids].length, location_to_name: params[:user_id_and_location_ids].first.last)
                else
                  I18n.t('push_notif.bulk_update_internal_price.completed.multiple_location_ids',
                         locale: locale,
                         count: params[:user_id_and_location_ids].length)
                end

      [message, message]
    end
  end

  # web push notifications
  def self.generate_push_notif_delivery(action, locale, params)
    case action
    when NotificationHelper::DELIVERY_ACTION_CREATE
      [I18n.t('push_notif.delivery.create.header', locale: locale),
       I18n.t('push_notif.delivery.create.content', delivery_no: params[:delivery_no],
                                                    location_name: params[:location_name], locale: locale)]
    when NotificationHelper::DELIVERY_ACTION_UPDATE
      [I18n.t('push_notif.delivery.update.header', locale: locale),
       I18n.t('push_notif.delivery.update.content', delivery_no: params[:delivery_no],
                                                    location_name: params[:location_name], locale: locale)]
    when NotificationHelper::DELIVERY_ACTION_RECEIVE
      if params[:partial_receive]
        [I18n.t('push_notif.delivery.incomplete.header', locale: locale),
         I18n.t('push_notif.delivery.incomplete.content', delivery_no: params[:delivery_no],
                                                          location_name: params[:location_from_name], locale: locale)]
      else
        [I18n.t('push_notif.delivery.receive.header', locale: locale),
         I18n.t('push_notif.delivery.receive.content', delivery_no: params[:delivery_no],
                                                       location_name: params[:location_from_name], locale: locale)]
      end
    when NotificationHelper::INCOMING_DELIVERY_ACTION_RECEIVE
      if params[:partial_receive]
        [I18n.t('push_notif.incoming_delivery.incomplete.header', locale: locale),
         I18n.t('push_notif.incoming_delivery.incomplete.content', delivery_no: params[:delivery_no],
                                                                   location_from_name: params[:location_from_name], locale: locale)]
      else
        [I18n.t('push_notif.incoming_delivery.receive.header', locale: locale),
         I18n.t('push_notif.incoming_delivery.receive.content', delivery_no: params[:delivery_no],
                                                                location_from_name: params[:location_from_name], locale: locale)]
      end
    when NotificationHelper::DELIVERY_ACTION_DESTROY
      [I18n.t('push_notif.delivery.destroy.header', locale: locale),
       I18n.t('push_notif.delivery.destroy.content', delivery_no: params[:delivery_no],
                                                     location_name: params[:location_name], locale: locale)]
    end
  end

  def self.generate_push_notif_delivery_return(action, locale, params)
    location_name = if params[:mask_third_party_location].present?
                      I18n.t('orders.third_party_location', locale: locale)
                    elsif params[:is_external]
                      params[:location_to_name]
                    else
                      params[:location_from_name]
                    end
    location_action = if params[:is_external]
                        I18n.t('notification.delivery_return.create.send_to_vendor', vendor_name: location_name, locale: locale)
                      else
                        I18n.t('notification.delivery_return.create.send_from_location', location_name: location_name, locale: locale)
                      end

    case action
    when NotificationHelper::DELIVERY_RETURN_FULFILLMENT_ACTION_CREATED
      [I18n.t('push_notif.delivery_return.create.header', locale: locale),
       I18n.t('push_notif.delivery_return.create_fulfillment.content', return_no: params[:return_no],
                                                                       deliveries_no: params[:deliveries_no],
                                                                       location_action: location_action, locale: locale)]
    when NotificationHelper::DELIVERY_RETURN_ACTION_CREATED
      [I18n.t('push_notif.delivery_return.create.header', locale: locale),
       I18n.t('push_notif.delivery_return.create.content', return_no: params[:return_no],
                                                           location_action: location_action, locale: locale)]
    when NotificationHelper::DELIVERY_RETURN_ACTION_REJECTED
      [I18n.t('notification.delivery_return.rejected.header', locale: locale, return_no: params[:return_no]),
       I18n.t('notification.delivery_return.rejected.content', return_no: params[:return_no], locale: locale)]
    end
  end

  def self.generate_push_notif_stock(action, locale, params)
    case action
    when NotificationHelper::STOCK_ACTION_PAR
      [I18n.t('push_notif.stock.par.header', locale: locale),
       I18n.t('push_notif.stock.par.content', count: params.try(:[], :metadata).try(:[], :count).presence || 0,
                                              location_name: params[:location_name], locale: locale)]
    end
  end

  def self.generate_push_notif_taking(action, locale, params)
    case action
    when 'create'
      [I18n.t('push_notif.taking.create.header', locale: locale),
       I18n.t('push_notif.taking.create.content', location_name: params[:location_name], locale: locale)]
    end
  end

  def self.generate_push_notif_grab_food_failed_menu_sync(locale, params)
    [
      I18n.t('push_notif.grab_food.failed_menu_sync.header', locale: locale),
      I18n.t('push_notif.grab_food.failed_menu_sync.content', location_name: params[:location_name], locale: locale)
    ]
  end

  def self.generate_push_notif_grab_food_failed_promo_sync(locale, params)
    [
      I18n.t('push_notif.grab_food.failed_promo_sync.header', locale: locale),
      I18n.t(
        'push_notif.grab_food.failed_promo_sync.content',
        location_name: params[:location_name],
        promo_name: params[:promo_name],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_grab_food_failed_order_sync(locale, params)
    [
      I18n.t('push_notif.grab_food.failed_order_sync.header', locale: locale),
      I18n.t(
        'push_notif.grab_food.failed_order_sync.content',
        location_name: params[:location_name],
        order_id: params[:order_id],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_go_food_failed_menu_sync(locale, params)
    [
      I18n.t('push_notif.go_food.failed_menu_sync.header', locale: locale),
      I18n.t('push_notif.go_food.failed_menu_sync.content', location_name: params[:location_name], locale: locale)
    ]
  end

  def self.generate_push_notif_go_food_failed_promo_sync(locale, params)
    [
      I18n.t('push_notif.go_food.failed_promo_sync.header', locale: locale),
      I18n.t(
        'push_notif.go_food.failed_promo_sync.content',
        location_name: params[:location_name],
        promo_name: params[:promo_name],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_go_food_failed_order_sync(locale, params)
    [
      I18n.t('push_notif.go_food.failed_order_sync.header', locale: locale),
      I18n.t(
        'push_notif.go_food.failed_order_sync.content',
        location_name: params[:location_name],
        order_id: params[:order_id],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_shopee_food_failed_menu_sync(locale, params)
    [
      I18n.t('push_notif.shopee_food.failed_menu_sync.header', locale: locale),
      I18n.t('push_notif.shopee_food.failed_menu_sync.content', location_name: params[:location_name], locale: locale)
    ]
  end

  def self.generate_push_notif_shopee_food_failed_promo_sync(locale, params)
    [
      I18n.t('push_notif.shopee_food.failed_promo_sync.header', locale: locale),
      I18n.t(
        'push_notif.shopee_food.failed_promo_sync.content',
        location_name: params[:location_name],
        promo_name: params[:promo_name],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_shopee_food_failed_order_sync(locale, params)
    [
      I18n.t('push_notif.shopee_food.failed_order_sync.header', locale: locale),
      I18n.t(
        'push_notif.shopee_food.failed_order_sync.content',
        location_name: params[:location_name],
        order_id: params[:order_id],
        locale: locale
      )
    ]
  end

  def self.generate_push_notif_royalty_creation_failed(locale, _params)
    [
      I18n.t('push_notif.royalty_transaction.creation_failed.header', locale: locale),
      I18n.t('push_notif.royalty_transaction.creation_failed.content', locale: locale)
    ]
  end

  def self.generate_push_notif_royalty_creation_completed(locale, _params)
    [
      I18n.t('push_notif.royalty_transaction.creation_completed.header', locale: locale),
      I18n.t('push_notif.royalty_transaction.creation_completed.content', locale: locale)
    ]
  end

  # Notification In App
  def self.generate_message(notification_type, action, params, locale)
    notif_types = Notification.notification_types

    case notification_type
    when notif_types.key(notif_types['product_import']),
         notif_types.key(notif_types['customer_import']),
         notif_types.key(notif_types['customer_point_import']),
         notif_types.key(notif_types['daily_sale_import']),
         notif_types.key(notif_types['stock_adjustment_import']),
         notif_types.key(notif_types['stock_opening_import']),
         notif_types.key(notif_types['order_transaction_import']),
         notif_types.key(notif_types['recipe_storage_sections_import']),
         notif_types.key(notif_types['money_movement_import']),
         notif_types.key(notif_types['recipe_import']),
         notif_types.key(notif_types['stock_in_import']),
         notif_types.key(notif_types['stock_out_import'])
      generate_message_import(action, locale, params)
    when notif_types.key(notif_types['app_notif_procurement_order_incoming_order']),
         notif_types.key(notif_types['app_notif_procurement_order_approve_void']),
         notif_types.key(notif_types['order_transaction_remind_payment']),
         notif_types.key(notif_types['order_transaction_remind_shipping_fee_payment']),
         notif_types.key(notif_types['order_transaction_added_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid']),
         notif_types.key(notif_types['order_transaction_buyer_paid']),
         notif_types.key(notif_types['order_transaction_remind_limit']),
         notif_types.key(notif_types['order_transaction_manual_refund']),
         notif_types.key(notif_types['order_approval_reminder'])
      generate_message_order(action, locale, params)
    when notif_types.key(notif_types['procurement_delivery_incoming']),
         notif_types.key(notif_types['procurement_delivery_received'])
      generate_message_delivery(action, locale, params)
    when notif_types.key(notif_types['completed_bulk_update_internal_price'])
      generate_message_completed_bulk_update_internal_price(action, locale, params)
    when notif_types.key(notif_types['procurement_delivery_return_created']),
      notif_types.key(notif_types['procurement_delivery_return_rejected'])
      generate_message_delivery_return(action, locale, params)
    when notif_types.key(notif_types['app_notif_report_par']),
         notif_types.key(notif_types['stock_transfer'])
      generate_message_stock(action, locale, params)
    when notif_types.key(notif_types['pos_taking'])
      generate_message_taking(action, locale, params)
    when notif_types.key(notif_types['grab_food_failed_menu_sync'])
      generate_message_grab_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['grab_food_failed_promo_sync'])
      generate_message_grab_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['grab_food_failed_order_sync'])
      generate_message_grab_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_menu_sync'])
      generate_message_go_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_promo_sync'])
      generate_message_go_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['go_food_failed_order_sync'])
      generate_message_go_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_menu_sync'])
      generate_message_shopee_food_failed_menu_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_promo_sync'])
      generate_message_shopee_food_failed_promo_sync(locale, params)
    when notif_types.key(notif_types['shopee_food_failed_order_sync'])
      generate_message_shopee_food_failed_order_sync(locale, params)
    when notif_types.key(notif_types['royalty_transaction_creation_failed'])
      generate_message_royalty_transaction_creation_failed(locale, params)
    when notif_types.key(notif_types['royalty_transaction_creation_completed'])
      generate_message_royalty_transaction_creation_completed(locale, params)
    when notif_types.key(notif_types['bulk_product_activation_success'])
      generate_message_bulk_product_activation_success(locale, params)
    when notif_types.key(notif_types['bulk_product_activation_failed'])
      generate_message_bulk_product_activation_failed(locale, params)
    else
      # TODO: implement the other later
      raise NotImplementedError
    end
  end

  def self.generate_message_autovoided_order(locale, variables)
    {
      data: I18n.t('notification.order_transaction.autovoid_completion_alert', locale: locale),
      variables: variables
    }
  end

  def self.generate_message_autovoid_reminder(locale, variables)
    {
      data: I18n.t('notification.order_transaction.autovoid_reminder', locale: locale),
      variables: variables
    }
  end

  def self.generate_message_completed_bulk_update_internal_price(action, locale, params)
    case action
    when NotificationHelper::COMPLETED_BULK_INTERNAL_PRICE_UPDATE
      if params[:user_id_and_location_ids].length == 1
        {
          data: I18n.t('push_notif.bulk_update_internal_price.completed.single_location_id', locale: locale),
          variables: {
            count: params[:user_id_and_location_ids].length,
            location_to_name: params[:user_id_and_location_ids].first.last
          }
        }
      else
        {
          data: I18n.t('push_notif.bulk_update_internal_price.completed.multiple_location_ids', locale: locale),
          variables: {
            count: params[:user_id_and_location_ids].length
          }
        }
      end
    end
  end

  def self.generate_message_import(action, locale, params)
    case action
    when 'product'
      {
        data: I18n.t('notification.import_data.product', locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'customer'
      {
        data: I18n.t('notification.import_data.customer', locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'customer_point'
      {
        data: I18n.t('notification.import_data.customer_point', locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'stock_adjustment'
      status = params[:fail_reason].present? ? "failed with reason: #{params[:fail_reason]}" : 'success'
      {
        data: I18n.t('notification.import_data.stock_adjustment', locale: locale),
        variables: { status: status, location_name: params[:location_name] }
      }
    when 'stock_opening'
      status = params[:success] ? 'success' : I18n.t('notification.import_data.stock_opening.failed', locale: locale).to_s
      {
        data: I18n.t('notification.import_data.stock_opening.create', locale: locale),
        variables: { status: status, location_name: params[:location_name] }
      }
    when 'order_transaction'
      if params[:success]
        {
          data: I18n.t('notification.import_data.order_transaction.create', locale: locale),
          variables: { date: params[:date] }
        }
      else
        {
          data: I18n.t('notification.import_data.order_transaction.failed', locale: locale),
          variables: { date: params[:date] }
        }
      end
    when 'daily_sale'
      if params[:success]
        {
          data: I18n.t('notification.import_data.daily_sale', locale: locale),
          variables: { date: params[:date] }
        }
      else
        {
          data: I18n.t('notification.import_data.daily_sale_failed', locale: locale),
          variables: { date: params[:date] }
        }
      end
    when 'recipe_storage_sections'
      key = 'notification.import_data.recipe_storage_sections'
      key << '_with_error' if params[:count] != params[:total_count]

      {
        data: I18n.t(key, locale: locale),
        variables: { location_name: params[:location_name] }
      }
    when 'money_movement'
      key = 'notification.import_data.money_movement'
      key << '_with_error' if params[:count] != params[:total_count]

      {
        data: I18n.t(key, locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'recipe'
      key = 'notification.import_data.recipe'
      key << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      {
        data: I18n.t(key, locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'stock_in'
      key = 'notification.import_data.stock_in'
      key << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      {
        data: I18n.t(key, locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    when 'stock_out'
      key = 'notification.import_data.stock_out'
      key << '_with_error' if params[:count] != params[:total_count] || (params[:count].zero? && params[:total_count].zero?)

      {
        data: I18n.t(key, locale: locale),
        variables: { count: params[:count], total_count: params[:total_count] }
      }
    end
  end

  def self.generate_message_order(action, locale, params)
    case action
    when NotificationHelper::ORDER_ACTION_CREATE
      {
        data: I18n.t('notification.order_transaction.create', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_from_name] }
      }
    when NotificationHelper::ORDER_ACTION_UPDATE
      {
        data: I18n.t('notification.order_transaction.update', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_REMIND_PAYMENT
      {
        data: I18n.t('notification.order_transaction.remind_payment', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_REMIND_SHIPPING_FEE_PAYMENT
      {
        data: I18n.t('notification.order_transaction.remind_shipping_fee_payment', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_ADDED_SHIPPING_FEE
      {
        data: I18n.t('notification.order_transaction.added_shipping_fee', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_PAID_SHIPPING_FEE
      {
        data: I18n.t('notification.order_transaction.paid_shipping_fee', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_PAID_INTERNAL
      {
        data: I18n.t('notification.order_transaction.paid', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_to_name] }
      }
    when NotificationHelper::ORDER_ACTION_PAID_EXTERNAL
      {
        data: I18n.t('notification.order_transaction.paid', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_to_name] }
      }
    when NotificationHelper::ORDER_ACTION_PAID_CUSTOMER
      {
        data: I18n.t('notification.order_transaction.paid', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_to_name] }
      }
    when NotificationHelper::ORDER_ACTION_BUYER_PAID
      {
        data: I18n.t('notification.order_transaction.buyer_paid', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_to_name] }
      }
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_INTERNAL
      {
        data: I18n.t('notification.order_transaction.remind_limit', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_EXTERNAL
      {
        data: I18n.t('notification.order_transaction.remind_limit', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_REMIND_LIMIT_CUSTOMER
      {
        data: I18n.t('notification.order_transaction.remind_limit', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_UPDATE_SHIPPING_FEE
      {
        data: I18n.t('notification.order_transaction.update_shipping_fee', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_UPDATE_PRICES
      {
        data: I18n.t('notification.order_transaction.update_prices', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_AUTO_APPROVE,
         NotificationHelper::ORDER_ACTION_APPROVE_INTERNAL,
         NotificationHelper::ORDER_ACTION_APPROVE_EXTERNAL,
         NotificationHelper::ORDER_ACTION_APPROVE_CUSTOMER
      {
        data: I18n.t('notification.order_transaction.approve', locale: locale),
        variables: { order_no: params[:order_no], location_to_name: params[:location_to_name],
                     location_from_name: params[:location_from_name] }
      }
    when NotificationHelper::ORDER_ACTION_VOID_INTERNAL
      {
        data: I18n.t('notification.order_transaction.void', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_VOID_EXTERNAL
      {
        data: I18n.t('notification.order_transaction.void', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_VOID_CUSTOMER
      {
        data: I18n.t('notification.order_transaction.void', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_CLOSE
      {
        data: I18n.t('notification.order_transaction.close', locale: locale),
        variables: { order_no: params[:order_no], location_name: params[:location_name] }
      }
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_INTERNAL
      {
        data: I18n.t('notification.order_transaction.manual_refund', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_EXTERNAL
      {
        data: I18n.t('notification.order_transaction.manual_refund', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_MANUAL_REFUND_CUSTOMER
      {
        data: I18n.t('notification.order_transaction.manual_refund', locale: locale),
        variables: { order_no: params[:order_no] }
      }
    when NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_EXTERNAL,
         NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_CUSTOMER,
         NotificationHelper::ORDER_ACTION_ORDER_APPROVAL_REMINDER_INTERNAL
      location_name = params[:vendor_name].presence || params[:location_name]
      {
        data: I18n.t('notification.order_transaction.order_approval_reminder', locale: locale),
        variables: { order_no: params[:order_no], location_name: location_name }
      }
    end
  end

  def self.generate_message_delivery(action, locale, params)
    case action
    when NotificationHelper::DELIVERY_ACTION_CREATE
      {
        data: I18n.t('notification.delivery.create', locale: locale),
        variables: { delivery_no: params[:delivery_no], location_name: params[:location_name] }
      }
    when NotificationHelper::DELIVERY_ACTION_UPDATE
      {
        data: I18n.t('notification.delivery.update', locale: locale),
        variables: { delivery_no: params[:delivery_no], location_name: params[:location_name] }
      }
    when NotificationHelper::DELIVERY_ACTION_RECEIVE
      if params[:partial_receive]
        {
          data: I18n.t('notification.delivery.incomplete', locale: locale),
          variables: { delivery_no: params[:delivery_no], location_name: params[:location_from_name] }
        }
      else
        {
          data: I18n.t('notification.delivery.receive', locale: locale),
          variables: { delivery_no: params[:delivery_no], location_name: params[:location_from_name] }
        }
      end
    when NotificationHelper::INCOMING_DELIVERY_ACTION_RECEIVE
      {
        data: I18n.t('notification.incoming_delivery.receive', locale: locale),
        variables: { delivery_no: params[:delivery_no], location_from_name: params[:location_from_name] }
      }
    when NotificationHelper::DELIVERY_ACTION_DESTROY
      {
        data: I18n.t('notification.delivery.destroy', locale: locale),
        variables: { delivery_no: params[:delivery_no], location_name: params[:location_name] }
      }
    end
  end

  def self.generate_message_delivery_return(action, locale, params)
    location_name = if params[:mask_third_party_location].present?
                      I18n.t('orders.third_party_location', locale: locale)
                    elsif params[:is_external]
                      params[:location_to_name]
                    else
                      params[:location_from_name]
                    end
    location_action = if params[:is_external]
                        I18n.t('notification.delivery_return.create.send_to_vendor', vendor_name: location_name, locale: locale)
                      else
                        I18n.t('notification.delivery_return.create.send_from_location', location_name: location_name, locale: locale)
                      end

    case action
    when NotificationHelper::DELIVERY_RETURN_FULFILLMENT_ACTION_CREATED
      {
        data: I18n.t('notification.delivery_return.create_fulfillment.content', locale: locale),
        variables: { return_no: params[:return_no], deliveries_no: params[:deliveries_no], location_action: location_action }
      }
    when NotificationHelper::DELIVERY_RETURN_ACTION_CREATED
      {
        data: I18n.t('notification.delivery_return.create.content', locale: locale),
        variables: { return_no: params[:return_no], location_action: location_action }
      }
    when NotificationHelper::DELIVERY_RETURN_ACTION_REJECTED
      {
        data: I18n.t('notification.delivery_return.rejected.content', locale: locale),
        variables: { return_no: params[:return_no] }
      }
    end
  end

  def self.generate_message_stock(action, locale, params)
    case action
    when NotificationHelper::STOCK_ACTION_PAR
      {
        data: I18n.t('notification.stock.par', locale: locale),
        variables: { count: params.try(:[], :metadata).try(:[], :count).presence || 0, location_name: params[:location_name] }
      }
    when NotificationHelper::STOCK_TRANSFER
      {
        data: I18n.t('notification.stock.stock_transfer', locale: locale),
        variables: { id: params[:resource_id], location_from_name: params[:location_from_name], location_to_name: params[:location_to_name] }
      }
    end
  end

  def self.generate_message_taking(action, locale, params)
    case action
    when NotificationHelper::TAKING_ACTION_CREATE
      {
        data: I18n.t('notification.taking.create', locale: locale),
        variables: { location_name: params[:location_name] }
      }
    end
  end

  def self.generate_message_grab_food_failed_menu_sync(locale, params)
    {
      data: I18n.t('notification.grab_food.failed_menu_sync', locale: locale),
      variables: { location_name: params[:location_name] }
    }
  end

  def self.generate_message_grab_food_failed_promo_sync(locale, params)
    {
      data: I18n.t('notification.grab_food.failed_promo_sync', locale: locale),
      variables: { promo_name: params[:promo_name], location_name: params[:location_name] }
    }
  end

  def self.generate_message_grab_food_failed_order_sync(locale, params)
    {
      data: I18n.t('notification.grab_food.failed_order_sync', locale: locale),
      variables: { order_id: params[:order_id], location_name: params[:location_name] }
    }
  end

  def self.generate_message_go_food_failed_menu_sync(locale, params)
    {
      data: I18n.t('notification.go_food.failed_menu_sync', locale: locale),
      variables: { location_name: params[:location_name] }
    }
  end

  def self.generate_message_go_food_failed_promo_sync(locale, params)
    {
      data: I18n.t('notification.go_food.failed_promo_sync', locale: locale),
      variables: { promo_name: params[:promo_name], location_name: params[:location_name] }
    }
  end

  def self.generate_message_go_food_failed_order_sync(locale, params)
    {
      data: I18n.t('notification.go_food.failed_order_sync', locale: locale),
      variables: { order_id: params[:order_id], location_name: params[:location_name] }
    }
  end

  def self.generate_message_shopee_food_failed_menu_sync(locale, params)
    {
      data: I18n.t('notification.shopee_food.failed_menu_sync', locale: locale),
      variables: { location_name: params[:location_name] }
    }
  end

  def self.generate_message_shopee_food_failed_promo_sync(locale, params)
    {
      data: I18n.t('notification.shopee_food.failed_promo_sync', locale: locale),
      variables: { promo_name: params[:promo_name], location_name: params[:location_name] }
    }
  end

  def self.generate_message_shopee_food_failed_order_sync(locale, params)
    {
      data: I18n.t('notification.shopee_food.failed_order_sync', locale: locale),
      variables: { order_id: params[:order_id], location_name: params[:location_name] }
    }
  end

  def self.generate_message_royalty_transaction_creation_failed(locale, params)
    {
      data: I18n.t('notification.royalty_transaction.creation_failed', locale: locale),
      variables: { id: params[:resource_id] }
    }
  end

  def self.generate_message_royalty_transaction_creation_completed(locale, params)
    {
      data: I18n.t('notification.royalty_transaction.creation_completed', locale: locale),
      variables: { id: params[:resource_id] }
    }
  end

  def self.generate_message_bulk_product_activation_success(locale, params)
    metadata = params[:metadata].with_indifferent_access
    locale_key = if metadata[:activate] == true
                   'notification.bulk_product_activation.activation_success'
                 else
                   'notification.bulk_product_activation.deactivation_success'
                 end

    {
      data: I18n.t(locale_key, success_count: metadata[:success_count], locale: locale),
      variables: { success_count: metadata[:success_count], activate: metadata[:activate] }
    }
  end

  def self.generate_message_bulk_product_activation_failed(locale, params)
    metadata = params[:metadata].with_indifferent_access

    success_count = metadata[:success_count].to_i
    total_count   = params[:total_count].to_i

    locale_key = if metadata[:activate] == true
                   'notification.bulk_product_activation.activation_failed'
                 else
                   'notification.bulk_product_activation.deactivation_failed'
                 end

    {
      data: I18n.t(
        locale_key,
        success_count: success_count,
        failed_count: (total_count - success_count),
        locale: locale
      ),
      variables: { success_count: success_count, activate: metadata[:activate] }
    }
  end

  def self.send_email(notification_type, user, notification, location, food_delivery_integration_id = nil)
    notif_types = Notification.notification_types

    case notification_type
    when notif_types.key(notif_types['product_import']),
         notif_types.key(notif_types['customer_import']),
         notif_types.key(notif_types['customer_point_import']),
         notif_types.key(notif_types['daily_sale_import']),
         notif_types.key(notif_types['stock_adjustment_import']),
         notif_types.key(notif_types['stock_opening_import']),
         notif_types.key(notif_types['order_transaction_import']),
         notif_types.key(notif_types['recipe_storage_sections_import']),
         notif_types.key(notif_types['money_movement_import']),
         notif_types.key(notif_types['recipe_import']),
         notif_types.key(notif_types['stock_in_import']),
         notif_types.key(notif_types['stock_out_import'])
      NotificationMailer.import_data(user, notification).deliver_later
    when notif_types.key(notif_types['email_procurement_order_incoming_order'])
      NotificationMailer.order_transaction_create(user, notification).deliver_later
    when notif_types.key(notif_types['email_procurement_order_approve_void'])
      NotificationMailer.order_transaction_approve_void(user, notification).deliver_later
    when notif_types.key(notif_types['procurement_delivery_incoming']),
         notif_types.key(notif_types['procurement_delivery_received']),
         notif_types.key(notif_types['procurement_delivery_return_created']),
         notif_types.key(notif_types['procurement_delivery_return_rejected']),
         notif_types.key(notif_types['order_transaction_remind_payment']),
         notif_types.key(notif_types['order_transaction_remind_shipping_fee_payment']),
         notif_types.key(notif_types['order_transaction_added_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid_shipping_fee']),
         notif_types.key(notif_types['order_transaction_paid']),
         notif_types.key(notif_types['order_transaction_buyer_paid']),
         notif_types.key(notif_types['order_transaction_remind_limit']),
         notif_types.key(notif_types['completed_bulk_update_internal_price'])
      # TODO: implement this
    when notif_types.key(notif_types['email_report_par'])
      # NOTE: RR-4860 Disable email notification as requested
      # NotificationMailer.low_stock_alert(user, notification).deliver_later
    when notif_types.key(notif_types['pos_taking'])
      # TODO: implement this
    when notif_types.key(notif_types['grab_food_failed_menu_sync'])
      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_menu_sync('grab_food', location, user).deliver_later
    when notif_types.key(notif_types['grab_food_failed_promo_sync'])
      promo = Promo.find_by(id: notification.associated_id)
      food_delivery_integration = FoodDeliveryIntegration.find_by(id: food_delivery_integration_id)
      promo_error_message = NotificationHelper.integration_promo_error_message(promo, food_delivery_integration)

      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_promo_sync(
        menu_type: 'grab_food',
        location: location,
        user: user,
        promo: promo,
        partner_outlet_id: food_delivery_integration.partner_outlet_id,
        promo_error_message: promo_error_message
      ).deliver_later
    when notif_types.key(notif_types['grab_food_failed_order_sync'])
      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_order_sync('grab_food', location, user).deliver_later
    when notif_types.key(notif_types['go_food_failed_menu_sync'])
      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_menu_sync('go_food', location, user).deliver_later
    when notif_types.key(notif_types['go_food_failed_promo_sync'])
      promo = Promo.find_by(id: notification.associated_id)
      food_delivery_integration = FoodDeliveryIntegration.find_by(id: food_delivery_integration_id)
      promo_error_message = NotificationHelper.integration_promo_error_message(promo, food_delivery_integration)

      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_promo_sync(
        menu_type: 'go_food',
        location: location,
        user: user,
        promo: promo,
        partner_outlet_id: food_delivery_integration.partner_outlet_id,
        promo_error_message: promo_error_message
      ).deliver_later
    when notif_types.key(notif_types['go_food_failed_order_sync'])
      klass = if location.brand.edot_usage
                PostmarkFoodIntegrationMailer
              else
                FoodIntegrationMailer
              end

      klass.failed_order_sync('go_food', location, user).deliver_later
    when notif_types.key(notif_types['shopee_food_failed_menu_sync'])
      FoodIntegrationMailer.failed_menu_sync('shopee_food', location, user).deliver_later
    when notif_types.key(notif_types['shopee_food_failed_promo_sync'])
      promo = Promo.find_by(id: notification.associated_id)
      food_delivery_integration = FoodDeliveryIntegration.find_by(id: food_delivery_integration_id)
      promo_error_message = NotificationHelper.integration_promo_error_message(promo, food_delivery_integration)

      FoodIntegrationMailer.failed_promo_sync(
        menu_type: 'shopee_food',
        location: location,
        user: user,
        promo: promo,
        partner_outlet_id: food_delivery_integration.partner_outlet_id,
        promo_error_message: promo_error_message
      ).deliver_later
    when notif_types.key(notif_types['shopee_food_failed_order_sync'])
      FoodIntegrationMailer.failed_order_sync('shopee_food', location, user).deliver_later
    when notif_types.key(notif_types['order_transaction_manual_refund'])
      NotificationMailer.order_transaction_manual_refund(user, notification).deliver_later
    when notif_types.key(notif_types['stock_transfer']),
         notif_types.key(notif_types['royalty_transaction_creation_failed']),
         notif_types.key(notif_types['royalty_transaction_creation_completed']),
         notif_types.key(notif_types['bulk_product_activation_success']),
         notif_types.key(notif_types['bulk_product_activation_failed']),
         notif_types.key(notif_types['order_approval_reminder'])
      nil
    else
      raise NotImplementedError
    end
  end

  def self.integration_promo_error_message(promo, food_delivery_integration)
    promo_sync_log = promo.promo_sync_logs.where(
      food_delivery_integration_id: food_delivery_integration.id,
      status: 'failed'
    ).last
    raw_response = JSON.parse(promo_sync_log.provider_raw_response)

    return raw_response.dig('body', 'errors').to_json if food_delivery_integration.food_delivery_type == 'gofood'.freeze

    # TODO: for shpopee food later
    # return raw_response.dig('body', 'errors').to_json if food_delivery_integration.food_delivery_type == 'official_shopee_food'.freeze

    # grab food
    raw_response.dig('body', 'message').to_s
  end
end
# rubocop:enable Metrics/ModuleLength, Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity
