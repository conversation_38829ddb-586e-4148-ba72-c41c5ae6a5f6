class Notification < ApplicationRecord
  belongs_to :user
  belongs_to :brand
  belongs_to :location, optional: true
  belongs_to :associated, polymorphic: true, optional: true

  validates :user_id, :brand, :notification_type, presence: true

  enum notification_type: {
    app_notif_procurement_order_incoming_order: 0,
    app_notif_procurement_order_approve_void: 1,
    procurement_delivery_incoming: 2,
    procurement_delivery_received: 3,

    # imports
    product_import: 4,
    product_min_quantity: 5,
    customer_import: 6,
    daily_sale_import: 7,
    stock_adjustment_import: 8,

    pos_taking: 9,
    online_ordering_incoming_order: 10,
    app_notif_report_par: 11,
    # grab food related
    grab_food_failed_menu_sync: 12,
    grab_food_failed_promo_sync: 13,
    grab_food_failed_order_sync: 14,
    # go food related
    go_food_failed_menu_sync: 15,
    go_food_failed_promo_sync: 16,
    go_food_failed_order_sync: 17,
    # procurement payment related
    order_transaction_remind_payment: 18,
    order_transaction_paid: 19,
    order_transaction_remind_limit: 20,
    order_transaction_buyer_paid: 21,
    # procurement payment (shipping fee) related
    order_transaction_remind_shipping_fee_payment: 22,
    order_transaction_added_shipping_fee: 23,
    order_transaction_paid_shipping_fee: 24,
    # non official shopee_food related (not used)
    shopee_food_failed_get_orders: 25,
    shopee_food_failed_confirm_order: 26,
    # procurement return
    procurement_delivery_return_created: 27,
    procurement_delivery_return_rejected: 29,
    # procurements
    stock_opening_import: 28,
    order_transaction_manual_refund: 30,
    # bulk update internal price
    completed_bulk_update_internal_price: 31,
    # stock transfer
    stock_transfer: 32,
    order_transaction_import: 33,
    # official shopee food related
    shopee_food_failed_menu_sync: 34,
    shopee_food_failed_promo_sync: 35,
    shopee_food_failed_order_sync: 36,
    # royalty transaction creation
    royalty_transaction_creation_failed: 37,
    royalty_transaction_creation_completed: 38,
    # customer point
    customer_point_import: 39,
    # email
    email_procurement_order_incoming_order: 40,
    email_procurement_order_approve_void: 41,
    # bulk product activation
    bulk_product_activation_success: 42,
    bulk_product_activation_failed: 43,
    # order approval reminder
    order_approval_reminder: 44,
    # email
    email_report_par: 45,
    # import (continued)
    recipe_storage_sections_import: 46,
    autovoid_order_reminder: 47,
    autovoid_order_completion_alert: 48,
    recipe_import: 49,
    money_movement_import: 50,
    stock_in_import: 51,
    stock_out_import: 52
  }

  # rubocop:disable Metrics/MethodLength
  def generate_filter
    if bulk_product_activation_failed?
      return {
        success_count: metadata['success_count'].to_i,
        failed_products: metadata['failed_products'] || [],
        activate: metadata['activate'] || false
      }
    end

    return nil if location.blank?

    if app_notif_report_par?
      {
        date: created_at.in_time_zone(location.timezone).strftime('%d/%m/%Y'),
        location_id: location_id,
        location_name: location.name,
        stock_below_par: true
      }
    elsif grab_food_failed_menu_sync?
      {
        location_id: location.id,
        location_name: location.name,
        menu_type: 'grab_food'
      }
    elsif go_food_failed_menu_sync?
      {
        location_id: location.id,
        location_name: location.name,
        menu_type: 'go_food'
      }
    elsif shopee_food_failed_menu_sync?
      {
        location_id: location.id,
        location_name: location.name,
        menu_type: 'shopee_food'
      }
    elsif bulk_product_activation_failed?
      {
        success_count: metadata['success_count'].to_i,
        failed_products: metadata['failed_products'] || []
      }
    elsif app_notif_procurement_order_incoming_order? || order_approval_reminder?
      { order_nature: metadata['order_nature'] }
    elsif autovoid_order_reminder? || autovoid_order_completion_alert?
      { order_nature: 'outgoing' }
    end
  end
  # rubocop:enable Metrics/MethodLength

  def self.type_by_key(key)
    notification_types.key(notification_types[key])
  end
end
