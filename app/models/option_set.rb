class OptionSet < ApplicationRecord
  include UserAttributable
  include Restaurant::Concerns::NameTrimmable
  include Restaurant::Modules::ProductsUniqueness<PERSON><PERSON><PERSON>

  acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })
  audited
  has_associated_audits

  enum docket_printing_option: { same_as_main_menu: 0, split_item_by_category: 1 }
  enum docket_printing_sticker_option: { same_with_main_menu: 0, per_option_set_menu: 1 }
  enum course_display_menu: { group_same_menu: 0, default_per_options: 1 }

  belongs_to :brand

  has_many :option_set_options, dependent: :destroy, index_errors: true
  has_many :product_option_sets
  has_many :option_set_custom_price_locations
  has_many :option_set_price_table_details, class_name: 'Products::Models::OptionSetPriceTableDetail', dependent: :destroy

  belongs_to :created_by, class_name: 'User', optional: true
  belongs_to :last_updated_by, class_name: 'User', optional: true

  before_validation :clean_order_type_ids

  validate :products_uniqueness
  validate :validate_same_resource

  # index has been created
  validates :name, uniqueness: { scope: :brand_id, case_sensitive: false, conditions: -> {
    where(deleted: 0)
  } }
  accepts_nested_attributes_for :option_set_options, allow_destroy: true

  validates :name, :brand, presence: true
  validates :rule_minimum, numericality: { greater_than_or_equal_to: 0 }, allow_blank: true
  validates :rule_maximum, numericality: { greater_than: 0 }, allow_blank: true
  validates :option_set_options, presence: true

  before_save :build_order_type_ids
  before_destroy :check_validity_before_destroy
  after_update :update_sale_detail_modifier_cost
  after_save :update_has_option_set_as_option

  after_commit -> { sync_richeese_database }
  after_update_commit :update_product_option_set
  after_update_commit :check_parent_options_availability

  def initialize(params = {})
    super
    self.deleted = false if deleted.nil?
    self.rule_show_option_prefix = false if rule_show_option_prefix.nil?
  end

  def self.audit_trackable_column
    ['name', 'rule_minimum', 'rule_maximum', 'rule_show_option_prefix']
  end

  def check_validity_before_destroy
    if product_option_sets.exists?
      errors.add(:option_set,
                 I18n.t('activerecord.errors.models.option_set.used'))
      throw :abort
    end
  end

  # Querying option sets with active products AND active locations.
  def self.with_only_active_option_sets
    left_outer_joins(option_set_options: { product: %i[locations variance_parent_product] })
      .where('products.status = ?', Product.statuses[:activated])
      .where('locations.status = ?', Location.statuses[:activated])
      .where('
        products.variance_parent_product_id IS NULL OR
        products.variance_parent_product_id IS NOT NULL AND variance_parent_products_products.status = ?
      ', Product.statuses[:activated])
      .distinct
  end

  def parent_rule_update_lock!
    update_columns(parent_rule_update_locked: true)
  end

  def parent_rule_update_unlock!
    update_columns(parent_rule_update_locked: false)
  end

  def child_has_option_set_as_option
    option_set_options.detect do |option_set_option|
      option_set_option.option_set_as_option_id.present? ||
        option_set_option.product_with_variance ||
        option_set_option.product_with_option_set
    end.present?
  end

  private

  def validate_same_resource
    validate_same_resource_order_type
  end

  def validate_same_resource_order_type
    return if order_type_ids.blank?

    current_brand_ids = OrderType.where(id: order_type_ids).pluck(:brand_id).uniq
    filtered_brand_ids = current_brand_ids - [nil, brand_id]
    return if filtered_brand_ids.empty?

    errors.add(:order_type_ids, I18n.t('activerecord.errors.models.general.not_same_resource'))
  end

  def clean_order_type_ids
    self.order_type_ids = order_type_ids.grep(Integer)
    self.exclude_order_type_ids = exclude_order_type_ids.grep(Integer)
  end

  def build_order_type_ids
    return unless is_select_all_order_type

    self.order_type_ids = OrderType.where(brand_id: [brand_id, nil]).pluck(:id) - exclude_order_type_ids
  end

  def update_has_option_set_as_option
    has_child = child_has_option_set_as_option
    return if has_child == has_option_set_as_option

    update_columns(
      has_option_set_as_option: has_child
    )
  end

  def check_parent_options_availability
    return if option_set_options.blank?
    return if saved_changes['rule_minimum'].blank? && rule_minimum.to_i.zero?

    product_id = option_set_options.first.product_id

    case Settings.searchkick_mode
    when :inline
      Restaurant::Jobs::Product::OutOfStockParentOptionJob.new.perform(option_set_option_product_id: product_id)
    when :async
      Restaurant::Jobs::Product::OutOfStockParentOptionJob.perform_later(option_set_option_product_id: product_id)
    end
  end

  def update_product_option_set
    Restaurant::Jobs::OptionSet::ProductVersionUpdateJob.perform_later([id])
  end

  def products_uniqueness
    duplicate_product_ids = duplicate_product_ids(option_set_options).compact
    duplicated_option_set_ids = duplicate_option_set_ids(option_set_options).compact

    return if duplicate_product_ids.blank? && duplicated_option_set_ids.blank?

    if duplicate_product_ids.present? && duplicated_option_set_ids.present?
      option_set_names = brand.option_sets.where(id: duplicated_option_set_ids).map(&:name).join(', ')
      product_names = brand.products.where(id: duplicate_product_ids).map(&:name).join(', ')

      raise ::Errors::UnprocessableEntity,
            I18n.t('option_sets.errors.duplicate_products_and_option_sets',
                   product_names: product_names, option_set_names: option_set_names)
    elsif duplicate_product_ids.present?
      product_names = brand.products.where(id: duplicate_product_ids).map(&:name).join(', ')
      raise ::Errors::UnprocessableEntity, I18n.t('option_sets.errors.duplicate_products', product_names: product_names)
    elsif duplicated_option_set_ids.present?
      option_set_names = brand.option_sets.where(id: duplicated_option_set_ids).map(&:name).join(', ')
      raise ::Errors::UnprocessableEntity, I18n.t('option_sets.errors.duplicate_option_sets', option_set_names: option_set_names)
    end
  end

  def update_sale_detail_modifier_cost
    return unless saved_change_to_rule_cost_included_in_parent?

    brand.option_set_parent_rule_update_lock_check!
    check_calculating_costing
    check_deletion_costing

    parent_rule_update_lock!
    location_ids = brand.locations.ids
    job_klass = Restaurant::Jobs::OptionSet::SaleDetailModifierCostUpdateJob

    case Settings.searchkick_mode
    when :inline
      job_klass.perform_now(id, location_ids, rule_cost_included_in_parent)
    when :async
      job_klass.perform_later(id, location_ids, rule_cost_included_in_parent)
    end
  end

  def check_calculating_costing
    return unless brand.costings.calculating.exists?

    raise ::Errors::UnprocessableEntity, I18n.t('activerecord.errors.models.costing.calculation_in_process')
  end

  def check_deletion_costing
    deletion_in_process = brand
                          .costing_deletion_logs
                          .where('delete_sale_cogs_locked_at IS NOT NULL OR delete_procurement_cogs_locked_at IS NOT NULL')
                          .exists?

    raise ::Errors::UnprocessableEntity, I18n.t('activerecord.errors.models.costing.deletion_in_process') if deletion_in_process.present?
  end

  def duplicate_option_set_ids(product_uniqueness_checkable)
    product_uniqueness_checkable_temp = product_uniqueness_checkable.to_a

    tallied_product_ids = product_uniqueness_checkable_temp
                          .reject(&:marked_for_destruction?)
                          .map(&:option_set_as_option_id)
                          .tally

    tallied_product_ids.select { |_product_id, count| count > 1 }.keys
  end
end
