# rubocop:disable Metrics/BlockLength
module SaleDetailTransactionLogic
  extend ActiveSupport::Concern

  included do
    include Restaurant::Modules::SaleDetailTransaction::CalculateTaxFeeForAmount
    include Restaurant::Modules::SaleDetailTransaction::CalculateReportData
    include Restaurant::Modules::SaleTransaction::BulkPopulateProductRecipe
    extend Restaurant::Modules::SaleDetailTransactionQuery

    delegate :customer_order, to: :sale_transaction, allow_nil: true

    acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })
    audited associated_with: :sale_transaction

    belongs_to :product
    belongs_to :sale_transaction
    belongs_to :product_unit
    belongs_to :order_type, optional: true
    belongs_to :sub_brand, optional: true
    belongs_to :tax, optional: true
    belongs_to :product_category, optional: true

    has_many :sale_detail_modifiers, dependent: :destroy
    has_many :sales_return_lines

    accepts_nested_attributes_for :sale_detail_modifiers

    validates :product, :sale_transaction, :product_unit, :price, :batch_id, :quantity, :cancelled_quantity, presence: true
    validates :total_amount, presence: true
    validates :total_amount, numericality: { greater_than_or_equal_to: 0 }

    validate :validate_same_resource
    before_validation :check_change_object
    before_save :add_report_columns
    before_validation :add_meta_sales_feed_adjustment_notes
    after_create :assign_sales_by_amounts

    # TODO: add accessor to real columns when needed
    attr_accessor :prorate_service_charge_before_tax, :total_line_discount_prorate_for_service_charge, :total_line_discount_prorate_for_tax,
                  :kds_metadata_updated, :prorate_customer_order_payment_processing_fee,
                  :calculated_option_set_options, :calculated_modifier_products, :calculated_modifier_product_units,
                  :calculated_discount_line_modifier, :calculated_free_of_charge_line_modifier, :calculated_surcharge_line_modifier,
                  :calculated_prorate_rounding_service_charge_modifier, :calculated_prorate_rounding_tax_modifier,
                  :use_product_option_set_recipe, :prorate_rounding_tax, :prorate_rounding_service_charge,
                  :prorate_discount_product, :prorate_discount_order_level
  end

  class_methods do
    def associated_primary_key(audit)
      audit.auditable.product_name
    end
  end
  def initialize(params = {})
    super
    self.cancelled_quantity = 0 if cancelled_quantity.nil?
  end

  # Cost metadata here includes modifiers' costs as well.
  def cost
    return nil if meta.nil?

    meta['cost'].to_d
  end

  def map_product_id
    sale_detail_modifiers.map(&:product_id) + [product_id]
  end

  def map_product_name
    sale_detail_modifiers.map(&:product_name) + [product_name]
  end

  def map_product_sku
    sale_detail_modifiers.map(&:product_sku) + [product_sku]
  end

  def generate_adjustment_notes
    modifier = sale_detail_modifiers.detect do |sale_detail_modifier|
      sale_detail_modifier.product_id.nil? && sale_detail_modifier.free_of_charge == false
    end

    return nil if modifier.blank? || modifier.meta.blank?

    modifier.meta['notes']
  end

  def detail_adjustment_notes
    detail_adjustment_note = adjustment_notes

    if detail_adjustment_note.present? && meta.present? && meta['integration_product_name'].present?
      "#{detail_adjustment_note}, #{meta['integration_product_name']}"
    elsif detail_adjustment_note.present?
      detail_adjustment_note
    elsif meta.present? && meta['integration_product_name'].present?
      meta['integration_product_name']
    end
  end

  def discount_line_modifier
    return calculated_discount_line_modifier unless calculated_discount_line_modifier.nil?

    modifier = sale_detail_modifiers.detect(&:type_discount)

    modifier ? modifier.total_line_amount * -1 : 0
  end

  def free_of_charge_line_modifier
    return calculated_free_of_charge_line_modifier unless calculated_free_of_charge_line_modifier.nil?

    modifier = sale_detail_modifiers.detect do |sale_detail_modifier|
      sale_detail_modifier.product_id.nil? && sale_detail_modifier.total_line_amount.negative? && sale_detail_modifier.free_of_charge
    end

    modifier ? modifier.total_line_amount * -1 : 0
  end

  def surcharge_line_modifier
    return calculated_surcharge_line_modifier unless calculated_surcharge_line_modifier.nil?

    modifier = sale_detail_modifiers.detect do |sale_detail_modifier|
      sale_detail_modifier.product_id.nil? && sale_detail_modifier.total_line_amount.positive?
    end

    modifier ? modifier.total_line_amount : 0
  end

  def total_surcharge_and_discount_line_modifier
    free_of_charge_line_modifier.to_d +
      discount_line_modifier.to_d - surcharge_line_modifier.to_d
  end

  def generate_modifier_names
    sale_detail_modifiers.map do |modifier|
      if modifier.product_id.nil?
        ''
      else
        "#{modifier.quantity}x #{modifier.product_name}"
      end
    end.join(',')
  end

  def handle_cancelled_detail_modifiers_meta?
    customer_order = sale_transaction.customer_order
    return false if customer_order.blank?

    [DineIn::Models::MergedOpenBillOrder, Preorder::Models::CustomerPreorder, DineIn::Models::ClosedBill].any? { |klas| customer_order.is_a?(klas) }
  end

  def option_set_option_ids
    option_set_option_ids = []

    sale_detail_modifiers.each do |sale_detail_modifier|
      option_set_option_ids << sale_detail_modifier.meta_option_set_option_id if sale_detail_modifier.meta_option_set_option_id.present?
    end

    option_set_option_ids
  end

  def parent_rule_metadata
    {
      parent_rule_total_line_amount: parent_rule_total_line_amount,
      parent_rule_prorate_discount: parent_rule_prorate_discount,
      parent_rule_prorate_surcharge: parent_rule_prorate_surcharge,
      sales_by_free_of_charge: sales_by_free_of_charge,
      parent_rule_total_line_discount_prorate: parent_rule_total_line_discount_prorate
    }
  end

  def assign_sale_product_ids
    generate_modifiers_associations

    sale_detail_modifiers.select(&:should_rule_cost_included_in_parent?).map(&:product_id) << product_id
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def generate_modifiers_associations
    option_set_option_ids = []
    product_ids = []
    product_unit_ids = []
    self.calculated_discount_line_modifier = 0.0
    self.calculated_free_of_charge_line_modifier = 0.0
    self.calculated_surcharge_line_modifier = 0.0

    sale_detail_modifiers.each do |modifier|
      product_ids << modifier.product_id
      product_unit_ids << modifier.product_unit_id
      option_set_option_ids << modifier.meta['option_set_option_id'] if modifier.meta.present?

      self.calculated_surcharge_line_modifier = modifier.total_line_amount if modifier.product_id.nil? && modifier.total_line_amount.positive?

      if modifier.product_id.nil? && modifier.total_line_amount.negative? && modifier.free_of_charge == false
        self.calculated_discount_line_modifier = modifier.total_line_amount * -1
      end

      if modifier.product_id.nil? && modifier.total_line_amount.negative? && modifier.free_of_charge
        self.calculated_free_of_charge_line_modifier = modifier.total_line_amount * -1
      end
    end

    product_unit_ids = product_unit_ids.uniq
    product_ids = product_ids.uniq
    option_set_option_ids = option_set_option_ids.uniq

    self.calculated_modifier_product_units = if product_unit_ids.blank?
                                               {}
                                             else
                                               ProductUnit.where(id: product_unit_ids).index_by(&:id)
                                             end

    self.calculated_modifier_products = if product_ids.blank?
                                          {}
                                        else
                                          Product.includes(:product_unit, :product_unit_conversions).where(id: product_ids).index_by(&:id)
                                        end

    self.calculated_option_set_options = if option_set_option_ids.blank?
                                           {}
                                         else
                                           OptionSetOption.includes(:option_set)
                                                          .where(id: option_set_option_ids, product_id: product_ids)
                                                          .index_by(&:id)
                                         end
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  def all_you_can_eat_order?
    meta.present? && meta['all_you_can_eat_id'].present?
  end

  def all_returned?
    quantity == return_quantity
  end

  def calculate_return_quantity
    sales_return_lines.sum(:return_quantity)
  end

  def return_quantity
    calculate_return_quantity
  end

  def return_qty_ratio
    product_quantity = quantity.to_d

    if product_quantity.zero?
      0
    else
      sales_return_lines.select do |return_line|
        return_line.sales_return.ok_and_item_not_delivered?
      end.sum(&:return_quantity) / product_quantity
    end
  end

  def discount_line_modifier_add_by_tax(self_tax_setting = tax_setting)
    discount_line_amount = discount_line_modifier
    if self_tax_setting == 'price_exclude_tax' && sale_transaction.food_delivery_integration_type?
      discount_line_amount += (discount_line_amount * tax_rate.to_d / 100.0)
    end
    discount_line_amount
  end

  def generate_cooking_time
    kds_metadata = meta&.dig('kds_product_metadata')

    return nil if kds_metadata.nil?

    duration = kds_metadata.dig('time_duration', 'cooking', 'total_duration').to_i

    other_kds_product_metadata = meta['other_kds_product_metadata'] || []
    other_kds_product_metadata.each do |other_metadata|
      duration += other_metadata.dig('time_duration', 'cooking', 'total_duration').to_i
    end

    duration
  end

  def generate_serving_time
    kds_metadata = meta&.dig('kds_product_metadata')

    return nil if kds_metadata.nil?

    duration = kds_metadata.dig('time_duration', 'serving', 'total_duration').to_i

    other_kds_product_metadata = meta['other_kds_product_metadata'] || []
    other_kds_product_metadata.each do |other_metadata|
      duration += other_metadata.dig('time_duration', 'serving', 'total_duration').to_i
    end

    duration
  end

  def update_kds_data_report_sales_feed
    update_columns(cooking_time: generate_cooking_time, serving_time: generate_serving_time)
  end

  def calculated_gross_sales_modifier
    @calculated_gross_sales_modifier ||= begin
      total_gross_sales = 0.0
      sale_detail_modifiers.each do |modifier|
        total_gross_sales += modifier.calculate_gross_sales
      end

      total_gross_sales
    end
  end

  def calculated_loyalty_earn_point
    return 0 if meta.blank? || meta['loyalty_earn_product'].blank?

    meta.dig('loyalty_earn_product', 'earned_point').to_d
  end

  def loyalty_earn_point?
    meta.present? && meta['loyalty_earn_product'].present?
  end

  def loyalty_product?
    meta.present? && meta['loyalty_id'].present? && meta['loyalty_product_id'].present?
  end

  def valid_to_earn_point?
    final_price = total_line_amount - prorate_discount - prorate_free_of_charge + prorate_surcharge

    final_price.positive? && # check final price
      meta['loyalty_product_id'].blank? # check if loyalty or not
  end

  private

  def add_meta_sales_feed_adjustment_notes
    self.meta = {} if meta.blank?
    self.meta = meta.merge(sales_feed_adjustment_notes: detail_adjustment_notes.presence || '-')
  end

  def included_in_parent_modifiers
    sale_detail_modifiers.select(&:rule_cost_included_in_parent)
  end

  def parent_rule_total_line_amount
    total_line_amount.to_d + included_in_parent_modifiers.reduce(0) do |sum, sale_detail_modifier|
                               sum + sale_detail_modifier.total_line_amount
                             end
  end

  def product_modifiers
    sale_detail_modifiers.select { |modifier| modifier.product_id.present? }
  end

  def sales_by_free_of_charge
    prorate_free_of_charge_before_tax_report_usage_only.to_d +
      included_in_parent_modifiers.reduce(0) do |sum, sale_detail_modifier|
        sum + sale_detail_modifier.prorate_free_of_charge_before_tax_report_usage_only
      end
  end

  def parent_rule_prorate_discount
    prorate_discount.to_d +
      included_in_parent_modifiers.reduce(0) do |sum, sale_detail_modifier|
        sum + sale_detail_modifier.prorate_discount.to_d
      end
  end

  def parent_rule_prorate_surcharge
    prorate_surcharge.to_d + included_in_parent_modifiers.reduce(0) do |sum, sale_detail_modifier|
                               sum + sale_detail_modifier.prorate_surcharge.to_d
                             end
  end

  def parent_rule_total_line_discount_prorate
    total_line_discount_prorate.to_d + included_in_parent_modifiers.reduce(0) do |sum, sale_detail_modifier|
                                         sum + sale_detail_modifier.total_amount_prorate_discount
                                       end
  end

  def add_report_columns
    self.cooking_time = generate_cooking_time
    self.serving_time = generate_serving_time
    self.cancelled_item_reason = generate_cancelled_item_reason
    self.cancelled_item_by_detail = generate_cancelled_item_by_detail
    self.adjustment_notes = generate_adjustment_notes
    self.sub_brand_name = sub_brand&.name
  end

  def generate_cancelled_item_reason
    cancel_reasons.present? ? cancel_reasons.map { |cancel_reason| translate_cancelled_reason(cancel_reason) }.join(', ') : '-'
  end

  def generate_cancelled_item_by_detail
    return '-' if cancel_reasons.blank?

    send_order_users = sale_transaction.metadata&.dig('send_order_users').presence || {}
    cancelled_item_details = meta&.dig('cancelled_by_detail').presence || {}

    return '-' if send_order_users.blank? && cancelled_item_details.blank?

    fullnames = cancelled_item_details.presence || send_order_users
    fullnames.map { |fullname| fullname['fullname'] || fullname['name'] }.compact.uniq.sort.join(', ')
  end

  def translate_cancelled_reason(cancelled_reason)
    case cancelled_reason
    when 'wrong_input'.freeze
      I18n.t('report.sales_feed.body.wrong_input')
    when 'wrong_item'.freeze
      I18n.t('report.sales_feed.body.wrong_item')
    when 'item_exchange'.freeze
      I18n.t('report.sales_feed.body.item_exchange')
    when 'item_damage'.freeze
      I18n.t('report.sales_feed.body.item_damage')
    when 'unhappy_customer'.freeze
      I18n.t('report.sales_feed.body.unhappy_customer')
    when 'item_not_delivered'.freeze
      I18n.t('report.sales_feed.body.item_not_delivered')
    else
      # handle custom cancel reason
      cancelled_reason
    end
  end

  # This assignment is part of variant parent rule.
  # For now it's only used in sales by product report.
  # By default data will be in child.
  # If we find sale_detail_modifiers that rule_cost_included_in_parent is true, we accumulate them.
  def assign_sales_by_amounts
    detail_meta = meta.nil? ? {} : meta
    detail_meta = detail_meta.merge(parent_rule_metadata)
    update_columns(meta: detail_meta)
  end

  # rubocop:disable Metrics/AbcSize
  def check_change_object
    return unless product_id_changed?

    self.product_unit_id = product.sell_unit_id if product.present?
    self.product_name = product.name
    self.product_sku = product.sku
    self.product_upc = product.upc
    self.product_description = product.description
    product_groups = product.product_groups.active

    if product_group_ids.blank?
      self.product_group_ids = product_groups.map(&:id).presence || [0] # quick solution for filtering sales feed
      self.product_group_names = product_groups.map(&:name).uniq.sort.join(', ')
    end

    self.sale_product_ids = assign_sale_product_ids.flatten.compact # for filtering by product id in sales by report

    # denorm fields, status default to ok
    unless Flipper.enabled?(:prevent_fill_sale_denorm_fields)
      self.brand_id = sale_transaction.brand_id
      self.location_id = sale_transaction.location_id
      self.local_sales_time = sale_transaction.local_sales_time
    end

    self.order_type_id = sale_transaction.order_type_id if order_type_id.blank? &&
                                                           sale_transaction.customer_order_for_report_calculation.present?
    self.order_type_name = order_type&.name
    self.product_unit_name = product_unit.name
    self.product_unit_conversion_qty = product.convert_quantity_to_base(product_unit_id, 1)

    if tax_id.present?
      # NOTE: Intentionally let this fail if the tax_id can't be found
      tax = sale_transaction.brand.taxes.find(tax_id)
      self.tax_name = tax.name
      self.tax_rate = tax.rate || 0
    end

    recipe_order_type_id = order_type_id.presence || sale_transaction.order_type_id
    find_and_populate_product_recipe(sale_transaction, self, recipe_order_type_id)
  end
  # rubocop:enable Metrics/AbcSize

  def validate_same_resource
    if product.present? && product.brand_id != sale_transaction.brand_id
      errors.add(:product,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
    if product_unit.present? && product_unit.brand_id != sale_transaction.brand_id
      errors.add(:product_unit,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end
end
# rubocop:enable Metrics/BlockLength
