# rubocop:disable Metrics/BlockLength
module SaleDetailModifierLogic
  extend ActiveSupport::Concern

  included do
    include Restaurant::Modules::SaleDetailTransaction::CalculateTaxFeeForAmount
    include Restaurant::Modules::SaleDetailModifier::CalculateReportData
    include Restaurant::Modules::SaleTransaction::BulkPopulateProductRecipe

    delegate :customer_order, to: :sale_detail_transaction, allow_nil: true

    extend Restaurant::Modules::SaleDetailModifierQuery

    acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })
    audited associated_with: :sale_transaction

    belongs_to :product, optional: true
    belongs_to :sale_detail_transaction
    belongs_to :product_unit, optional: true
    belongs_to :tax, optional: true
    belongs_to :parent, class_name: 'SaleDetailModifier', optional: true
    has_one :sale_transaction, through: :sale_detail_transaction

    validates :sale_detail_transaction, :price, :quantity, :total_line_amount, presence: true

    validates :selected_product, presence: { if: -> { description.blank? } }
    validates :description, presence: { if: -> { selected_product.blank? } }
    validates :selected_product_unit, presence: { if: -> { selected_product.present? } }
    validate :validate_same_resource
    before_create :assign_rule_cost_and_metadata
    before_create :apply_denorm_fields
    before_save :handle_child_index

    scope :included_in_parent, -> { where('rule_cost_included_in_parent IS TRUE') }
    scope :not_included_in_parent, -> { where('rule_cost_included_in_parent IS FALSE') }

    before_validation :check_change_object

    # TODO: add accessor to real columns when needed
    attr_accessor :prorate_service_charge_before_tax, :prorate_additional_charge_fee, :prorate_rounding,
                  :prorate_total_subsidized, :prorate_processing_fee, :net_received,
                  :prorate_customer_order_payment_processing_fee,
                  :prorate_rounding_tax, :prorate_rounding_service_charge,
                  :sale_detail_modifier_childs,
                  :row_index, :parent_row_index, :prorate_internal_subsidize,
                  :total_amount_prorate_discount_for_tax, :total_amount_prorate_discount_for_service_charge,
                  :prorate_discount_product, :prorate_discount_order_level
  end

  class_methods do
    def associated_primary_key(audit)
      audit.auditable.product_name
    end
  end

  def cost
    return nil if meta.nil?

    meta['cost'].to_d
  end

  def meta_option_set_option_id
    return nil if meta.nil?

    meta['option_set_option_id']
  end

  def should_rule_cost_included_in_parent?
    if option_set.blank?
      false
    else
      option_set.rule_cost_included_in_parent
    end
  end

  def assign_rule_cost_and_metadata
    self.rule_cost_included_in_parent = should_rule_cost_included_in_parent?
    self.meta ||= {}

    # This assignment is part of variant parent rule.
    # For now it's only used in sales by product report.
    # By default data will be in child.
    # If we find that rule_cost_included_in_parent is true, we make them 0 (moved to parent).
    self.meta = meta.merge(parent_rule_metadata)
    self.option_set_id = meta['option_set_id']
    self.option_set_option_id = meta['option_set_option_id']
  end

  def type_discount
    product_id.nil? && total_line_amount.negative? && free_of_charge == false
  end

  def type_free_of_charge_discount?
    product_id.nil? && total_line_amount.negative? && free_of_charge
  end

  def option_set_from_self
    return nil if meta.nil?

    if sale_detail_transaction.calculated_option_set_options.nil?
      return product.option_set_options.with_deleted.find_by(id: meta['option_set_option_id'])&.option_set
    end

    option_set_option_id = meta['option_set_option_id'].to_i
    sale_detail_transaction.calculated_option_set_options[option_set_option_id]&.option_set
  end

  def option_set_option_from_detail
    return nil if customer_order.blank?

    found_option_set_option = nil
    customer_order.customer_order_details.each do |customer_order_detail|
      customer_order_detail_metadata = customer_order_detail.metadata
      return nil if customer_order_detail_metadata.blank?
      return nil if customer_order_detail_metadata['option_sets'].blank?

      customer_order_detail_metadata['option_sets'].each do |option_set_metadata|
        option_set_options_metadata = option_set_metadata['option_set_options']
        next if option_set_options_metadata.blank?

        found_option_set_option = option_set_options_metadata.detect do |option_set_option_metadata|
          option_set_option_metadata['product_id'] == product_id &&
            option_set_option_metadata['price'].to_d == price.to_d
        end

        break if found_option_set_option.present?
      end

      break if found_option_set_option.present?
    end

    return nil if found_option_set_option.nil?

    product.option_set_options.find_by(id: found_option_set_option['id'], product_id: found_option_set_option['product_id'])
  end

  def option_set
    return nil if product_id.nil? && selected_product.nil?

    option_set_from_self || option_set_option_from_detail&.option_set
  end

  def parent_rule_metadata
    {
      parent_rule_total_line_amount: rule_cost_included_in_parent ? 0.to_d : total_line_amount,
      parent_rule_prorate_discount: rule_cost_included_in_parent ? 0.to_d : prorate_discount.to_d,
      parent_rule_prorate_surcharge: rule_cost_included_in_parent ? 0.to_d : prorate_surcharge.to_d,
      sales_by_free_of_charge: rule_cost_included_in_parent ? 0.to_d : prorate_free_of_charge_before_tax_report_usage_only.to_d,
      parent_rule_total_amount_prorate_discount: rule_cost_included_in_parent ? 0.to_d : total_amount_prorate_discount
    }
  end

  def assign_sale_product_ids
    if should_rule_cost_included_in_parent?
      [product_id, sale_detail_transaction&.product_id].flatten.compact
    else
      [product_id]
    end
  end

  def assign_sale_product_ids_for_existing
    if rule_cost_included_in_parent
      [product_id, sale_detail_transaction&.product_id].flatten.compact
    else
      [product_id]
    end
  end

  def zero_adjustment_amount?
    product_id.nil? || zero_quantity?
  end

  def zero_quantity?
    quantity.zero? || sale_detail_transaction.quantity.zero?
  end

  def set_tax_setting(params_product, params_meta)
    return unless params_product.present? && (params_meta.blank? || params_meta['all_you_can_eat_parent_id'].blank?)

    self.tax_id = sale_detail_transaction.tax_id
    self.tax_setting = sale_detail_transaction.tax_setting
    self.tax_name = sale_detail_transaction.tax_name
    self.tax_rate = sale_detail_transaction.tax_rate.presence || 0
  end

  private

  def apply_denorm_fields
    return if Flipper.enabled?(:prevent_fill_sale_denorm_fields)

    # denorm fields, status default to ok
    self.brand_id = sale_transaction.brand_id
    self.location_id = sale_transaction.location_id
    self.local_sales_time = sale_transaction.local_sales_time
    self.promo_id = meta['promo_id']
  end

  def validate_same_resource
    if selected_product.present? && selected_product.brand_id != sale_detail_transaction.sale_transaction.brand_id
      errors.add(:product,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
    if selected_product_unit.present? && selected_product_unit.brand_id != sale_detail_transaction.sale_transaction.brand_id
      errors.add(:product_unit,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end

  def check_change_object
    self.product_unit_id = selected_product.sell_unit_id if selected_product.present?

    if product_id_changed? || description.present? || product_unit_id_changed?
      if product_id.nil?
        self.product_name = description
        self.product_unit_conversion_qty = 1
        self.option_set_quantity = 1
      else
        self.product_name = selected_product.name
        self.product_sku = selected_product.sku
        self.product_upc = selected_product.upc
        self.product_description = selected_product.description

        # for filtering by product id in sales by report
        self.sale_product_ids = assign_sale_product_ids

        set_tax_setting(selected_product, meta)

        self.product_unit_name = selected_product_unit&.name
        self.product_unit_conversion_qty = selected_product.convert_quantity_to_base(product_unit_id, 1)

        sale_transaction = sale_detail_transaction.sale_transaction
        recipe_order_type_id = sale_detail_transaction.order_type_id.presence || sale_transaction.order_type_id
        find_and_populate_product_recipe(sale_transaction, self, recipe_order_type_id)
        set_option_set_quantity
      end
    end
  end

  def handle_child_index
    return if parent_row_index.blank?

    parent_modifier = sale_detail_transaction.sale_detail_modifiers.detect { |modifier| modifier.row_index == parent_row_index }
    return if parent_modifier.blank?

    self.parent_id = parent_modifier.id
  end

  def set_option_set_quantity
    # New POS Will send option_set_quantity
    return if option_set_quantity.present?

    # Handle Older POS
    option_set_option = if meta.present? && meta['option_set_option_id'].present?
                          OptionSetOption.find_by(product_id: product_id, id: meta['option_set_option_id'])
                        end

    self.option_set_quantity = option_set_option&.quantity || 1
  end

  def selected_product
    return product if sale_detail_transaction.calculated_modifier_products.blank?

    found_product = sale_detail_transaction.calculated_modifier_products[product_id]
    return product if found_product.blank?

    found_product
  end

  def selected_product_unit
    calculated_modifier_product_units = sale_detail_transaction.calculated_modifier_product_units
    return product_unit if calculated_modifier_product_units.blank?

    found_product_unit = calculated_modifier_product_units[product_unit_id]
    return product_unit if found_product_unit.blank?

    found_product_unit
  end
end
# rubocop:enable Metrics/BlockLength
