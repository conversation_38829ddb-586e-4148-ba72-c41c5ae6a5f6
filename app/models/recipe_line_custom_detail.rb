class RecipeLineCustomDetail < ApplicationRecord
  acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })

  belongs_to :recipe_line_custom
  belongs_to :product
  belongs_to :product_unit
  validates :recipe_line_custom, :product, :product_id, :quantity, presence: true
  validates :product_id, uniqueness: { scope: :recipe_line_custom_id }
  validates :quantity, numericality: { greater_than: 0 }
  validate :product_unit_must_belong_to_product
  validate :product_must_not_recursive

  def select_suitable_product_recipe_custom(location_id: nil, default_storage_section: nil)
    storage_section_id = product.product_category
                                &.storage_section_mappings_out
                                &.find_by(location_id: location_id)
                                &.storage_section_id

    storage_section_id = default_storage_section&.id if storage_section_id.blank?

    { product_id: product_id, quantity: quantity, product_unit_id: product_unit_id,
      product_unit_name: product_unit.name, product_no_stock: product.no_stock,
      product_unit_conversion_qty: product.convert_quantity_to_base(product_unit_id, 1),
      storage_section_id: storage_section_id, product: product }
  end

  private

  def product_unit_must_belong_to_product
    unless product.allowable_product_units.include? product_unit_id
      errors.add(:product_unit, I18n.t('activerecord.errors.models.recipe.attributes.product_unit.not_belong'))
    end
  end

  def product_must_not_recursive
    if recipe_line_custom.recipe.product_id == product_id
      errors.add(:product_id, I18n.t('activerecord.errors.models.recipe.attributes.product.recursive'))
    end
  end
end
