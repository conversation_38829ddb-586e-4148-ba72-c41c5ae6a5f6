class Report::Queries::Clickhouse::SalesPromotionBySaleTransactionQuery < Report::Queries::BaseReportQuery
  include Restaurant::Modules::Report::SalesPromotions::Clickhouse::BaseQuery

  MASKING_CARD_CODE_LENGTH = 8

  protected

  def generate_variable(params)
    super

    @action_type = Restaurant::Constants::PROMOTION_ACTION

    @export_mode = params[:export_mode] || 'combined'
    @is_select_all_location = @export_mode == 'combined' ? @is_select_all_location : false
    @keyword = params[:keyword].to_s
  end

  def generate_data
    report_data = []

    manual_promo_query = base_query_transaction_manual_promo(@location_ids)
    manual_promo_sales_with_applied_promo_query = base_query_transaction_manual_promo_with_applied_promos(@location_ids)
    promo_count = build_total_data_promo
    manual_promo_count = build_total_data_manual_promo(manual_promo_query)
    manual_and_promo_count = promo_count + manual_promo_count
    manual_promo_with_applied_promo_count = build_total_data_manual_promo(manual_promo_sales_with_applied_promo_query)

    report_data = populate_data_promo(report_data, promo_count)
    report_data = populate_data_manual_promo(report_data, promo_count, manual_promo_query)
    report_data = populate_data_manual_promo(report_data, manual_and_promo_count, manual_promo_sales_with_applied_promo_query)
    total_data = promo_count + manual_promo_count + manual_promo_with_applied_promo_count

    [
      report_data,
      total_data
    ]
  end

  def generate_headers
    row = ::Restaurant::Services::Report::RowBuilder.new
    row.add_text(I18n.t('report.sales_feed.header.location_name'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.account_transaction.header.transaction_date'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.disbursement.header.sales_no'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.promotion_name'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.receipt_no'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.amount_before_promo'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.promo_amount'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.in_house'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.external'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.card_no'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.approval_code'), weight: FONT_WEIGHT_ROW)
       .add_text(I18n.t('report.sales_promotion.header.promotion_code'), weight: FONT_WEIGHT_ROW)

    row.build
  end

  private

  def populate_data_promo(report_data, promo_count)
    # skip populate data when not export
    # and total data <= previous page * item per page
    return report_data if !export? && promo_count <= (@current_page - 1) * @item_per_page

    promos, ids = build_data_promo

    @mapping_approval_code = ::Report::Models::SaleTransaction.where(id: ids).select('id, metadata').index_by(&:id)
    @sales_returns = Report::Models::SalesReturn.where(sale_transaction_id: ids).group_by(&:sale_transaction_id)

    last_id = nil
    promos.each do |sale_transaction|
      if last_id != sale_transaction['id']
        report_data += report_data_sales_return(last_id) if last_id.present?
        report_data << report_data_index_zero(sale_transaction)
      else
        report_data << report_data_index(sale_transaction)
      end

      last_id = sale_transaction['id']
    end

    report_data += report_data_sales_return(last_id)

    report_data
  end

  def report_data_sales_return(last_id)
    sales_returns = @sales_returns[last_id]
    rows = []

    return rows if sales_returns.nil?

    style_options = { size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW }
    style_options_no_opacity = style_options.merge({ opacity: nil })

    sales_returns.each do |sales_return|
      row = ::Restaurant::Services::Report::RowBuilder.new
      row.add_text(sales_return.location_name, **style_options_no_opacity)
         .add_text(ApplicationHelper.format_date_report(sales_return.sales_return_local_sales_time), **style_options_no_opacity)
         .add_url_text_when_object_is_hash(sales_return.refund_no, sales_return.id, 'SalesReturn', **style_options)
         .add_text('Discount Refund', **style_options_no_opacity)
         .add_text('-', **style_options_no_opacity)
         .add_money(sales_return.gross_refund * -1, @brand, is_export: export?, **style_options_no_opacity)
         .add_money(sales_return.total_discount_before_tax_refund * -1, @brand, is_export: export?, **style_options_no_opacity)
         .add_money(sales_return.total_discount_before_tax_refund * -1, @brand, is_export: export?, **style_options_no_opacity)
         .add_money(0, @brand, is_export: export?, **style_options_no_opacity)
         .add_text('-', **style_options_no_opacity)
         .add_text('-', **style_options_no_opacity)
         .add_text('-', **style_options_no_opacity)

      rows << row.build
    end

    return rows
  end

  def populate_data_manual_promo(report_data, promo_count, base_query)
    # skip populate data when not export
    # and previous page * item per page <= total data promo
    return report_data if !export? && @current_page * @item_per_page <= promo_count

    manual_promos, ids = build_data_manual_promo(promo_count, base_query)

    @mapping_approval_code = ::Report::Models::SaleTransaction.where(id: ids).select('id, metadata').index_by(&:id)
    @sales_returns = Report::Models::SalesReturn.where(sale_transaction_id: ids).group_by(&:sale_transaction_id)

    is_manual_promo = true
    manual_promos.each do |manual_promo|
      report_data << report_data_index_zero(manual_promo, is_manual_promo)
      report_data += report_data_sales_return(manual_promo['id'])
    end

    report_data
  end

  def build_limit_offset_based_on_total_promo_count(total_promo_count)
    if export?
      offset = nil
      limit = nil
    else
      remaining_data_on_current_page = @current_page * @item_per_page - total_promo_count
      if remaining_data_on_current_page <= @item_per_page
        offset = 0
        limit = remaining_data_on_current_page
      else
        limit = @item_per_page
        offset = remaining_data_on_current_page - limit
      end
    end

    [limit, offset]
  end

  def build_data_manual_promo(promo_count, base_query)
    limit, offset = build_limit_offset_based_on_total_promo_count(promo_count)

    select_fields = ['public_sale_transactions.id',
                     'public_sale_transactions.location_name',
                     'public_sale_transactions.local_sales_time',
                     'public_sale_transactions.sales_no',
                     'public_sale_transactions.receipt_no',
                     'public_sale_transactions.gross_sales',
                     '(COALESCE(public_sale_transactions.discount_fee,0) -
                public_sale_transactions.loyalty_discount_fee +
                COALESCE(modifier_discounts.discount_line,0)) AS applied_promo_amount']
    query = base_query
            .select(select_fields)
            .order('public_sale_transactions.id DESC', 'applied_promo_amount ASC')

    if export?
      build_data_promo_for_export(query)
    else
      build_data_promo_result(query, limit, offset)
    end
  end

  def build_data_promo_for_export(query)
    result = []
    result_ids = []
    limit = 5000
    offset = 0

    loop do
      new_result, new_result_ids = build_data_promo_result(query, limit + 1, offset)

      result += new_result[...limit]
      result_ids += new_result_ids[...limit]

      break if new_result_ids.size <= limit

      offset += limit
    end

    [result, result_ids]
  end

  def build_data_promo_result(query, limit, offset)
    result = []
    result_ids = []

    query_sql = query.to_sql
    query_sql += " Limit #{limit} OFFSET #{offset}" if offset.present? && limit.present?

    ::Clickhouse::Models::SaleTransaction.with_deleted.from("(#{query_sql}) AS public_sale_transactions").each do |sale_transaction|
      result << sale_transaction
      result_ids << sale_transaction['id']
    end

    [result, result_ids]
  end

  def build_total_data_manual_promo(base_query)
    base_query.count('public_sale_transactions.id')
  end

  def build_data_promo
    query = base_query_data_promo.order('public_sale_transactions.id desc', 'promo_name asc')

    if export?
      build_data_promo_for_export(query)
    else
      build_data_promo_result(query, @item_per_page, @item_offset)
    end
  end

  def build_total_data_promo
    base_query_data_promo.count('public_sale_transactions.id')
  end

  def base_query_data_promo
    promo_ids = ::Report::Models::Promo.with_deleted
                                       .where(brand_id: @brand_id)
                                       .where('promos.name ilike :keyword', { keyword: "%#{@keyword}%" })
                                       .where('promos.start_date <= ?', @end_date.to_date.end_of_day)
                                       .where('(promos.end_date >= ? or promos.end_date is null)', @start_date.to_date.beginning_of_day)
                                       .pluck(:id)

    build_inner_sales_query(promo_ids)
  end

  def report_data_index_zero(sale_transaction, manual_promo = false)
    applied_promo_amount, discount_in_house, discount_external = build_applied_promo_amount(sale_transaction, manual_promo)

    promo_name = if manual_promo
                   "#{I18n.t('report.sales_promotion.manual_discount')} #{sale_transaction['applied_promo_amount']}"
                 else
                   sale_transaction['promo_name']
                 end

    style_options = { size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW }
    style_options_no_opacity = style_options.merge({ opacity: nil })

    receipt_no = sale_transaction['receipt_no'].presence || '-'
    row = ::Restaurant::Services::Report::RowBuilder.new
    row.add_text(sale_transaction['location_name'], **style_options_no_opacity)
       .add_text(ApplicationHelper.format_date_report(sale_transaction['local_sales_time']), **style_options_no_opacity)
       .add_url_text_when_object_is_hash(sale_transaction['sales_no'], sale_transaction['id'], 'SaleTransaction', **style_options)
       .add_text(promo_name, **style_options_no_opacity)
       .add_text(receipt_no, **style_options_no_opacity)
       .add_money(sale_transaction['gross_sales'], @brand, is_export: export?, **style_options_no_opacity)
       .add_money(applied_promo_amount, @brand, is_export: export?, **style_options_no_opacity)
       .add_money(discount_in_house, @brand, is_export: export?,  **style_options_no_opacity)
       .add_money(discount_external, @brand, is_export: export?,  **style_options_no_opacity)
       .add_text(build_card_code_masking(@mapping_approval_code[sale_transaction['id']].metadata['payment_card_codes']), **style_options_no_opacity)
       .add_text(build_card_code_masking(@mapping_approval_code[sale_transaction['id']].metadata['payment_approval_codes'],
                                         masked: false), **style_options_no_opacity)
       .add_text(build_applied_promo_code(sale_transaction['promo_codes']), **style_options_no_opacity)

    row.build
  end

  def build_card_code_masking(card_code, masked: true)
    return '-' if card_code.blank?

    card_code.map do |splitted_card_code|
      splitted_card_code_length = splitted_card_code.length

      if !masked || splitted_card_code_length <= MASKING_CARD_CODE_LENGTH
        splitted_card_code
      else
        splitted_card_code[...MASKING_CARD_CODE_LENGTH] + ('*' * (splitted_card_code_length - MASKING_CARD_CODE_LENGTH))
      end
    end.join(',')
  end

  def build_applied_promo_code(promo_codes)
    return '-' if promo_codes.blank?

    parsed_promo_codes = JSON.parse(promo_codes)
    return '-' if parsed_promo_codes.blank?

    # TODO: remove else
    # bug found when apply promo for delivery order fix on https://bitbucket.org/runchise/runchise/pull-requests/11035
    if parsed_promo_codes.is_a?(Array)
      mapped_promo_codes = parsed_promo_codes.map { |parsed_promo_code| parsed_promo_code['code'] }.compact
      return '-' if mapped_promo_codes.blank?

      mapped_promo_codes.join(',')
    else
      parsed_promo_codes['code']
    end
  end

  def report_data_index(sale_transaction)
    applied_promo_amount, discount_in_house, discount_external = build_applied_promo_amount(sale_transaction)

    style_options = { size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW }

    row = ::Restaurant::Services::Report::RowBuilder.new
    row.add_empty(3, **style_options)
       .add_text_no_opacity(sale_transaction['promo_name'], **style_options)
       .add_empty(2, **style_options)
       .add_money(applied_promo_amount, @brand, is_export: export?, **style_options, opacity: nil)
       .add_money(discount_in_house, @brand, is_export: export?, **style_options, opacity: nil)
       .add_money(discount_external, @brand, is_export: export?, **style_options, opacity: nil)
       .add_empty(2, **style_options)
       .add_text(build_applied_promo_code(sale_transaction['promo_codes']), **style_options, opacity: nil)

    row.build
  end

  def build_applied_promo_amount(sale_transaction, manual_promo = false)
    applied_promo_amount = sale_transaction['applied_promo_amount'].to_d
    discount_in_house_cost = manual_promo ? applied_promo_amount : applied_promo_amount * sale_transaction['discount_in_house_cost'].to_d / 100.0
    discount_external_cost = applied_promo_amount * sale_transaction['discount_external_cost'].to_d / 100.0

    [applied_promo_amount, discount_in_house_cost, discount_external_cost]
  end

  # rubocop:disable Metrics/MethodLength
  def build_inner_sales_query(promo_ids)
    return [] if promo_ids.blank?

    subquery_sales = ::Clickhouse::Models::SaleTransaction.with_deleted
                                                          .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                .strftime('%Y-%m-%d %H:%M:%S'),
                                                                             @end_date.to_date.end_of_day
                                                                                             .strftime('%Y-%m-%d %H:%M:%S'))
                                                          .where(location_id: @location_ids)
                                                          .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    query = ::Clickhouse::Models::SaleTransaction.from("(#{subquery_sales}) AS public_sale_transactions")
                                                 .where('public_sale_transactions.status = 0')
                                                 .where('notEmpty(public_sale_transactions.promo_ids)')
                                                 .where("hasAny(public_sale_transactions.promo_ids, [#{promo_ids.join(',')}])")
                                                 .joins(Arel.sql("
                                          ARRAY JOIN JSONExtractArrayRaw(public_sale_transactions.applied_promos) AS promo
                                        "))

    query = query.where("JSONExtractInt(promo, 'id') IN (#{promo_ids.join(',')})")

    query.select(
      'public_sale_transactions.id',
      'public_sale_transactions.location_id',
      'public_sale_transactions.location_name',
      'public_sale_transactions.local_sales_time',
      'public_sale_transactions.sales_no',
      'public_sale_transactions.receipt_no',
      'public_sale_transactions.gross_sales +
                  public_sale_transactions.total_prorate_surcharge_before_tax AS gross_sales',
      "JSONExtractString(promo, 'id') AS promo_id",
      "JSONExtractString(promo, 'name') AS promo_name",
      "JSONExtractString(promo, 'amount') AS applied_promo_amount",
      "JSONExtractString(promo, 'promo_codes') AS promo_codes",
      "JSONExtractFloat(promo, 'discount_in_house_cost') AS discount_in_house_cost",
      "JSONExtractFloat(promo, 'discount_external_cost') AS discount_external_cost"
    )
  end
  # rubocop:enable Metrics/MethodLength
end
