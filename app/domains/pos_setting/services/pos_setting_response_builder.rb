class PosSetting::Services::PosSettingResponseBuilder
  include MappingObjectHelper

  def initialize(pos_setting:, brand:)
    @pos_setting = pos_setting
    @location = @pos_setting.location
    @brand = brand
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def call
    generate_mapped_object
    global_setting = @brand.fetch_qr_order_setting
    dine_in_order_type = {
      id: @pos_setting.enabled? ? @pos_setting.dine_in_order_type_id : global_setting.dine_in_order_type_id,
      name: @pos_setting.enabled? ? @pos_setting.dine_in_order_type&.name : global_setting.dine_in_order_type&.name
    }
    takeaway_order_type = {
      id: @pos_setting.enabled? ? @pos_setting.takeaway_order_type_id : global_setting.takeaway_order_type_id,
      name: @pos_setting.enabled? ? @pos_setting.takeaway_order_type&.name : global_setting.takeaway_order_type&.name
    }

    order_types = []
    order_type_ids = []
    if dine_in_order_type[:id].present?
      order_types << dine_in_order_type
      order_type_ids << dine_in_order_type[:id]
    end
    if takeaway_order_type[:id].present?
      order_types << takeaway_order_type
      order_type_ids << takeaway_order_type[:id]
    end

    @pos_setting
      .attributes
      .except(
        :enable_qr_order, :enable_open_bill, :enable_closed_bill, :enable_dine_in, :enable_takeaway,
        :guest_mode_default, :require_guest_phone_number, :require_guest_name, :qr_preferences_for_open_bill
      )
      .merge(
        build_pos_device_count
      )
      .merge(
        {
          # TODO: deprecate order_types later since WBO not used this column anymore if POS not use this anymore
          order_types: order_types,
          preorder_order_types: build_id_and_name_from_ids_with_mapped_objects(@pos_setting.preorder_order_type_ids, @order_types),
          order_type_ids: order_type_ids,
          order_type_id: dine_in_order_type[:id].presence || nil,
          rounding_order_types: build_id_and_name_from_ids_with_mapped_objects(@pos_setting.rounding_order_type_ids, @order_types),
          required_table_order_types: build_id_and_name_from_ids_with_mapped_objects(@pos_setting.required_table_order_type_ids, @order_types),
          order_type: dine_in_order_type[:id].present? ? dine_in_order_type : nil,
          notify_food_is_ready: @pos_setting.enabled? ? @pos_setting.notify_food_is_ready : global_setting.notify_food_is_ready,
          notify_food_is_ready_type: @pos_setting.enabled? ? @pos_setting.notify_food_is_ready_type : global_setting.notify_food_is_ready_type,
          kds_integrated: @pos_setting.kds_integrated?,
          check_for_update: @brand.auto_update
        }
      ).merge(
        dine_in_order_type: dine_in_order_type,
        takeaway_order_type: takeaway_order_type
      ).merge(
        build_qr_order_flag
      )
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  private

  def generate_ids
    order_type_ids = @pos_setting.rounding_order_type_ids + @pos_setting.required_table_order_type_ids +
                     @pos_setting.order_type_ids + @pos_setting.preorder_order_type_ids

    {
      order_type_ids: order_type_ids
    }
  end

  def generate_mapped_object
    calculated_ids = generate_ids

    @order_types = @brand.order_types.where(id: calculated_ids[:order_type_ids]).index_by(&:id)
  end

  def build_pos_device_count
    pos_server_count = 0
    pos_client_count = 0

    @location.devices.each do |device|
      if device.is_server
        pos_server_count += 1
      else
        pos_client_count += 1
      end
    end

    {
      pos_server_count: pos_server_count,
      pos_client_count: pos_client_count
    }
  end

  def build_qr_order_flag
    setting = DineIn::Poro::QrDineInSetting.new(location: @location, pos_setting: @pos_setting)

    {
      enable_qr_order: setting.enable_qr_order,
      enable_open_bill: setting.enable_open_bill,
      enable_closed_bill: setting.enable_closed_bill,
      enable_dine_in: setting.enable_dine_in,
      enable_takeaway: setting.enable_takeaway,
      guest_mode_default: setting.guest_mode_default,
      require_guest_phone_number: setting.require_guest_phone_number,
      require_guest_name: setting.require_guest_name,
      qr_preferences_for_open_bill: setting.qr_preferences_for_open_bill
    }
  end
end
