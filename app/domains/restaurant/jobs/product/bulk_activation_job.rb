class Restaurant::Jobs::Product::BulkActivationJob < ApplicationJob
  Result = Struct.new(
    :success_count,
    :success_products,
    :failed_products,
    keyword_init: true
  )

  def perform(brand_id:, user_id:, activate:, import_record_id:)
    brand = Brand.find(brand_id)
    user = User.find(user_id)
    user.selected_brand = brand
    import_data = ImportData.find(import_record_id)
    product_ids = import_data.payload['product_ids']

    products = build_products(product_ids, user)
    result   = process_products(products, user, activate)

    finalize_import(import_data, products.count, result, activate)
  end

  private

  def build_products(product_ids, user)
    ProductQuery.find_filtered_products(current_user: user, product_ids: product_ids)
  end

  def process_products(products, user, activate)
    success_products = []
    failed_products  = []
    success_count    = 0
    error_index      = 0

    products.each do |product|
      if activate
        product.reactivate!(user: user) unless product.activated?
      else
        product.deactivate!(user) unless product.deactivated?
      end

      success_products << product.name
      success_count += 1
    rescue ActiveRecord::RecordInvalid, ::Errors::InvalidParamsError, ::Errors::UnprocessableEntity => e
      error_messages = e.try(:record).try(:errors).try(:full_messages).presence || e.message
      failed_products << {
        'index' => error_index,
        'errors' => error_messages
      }
      error_index += 1
    end

    Result.new(
      success_count: success_count,
      success_products: success_products,
      failed_products: failed_products
    )
  end

  def finalize_import(import_data, product_count, result, activate)
    import_data.update!(
      payload: import_data.payload.merge(
        'product_names' => result.success_products,
        'errors' => result.failed_products
      ),
      total_count: product_count,
      status: result.failed_products.any? ? 'failed' : 'finished'
    )

    send_notification(import_data, product_count, result, activate)
  end

  def send_notification(import_data, attempt_count, result, activate)
    notif_params = {
      brand_id: import_data.brand_id,
      user_id_multiple_locations: import_data.user_id,
      disable_pn: true,
      notification_type: result.failed_products.blank? ? 'bulk_product_activation_success' : 'bulk_product_activation_failed',
      action: NotificationHelper::IMPORT_ACTION_PRODUCT,
      resource_id: import_data.id,
      count: result.success_count,
      total_count: attempt_count,
      metadata: {
        success_count: result.success_count,
        activate: activate
      }
    }

    NotificationJob.perform_later(SecureRandom.uuid, notif_params)
  end
end
