class Restaurant::Jobs::OptionSet::SaleDetailModifierCostUpdateJob < ApplicationJob
  queue_as :default

  def perform(option_set_id, location_ids, rule_cost_included_in_parent)
    process(option_set_id, location_ids, rule_cost_included_in_parent)
  end

  # rubocop:disable Metrics/MethodLength
  def process(option_set_id, location_ids, rule_cost_included_in_parent)
    option_set = ::OptionSet.find_by(id: option_set_id)
    return if option_set.blank?

    brand = option_set.brand
    products_ids = option_set.option_set_options.pluck(:product_id)

    sale_detail_transaction_ids,
    sale_detail_modifier_ids = fetch_sale_detail_and_modifier_ids(brand, location_ids, option_set_id, products_ids)

    ::SaleDetailModifier.where(id: sale_detail_modifier_ids).find_in_batches(batch_size: 500) do |sale_detail_modifiers|
      batch_sale_modifier = sale_detail_modifiers.map do |sale_detail_modifier|
        sale_detail_modifier.rule_cost_included_in_parent = rule_cost_included_in_parent
        sale_detail_modifier
      end

      ::SaleDetailModifier.import(
        batch_sale_modifier.compact,
        validate: false,
        on_duplicate_key_update: { conflict_target: [:id], columns: %i[rule_cost_included_in_parent] },
        timestamps: false
      )
    end

    klass = if rule_cost_included_in_parent
              Restaurant::Services::SaleDetailTransactions::Cost::MoveToParentService
            else
              Restaurant::Services::SaleDetailTransactions::Cost::MoveToModifierService
            end

    update_cost_and_sale_product_ids(klass, sale_detail_transaction_ids)

    # update parent_rule_metadata
    # update the rule_cost_included_in_parent first before updating
    ::SaleDetailModifier
      .where(id: sale_detail_modifier_ids)
      .find_in_batches(batch_size: 500) do |sale_detail_modifiers|
      batch_sale_modifier = sale_detail_modifiers.map do |sale_detail_modifier|
        meta = sale_detail_modifier.meta
        meta = meta.merge(sale_detail_modifier.parent_rule_metadata)
        sale_detail_modifier.meta = meta
        sale_detail_modifier
      end

      ::SaleDetailModifier.import(
        batch_sale_modifier.compact,
        validate: false,
        on_duplicate_key_update: { conflict_target: [:id], columns: %i[meta] },
        timestamps: false
      )
    end

    ::SaleDetailTransaction
      .includes(:sale_detail_modifiers)
      .where(id: sale_detail_transaction_ids).find_in_batches(batch_size: 500) do |sale_detail_transactions|
      batch_sale_detail = sale_detail_transactions.map do |sale_detail_transaction|
        meta = sale_detail_transaction.meta
        meta = meta.merge(sale_detail_transaction.parent_rule_metadata)
        sale_detail_transaction.meta = meta
        sale_detail_transaction
      end

      ::SaleDetailTransaction.import(
        batch_sale_detail.compact,
        validate: false,
        on_duplicate_key_update: { conflict_target: [:id], columns: %i[meta] },
        timestamps: false
      )
    end

    option_set.parent_rule_update_unlock!
  end
  # rubocop:enable Metrics/MethodLength

  # rubocop:disable Metrics/MethodLength
  def fetch_sale_detail_and_modifier_ids(brand, location_ids, option_set_id, products_ids)
    last = SaleDetailModifier.last
    return [[], []] if last.nil?

    last_id = last.id
    beg_id = 1

    sale_detail_ids = []
    sale_modifier_ids = []

    while beg_id < last_id
      last_q_id = beg_id + 9999

      sub_query =
        ::Clickhouse::Models::SaleDetailModifier
        .with_deleted
        .by_field_option_set_id_and_product_ids(
          brand.id,
          location_ids,
          option_set_id,
          products_ids
        )
        .where("id between #{beg_id} and #{last_q_id}")
        .group('id')
        .select("id AS modifier_id,
                  argMax(deleted, _peerdb_version) AS deleted,
                  argMax(sale_detail_transaction_id, _peerdb_version) AS sale_detail_transaction_id")
        .to_sql

      modifiers = ::Clickhouse::Models::SaleDetailModifier
                  .from("(#{sub_query}) AS public_sale_detail_modifiers")
                  .select("public_sale_detail_modifiers.modifier_id,
                            public_sale_detail_modifiers.sale_detail_transaction_id")

      sale_detail_ids += modifiers.map(&:sale_detail_transaction_id)
      sale_modifier_ids += modifiers.map(&:modifier_id)

      beg_id += 10_000
    end

    [sale_detail_ids, sale_modifier_ids]
  end
  # rubocop:enable Metrics/MethodLength

  # update cost and sale product ids
  def update_cost_and_sale_product_ids(klass, sale_detail_transaction_ids)
    ::SaleDetailTransaction
      .where(id: sale_detail_transaction_ids)
      .find_in_batches(batch_size: 500) do |sale_detail_transactions|
      batch_sale_detail = []
      batch_sale_modifier = []

      sale_detail_transactions.each do |sale_detail_transaction|
        result = klass.new(sale_detail_transaction).call

        batch_sale_detail << result[:sale_detail_transaction]
        batch_sale_modifier += result[:modifiers]
      end

      if batch_sale_detail.present?
        ::SaleDetailTransaction.import(
          batch_sale_detail.flatten.compact,
          validate: false,
          on_duplicate_key_update: { conflict_target: [:id], columns: %i[meta sale_product_ids] },
          timestamps: false
        )
      end

      if batch_sale_modifier.present?
        ::SaleDetailModifier.import(
          batch_sale_modifier.flatten.compact,
          validate: false,
          on_duplicate_key_update: { conflict_target: [:id], columns: %i[meta sale_product_ids] },
          timestamps: false
        )
      end
    end
  end
end
