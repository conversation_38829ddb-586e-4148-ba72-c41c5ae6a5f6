Restaurant::Services::Recipe::RecipePresenter = Struct.new(:recipe, :location, :unit_conversion_show, :recipe_lines_show) do
  def call
    {
      id: recipe.id,
      product: {
        id: recipe_product.id,
        name: recipe_product_name,
        internal_distribution_type: recipe_product.internal_distribution_type
      },
      product_unit: {
        id: recipe.product_unit.id,
        name: recipe.product_unit.name
      },
      product_unit_conversions: recipe_product.valid_product_unit_conversions(false, unit_conversion_show),
      recipe_type: recipe.recipe_type,
      expected_yield: recipe.expected_yield,
      yield_storage_section: generate_storage_section(recipe),
      status: recipe.status,
      instruction: recipe.instruction,
      shelf_life: recipe.shelf_life,
      shelf_life_type: recipe.shelf_life_type,
      updated_at: recipe.updated_at,
      lines: generate_recipe_lines(recipe, recipe_lines_show, location),
      line_swap_products: generate_recipe_line_swap_products(recipe, recipe_lines_show),
      visible: recipe_lines_show ? true : false,
      lines_customs: recipe.recipe_line_customs.order(id: :asc).map do |line_custom|
                       generate_line_custom_detail_response(line_custom)
                     end
    }
  end

  private

  def generate_recipe_lines(recipe, recipe_lines_show, location)
    return [] unless recipe_lines_show

    auto_substitute = recipe.brand.use_recipe_substitute_on_no_stock
    stock_outs = Restaurant::Services::TransactionStockOutsAllocation.new(location)
    production_setting = recipe.brand.production_setting
    recipe.recipe_lines.order(id: :asc).map do |line|
      generate_line_detail_response(line, location, recipe_lines_show,
                                    stock_outs: stock_outs, recipe: recipe, auto_substitute: auto_substitute,
                                    production_setting: production_setting)
    end
  end

  def generate_recipe_line_swap_products(recipe, recipe_lines_show)
    return [] unless recipe_lines_show

    recipe.recipe_line_swap_products.order(id: :asc).map do |line|
      generate_line_swap_product_detail_response(line, recipe_lines_show)
    end
  end

  def generate_line_swap_product_detail_response(line, recipe_lines_show)
    {
      id: line.id,
      product_from: generate_product(line.product_from, recipe_lines_show),
      product_to: line.product_to.present? ? generate_product(line.product_to, recipe_lines_show) : nil,
      ratio: line.ratio
    }
  end

  def generate_line_detail_response(line, location, recipe_lines_show, **args)
    auto_substitute = args[:auto_substitute]
    product_info = line.select_suitable_product_recipe(
      use_substitute: auto_substitute,
      multiplier_quantity: 1,
      stock_outs: args[:stock_outs],
      production_setting: args[:production_setting]
    )

    {
      id: line.id,
      product: generate_product(line.product, recipe_lines_show),
      product_unit: generate_product_unit(line.product_unit, recipe_lines_show),
      quantity: line.quantity,
      stock: line.product.stock(location_id: location.id, unit_id: line.product_unit_id),
      is_selected: product_info[:product_id] == line.product_id,
      storage_section: generate_storage_section(line),
      recipe_line_substitutes: line.recipe_line_substitutes.includes(:product, :product_unit).reorder(:sequence).map do |substitute|
        {
          id: substitute.id,
          product: generate_product(substitute.product, recipe_lines_show),
          product_unit: generate_product_unit(substitute.product_unit, recipe_lines_show),
          quantity: substitute.quantity,
          is_selected: product_info[:product_id] == substitute.product_id,
          stock: substitute.product.stock(location_id: location.id, unit_id: substitute.product_unit_id),
          storage_section: generate_storage_section(substitute)
        }
      end
    }
  end

  def generate_product(product, recipe_lines_show)
    if recipe_lines_show
      {
        id: product.id,
        name: product.name,
        sku: product.sku,
        image_url: product.image_url
      }
    else
      {
        id: product.id
      }
    end
  end

  def generate_product_unit(product_unit, recipe_lines_show)
    if recipe_lines_show
      {
        id: product_unit.id,
        name: product_unit.name
      }
    else
      {
        id: product_unit.id
      }
    end
  end

  def generate_line_custom_detail_response(line_custom)
    order_types = OrderType.where(id: line_custom.order_type_ids).order(:name)
    {
      id: line_custom.id,
      order_types: order_types,
      recipe_line_custom_details: line_custom.recipe_line_custom_details.includes(:product, :product_unit).map do |detail|
        {
          id: detail.id,
          product: {
            id: detail.product.id,
            name: detail.product.name,
            sku: detail.product.sku
          },
          product_unit: {
            id: detail.product_unit.id,
            name: detail.product_unit.name
          },
          quantity: detail.quantity,
          storage_section: generate_line_custom_storage_section(detail.product.product_category)
        }
      end
    }
  end

  def generate_storage_section(mappable)
    storage_section = recipe_storage_section_lookup.call(mappable)

    {
      id: storage_section.id,
      name: storage_section.name
    }
  end

  def generate_line_custom_storage_section(mappable)
    storage_section = recipe_line_custom_storage_section_lookup.call(mappable)

    {
      id: storage_section.id,
      name: storage_section.name
    }
  end

  def recipe_storage_section_lookup
    @recipe_storage_section_lookup ||= Restaurant::Services::StorageSection::RecipeStorageSectionLookup.new(location, recipe_product_id)
  end

  def recipe_line_custom_storage_section_lookup
    @recipe_line_custom_storage_section_lookup ||= Restaurant::Services::StorageSection::RecipeStorageSectionLookup
                                                   .new(location, recipe_product_id,
                                                        product_category_ids: recipe_product_category_id,
                                                        fallback: recipe_line_custom_default_section)
  end

  def recipe_line_custom_default_section
    @recipe_line_custom_default_section ||= ::Report::Models::StorageSection.find_by(location_id: location.id, section_type: 'storage',
                                                                                     default_out: true) || StorageSection::Unassigned.instance
  end

  def recipe_product
    @recipe_product ||= recipe.product.presence || recipe.recipe_product_option_set.product
  end

  def recipe_product_id
    @recipe_product_id ||= recipe.product_id.presence || recipe.recipe_product_option_set.product_id
  end

  # used for recipe line custom
  def recipe_product_category_id
    @recipe_product_category_id ||= recipe&.product&.product_category_id
  end

  def option_set_recipe?
    recipe.recipe_product_option_set.present?
  end

  def recipe_product_name
    if option_set_recipe?
      option_names = recipe.recipe_product_option_set.option_set_options.map { |opt| opt.product.name }.join(', ')
      "#{recipe_product.name} #{option_names}"
    else
      recipe_product.name
    end
  end
end
