class Restaurant::Services::StorageSection::RecipeStorageSectionLookup
  def initialize(location, product_ids, product_category_ids: nil, fallback: ::StorageSection::DefaultSection.instance)
    @location = location
    @product_ids = Array(product_ids)
    @product_category_ids = Array(product_category_ids)
    @fallback = fallback
  end

  def call(mappable)
    return @fallback unless recipe_storage_section_exists?

    case mappable
    in nil
      @fallback
    in { yield_product_id: }
      mappable = recipe_by_product_id[yield_product_id.to_i]
      call(mappable)
    in { ingredient_product_id: }
      mappable = recipe_line_or_recipe_line_substitute_by_product_id[ingredient_product_id.to_i]
      call(mappable)
    in Recipe
      section_by_recipe.fetch(mappable.id, @fallback)
    in RecipeLine
      section_by_recipe_line.fetch(mappable.id, @fallback)
    in RecipeLineSubstitute
      section_by_recipe_line_substitute.fetch(mappable.id, @fallback)
    in ProductCategory
      section_by_product_category.fetch(mappable.id, @fallback)
    end
  end

  private

  def recipe_storage_section_exists?
    @recipe_storage_section_exists ||= StorageSectionMapping.within(@location).with_recipe_mappable.exists?
  end

  def recipe_by_product_id
    @recipe_by_product_id ||= recipes.index_by(&:product_id)
  end

  def recipe_line_or_recipe_line_substitute_by_product_id
    @recipe_line_or_recipe_line_substitute_by_product_id ||= begin
      hash = {}
      hash.merge! recipe_lines.index_by(&:product_id)
      hash.merge! recipe_line_substitutes.index_by(&:product_id)
      hash
    end
  end

  def section_by_recipe
    @section_by_recipe ||= in_mappings.where(mappable_type: 'Recipe', mappable_id: recipes.ids).reduce({}) do |hash, mapping|
      hash.merge(mapping.mappable_id => mapping.storage_section)
    end
  end

  def section_by_recipe_line
    @section_by_recipe_line ||= out_mappings.where(mappable_type: 'RecipeLine', mappable_id: recipe_lines.ids).reduce({}) do |hash, mapping|
      hash.merge(mapping.mappable_id => mapping.storage_section)
    end
  end

  def section_by_recipe_line_substitute
    @section_by_recipe_line_substitute ||= out_mappings.where(mappable_type: 'RecipeLineSubstitute',
                                                              mappable_id: recipe_line_substitutes.ids).reduce({}) do |hash, mapping|
      hash.merge(mapping.mappable_id => mapping.storage_section)
    end
  end

  def section_by_product_category
    @section_by_product_category ||= out_mappings.where(mappable_type: 'ProductCategory',
                                                        mappable_id: product_categories.ids).reduce({}) do |hash, mapping|
      hash.merge(mapping.mappable_id => mapping.storage_section)
    end
  end

  def in_mappings
    StorageSectionMapping.within(@location).in.includes(:storage_section)
  end

  def out_mappings
    StorageSectionMapping.within(@location).out.includes(:storage_section)
  end

  def recipes
    @recipes ||= Recipe.where(product_id: @product_ids).select(:id, :product_id)
  end

  def recipe_lines
    @recipe_lines ||= RecipeLine.where(recipe_id: recipes.ids).select(:id, :product_id)
  end

  def recipe_line_substitutes
    @recipe_line_substitutes ||= RecipeLineSubstitute.where(recipe_line_id: recipe_lines.ids).select(:id, :product_id)
  end

  def product_categories
    @product_categories ||= ProductCategory.where(id: @product_category_ids).select(:id)
  end
end
