class Restaurant::Services::Report::PaymentMethod::Clickhouse::SaleTransactionsQueryGroupByLocation
  attr_reader :brand_id, :location_ids, :start_date, :end_date, :taking_ids
  private :brand_id, :location_ids, :start_date, :end_date, :taking_ids

  def initialize(params)
    @brand_id = params[:brand_id]
    @location_ids = params[:location_ids]
    @start_date = params[:start_date]
    @end_date = params[:end_date]
    @taking_ids = params[:taking_ids]
  end

  # rubocop:disable Metrics/MethodLength
  def call!
    sales_subquery = ::Clickhouse::Models::SaleTransaction.with_deleted
                                                          .by_datetime_range(@start_date, @end_date)
                                                          .where(location_id: @location_ids)
                                                          .limit_by_to_sql(:id, 1)

    payment_subquery = ::Clickhouse::Models::Payment.with_deleted
                                                    .by_datetime_range(@start_date, @end_date)
                                                    .where(location_id: @location_ids)
                                                    .joins(Arel.sql("JOIN (#{sales_subquery}) AS public_sale_transactions
                                                                  ON public_payments.sale_transaction_id =
                                                                     public_sale_transactions.id
                                                                    AND public_payments.deleted = FALSE"))
                                                    .select("public_sale_transactions.deleted as deleted,
                                                                   public_sale_transactions.status as status,
                                                                   public_sale_transactions.taking_id as taking_id,
                                                                   payment_method_name,
                                                                   payment_method_id,
                                                                   location_name,
                                                                   location_id,
                                                                   local_sales_date,
                                                                   public_sale_transactions.id AS public_sale_transactions_id,
                                                                   public_payments.id AS public_payments_id,
                                                                   COALESCE(public_payments.change,0) AS change,
                                                                   COALESCE(public_payments.processing_fee,0) AS processing_fee,
                                                                   COALESCE(public_payments.amount_receive, 0)
                                                                    - COALESCE(public_payments.change, 0) AS amount_receive,
                                                                   COALESCE(public_payments.subsidize_amount, 0)
                                                                    - COALESCE(public_payments.processing_fee, 0) AS subsidize_amount,
                                                                   (COALESCE(public_payments.amount_receive, 0)
                                                                    + COALESCE(public_payments.subsidize_amount, 0)
                                                                    - COALESCE(public_payments.processing_fee, 0)
                                                                    - COALESCE(public_payments.change, 0)) AS net_received")
                                                    .limit_by_to_sql(:id, 1)

    locations_subquery = ::Clickhouse::Models::Location.with_deleted
                                                       .where(id: @location_ids)
                                                       .limit_by_to_sql(:id, 1)

    query = ::Clickhouse::Models::SaleTransaction.from("(#{payment_subquery}) AS public_sale_transactions")
                                                 .joins(Arel.sql("JOIN (#{locations_subquery}) AS public_locations
                                                                  ON public_locations.id =
                                                                     public_sale_transactions.location_id
                                                                  AND public_locations.deleted = FALSE"))
                                                 .where('status = 0')

    query = query.where("public_sale_transactions.taking_id IN (#{@taking_ids.join(',')})") if @taking_ids.present?

    query.group(Arel.sql('payment_method_name, payment_method_id, public_locations.name, public_locations.id'))
         .order(Arel.sql('public_locations.name, payment_method_name'))
         .select('public_locations.name AS location_name',
                 'public_locations.id AS location_id',
                 'payment_method_id',
                 'payment_method_name',
                 'COUNT(public_payments_id) AS count_payment',
                 'COUNT(DISTINCT public_sale_transactions_id) AS no_of_sales',
                 '0 AS no_of_refunds',
                 'SUM(amount_receive) AS amount_received',
                 'SUM(subsidize_amount) AS subsidize_amount',
                 'SUM(net_received) AS net_received')
         .as_json
  end
  # rubocop:enable Metrics/MethodLength
end
