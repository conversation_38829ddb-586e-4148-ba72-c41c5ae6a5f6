# rubocop:disable Metrics/ClassLength
class Restaurant::Services::Report::SalesBy::Clickhouse::EstimateCostQuerySalesByProducts
  include Report::Modules::Checkpointable
  attr_reader :brand_id,
              :brand,
              :location_ids,
              :start_date,
              :end_date,
              :group_by,
              :is_select_all_product,
              :product_ids,
              :exclude_product_ids,
              :order_type_ids,
              :taking_ids,
              :is_export,
              :all_bulk_estimate_costs,
              :all_incomplete_estimate_product_ids,
              :use_estimate_cost,
              :all_grouped_follow_parent_product_ids,
              :location_indexed_bulk_estimate_costs
  private :brand_id,
          :brand,
          :location_ids,
          :start_date,
          :end_date,
          :group_by,
          :is_select_all_product,
          :product_ids,
          :exclude_product_ids,
          :order_type_ids,
          :taking_ids,
          :is_export,
          :all_bulk_estimate_costs,
          :all_incomplete_estimate_product_ids,
          :use_estimate_cost,
          :location_indexed_bulk_estimate_costs

  PRODUCT = 'product'.freeze
  CATEGORY_GROUP = 'category_group'.freeze

  def initialize(params:)
    @brand_id = params[:brand_id]
    @brand = ::Report::Models::Brand.find(@brand_id)
    @location_ids = params[:location_ids]
    @start_date = params[:start_date]
    @end_date = params[:end_date]
    @group_by = params[:group_by]
    @is_select_all_product = params[:is_select_all_product]
    @product_ids = params[:product_ids]
    @exclude_product_ids = params[:exclude_product_ids]
    @order_type_ids = params[:order_type_ids]
    @taking_ids = params[:taking_ids]
    @is_export = params[:is_export]
    @all_bulk_estimate_costs = []
    @all_incomplete_estimate_product_ids = []
    @all_grouped_follow_parent_product_ids = []
    @location_indexed_bulk_estimate_costs = []
    @use_estimate_cost = @brand.use_estimate_cost
    @include_unsold_product = params[:include_unsold_product]
  end

  def self.location_partition_count
    5
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def call!
    accumulated_data = if @include_unsold_product
                         generate_blank_accumulated_data
                       else
                         {}
                       end

    accumulated_data_per_location = {}
    batch_grouped_follow_parent_product_ids = []

    # if use_estimate_cost
    #   # This is to insert estimate costs.
    #   bulk_estimate_costs, incomplete_estimate_product_ids, grouped_follow_parent_product_ids = bulk_estimate_costs_by_product(
    #     selected_location_ids
    #   )

    #   all_bulk_estimate_costs << bulk_estimate_costs
    #   all_incomplete_estimate_product_ids << incomplete_estimate_product_ids
    #   batch_grouped_follow_parent_product_ids << grouped_follow_parent_product_ids
    # end

    selected_location_ids = [@location_ids[0]]
    sale_details = with_checkpoint("sale_details_#{selected_location_ids.join(',')}", collection: false) do
      query_sale_detail_transactions(selected_location_ids)
    end

    modifiers = with_checkpoint("modifiers_#{selected_location_ids.join(',')}", collection: false) do
      query_sale_detail_modifiers(selected_location_ids)
    end

    return_lines = with_checkpoint("return_lines_#{selected_location_ids.join(',')}", collection: false) do
      query_sales_return_lines(selected_location_ids)
    end

    modifier_return_lines = with_checkpoint("modifier_return_lines_#{selected_location_ids.join(',')}", collection: false) do
      query_sales_return_lines_for_modifiers(selected_location_ids)
    end

    accumulated_data = update_accumulated_data(accumulated_data, sale_details)
    accumulated_data = update_accumulated_data(accumulated_data, modifiers, is_from_modifier: true)
    accumulated_data = update_accumulated_data(accumulated_data, return_lines, ignore_should_include_cost: true)
    accumulated_data = update_accumulated_data(accumulated_data, modifier_return_lines, is_from_modifier: true, ignore_should_include_cost: true)

    if @group_by != PRODUCT
      # NOTE: When group by is not by product, we need to exclude modifier follow parent from the result for group by category/category group.
      # Key that contain suffix with any integer is from data that follow parent cost.
      # The suffix integer come from modifier_follow_cost_sale_detail_product_id.
      # But key that contain suffix "_0" is from data without modifier follow parent cost.
      accumulated_data.keys.grep_v(/_0\z/).each do |key_not_needed_for_final_result|
        accumulated_data.delete key_not_needed_for_final_result
      end
    end

    if use_estimate_cost
      sale_details_per_location_agg = query_sale_detail_transactions_per_location(selected_location_ids)

      accumulated_data_per_location = update_accumulated_data_per_location(accumulated_data_per_location, sale_details_per_location_agg)

      sale_modifiers_per_location_agg = query_sale_detail_modifiers_per_location(selected_location_ids)
      accumulated_data_per_location = update_accumulated_data_per_location(accumulated_data_per_location, sale_modifiers_per_location_agg)

      sale_return_lines_per_location_agg = query_sales_return_lines_per_location(selected_location_ids)
      accumulated_data_per_location = update_accumulated_data_per_location(accumulated_data_per_location, sale_return_lines_per_location_agg)

      sale_modifier_lines_per_location_agg = query_sales_return_lines_for_modifiers_per_location(selected_location_ids)
      accumulated_data_per_location = update_accumulated_data_per_location(accumulated_data_per_location, sale_modifier_lines_per_location_agg)
    end

    if use_estimate_cost
      @all_grouped_follow_parent_product_ids = batch_grouped_follow_parent_product_ids.flatten.group_by do |batch_grouped_follow_parent_product_id|
        "modcost-#{batch_grouped_follow_parent_product_id[:location_id]}-#{batch_grouped_follow_parent_product_id[:detail_product_id]}"
      end

      @location_indexed_bulk_estimate_costs = all_bulk_estimate_costs.flatten.index_by do |all_bulk_estimate_cost|
        "estimate-cost-#{all_bulk_estimate_cost.location_id}-#{all_bulk_estimate_cost.product_id}"
      end

      apply_estimate_costing(accumulated_data, all_bulk_estimate_costs, all_incomplete_estimate_product_ids, accumulated_data_per_location)
    else
      accumulated_data.values.compact
    end
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  # rubocop:disable Metrics/AbcSize
  def generate_blank_accumulated_data
    accumulated_data = {}
    products = ::Product.includes(:product_category).where(brand_id: @brand_id, sell_to_customer_type: true, modifier: false, status: 0)
    products = products.where(id: @product_ids) if @product_ids.present?

    products = products.where.not(id: @exclude_product_ids) if @exclude_product_ids.present?

    products.find_each do |product|
      accumulated_data[lookup_key_product(product)] ||= {}
      accumulated_data[lookup_key_product(product)]['is_from_modifier'] = false
      accumulated_data[lookup_key_product(product)]['id'] = product.id
      accumulated_data[lookup_key_product(product)]['sku'] = product.sku
      accumulated_data[lookup_key_product(product)]['name'] = product.name
      accumulated_data[lookup_key_product(product)]['category'] = product.product_category&.name
      accumulated_data[lookup_key_product(product)]['modifier_follow_cost_sale_detail_product_id'] = 0
      accumulated_data[lookup_key_product(product)]['qty'] = 0
      accumulated_data[lookup_key_product(product)]['sales_amount'] = 0
      accumulated_data[lookup_key_product(product)]['gross_sales_amount'] = 0
      accumulated_data[lookup_key_product(product)]['cost'] = 0
      accumulated_data[lookup_key_product(product)]['discount'] = 0
      accumulated_data[lookup_key_product(product)]['surcharge'] = 0
      accumulated_data[lookup_key_product(product)]['refund_qty'] = 0
      accumulated_data[lookup_key_product(product)]['refund_amount'] = 0
      accumulated_data[lookup_key_product(product)]['gross_profit_percentage'] = 0
      accumulated_data[lookup_key_product(product)]['net_amount'] = 0
      accumulated_data[lookup_key_product(product)]['sale_percentage'] = 0
      accumulated_data[lookup_key_product(product)]['qty_percentage'] = 0
      accumulated_data[lookup_key_product(product)]['should_include_cost'] = true
    end

    accumulated_data
  end
  # rubocop:enable Metrics/AbcSize

  def apply_estimate_costing(accumulated_data, all_bulk_estimate_costs, all_incomplete_estimate_product_ids, accumulated_data_per_location)
    indexed_bulk_estimate_costs = all_bulk_estimate_costs.flatten.group_by(&:product_id)
    all_incomplete_estimate_product_ids.flatten!.uniq!

    # Sales by product data with real cost values
    sales_by_with_real_costs = accumulated_data.values.compact

    if @group_by == PRODUCT
      # Add estimate cost to sales by data, product ids without complete estimate costs will be ignored, still N/A.
      add_estimate_costs_group_by_product(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                          accumulated_data_per_location)
    elsif @group_by == CATEGORY_GROUP
      add_estimate_costs_group_by_category_group(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                                 accumulated_data_per_location)
    else
      # Add estimate cost to sales by data, product ids without complete estimate costs will be ignored, still N/A.
      add_estimate_costs_group_by_category(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                           accumulated_data_per_location)
    end
  end

  def calculate_estimate(accumulated_data_per_location, per_product_data, found_estimates, group_by_product: false)
    # NOTE: If has follow parent modifiers, the parent can just use child total estimations that already count each qty used.
    # If not, each product must calculate the price * qty used in transactions.
    found_estimates.reduce(0) do |total, estimate|
      # NOTE: Only when group_by=product the value is_from_modifier is accurate,
      # the rest is not accurate since the other filter is grouped by category/category group
      qty = if group_by_product
              qty_key = per_product_data['is_from_modifier'] ? 'qty_with_conversion' : 'qty_with_conversion_not_included_in_parent'
              accumulated_data_per_location.dig("loc-#{estimate.location_id}-#{estimate.product_id}", qty_key).to_d
            else
              accumulated_data_per_location.dig("loc-#{estimate.location_id}-#{estimate.product_id}",
                                                'qty_with_conversion_not_included_in_parent').to_d
            end

      total + (estimate.price.to_d * qty.to_d) + estimate.child_estimate_cost.to_d
    end
  end

  def add_estimate_costs_group_by_category_group(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                                 accumulated_data_per_location)
    indexed_product_and_category_groups = indexed_product_and_category_groups(indexed_bulk_estimate_costs)

    sales_by_with_real_costs.each do |per_product_data|
      product_category_group_id = per_product_data['id']
      category_group_products = indexed_product_and_category_groups[product_category_group_id]
      next if category_group_products.blank?

      products_ids = category_group_products.map(&:product_id)
      all_products_estimates = 0
      any_incomplete_estimate_cost = false
      products_ids.each do |product_id|
        complete_estimate_cost = !(product_id.in? all_incomplete_estimate_product_ids)

        unless complete_estimate_cost
          any_incomplete_estimate_cost = true

          next
        end

        found_estimates = indexed_bulk_estimate_costs[product_id]
        next if found_estimates.blank?

        total_estimates = calculate_estimate(accumulated_data_per_location, per_product_data, found_estimates, group_by_product: false)

        all_products_estimates += total_estimates
      end

      with_estimate_cost = per_product_data['cost'].to_d + all_products_estimates
      per_product_data['cost'] = with_estimate_cost
      per_product_data['should_include_cost'] = !any_incomplete_estimate_cost
    end
  end

  def add_estimate_costs_group_by_category(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                           accumulated_data_per_location)
    indexed_product_and_categories = indexed_product_and_categories(indexed_bulk_estimate_costs)
    sales_by_with_real_costs.each do |per_product_data|
      product_category_id = per_product_data['id']
      product_category_products = indexed_product_and_categories[product_category_id]
      next if product_category_products.blank?

      products_ids = product_category_products.map(&:product_id)
      all_products_estimates = 0
      any_incomplete_estimate_cost = false
      products_ids.each do |product_id|
        complete_estimate_cost = !(product_id.in? all_incomplete_estimate_product_ids)

        unless complete_estimate_cost
          any_incomplete_estimate_cost = true

          next
        end

        found_estimates = indexed_bulk_estimate_costs[product_id]
        next if found_estimates.blank?

        total_estimates = calculate_estimate(accumulated_data_per_location, per_product_data, found_estimates, group_by_product: false)

        all_products_estimates += total_estimates
      end

      with_estimate_cost = per_product_data['cost'].to_d + all_products_estimates
      per_product_data['cost'] = with_estimate_cost
      per_product_data['should_include_cost'] = !any_incomplete_estimate_cost
    end
  end

  def add_estimate_costs_group_by_product(sales_by_with_real_costs, indexed_bulk_estimate_costs, all_incomplete_estimate_product_ids,
                                          accumulated_data_per_location)
    sales_by_with_real_costs.each do |per_product_data|
      product_id = per_product_data['id']
      complete_estimate_cost = !(product_id.in? all_incomplete_estimate_product_ids)

      next unless complete_estimate_cost

      found_estimates = indexed_bulk_estimate_costs[product_id]
      next if found_estimates.blank?

      modifier_cost_follow_parent = per_product_data['is_from_modifier'] && per_product_data['modifier_follow_cost_sale_detail_product_id'] != 0

      total_estimates = if modifier_cost_follow_parent
                          0
                        else
                          calculate_estimate(accumulated_data_per_location, per_product_data, found_estimates)
                        end
      with_estimate_cost = per_product_data['cost'].to_d + total_estimates
      per_product_data['cost'] = with_estimate_cost
      per_product_data['should_include_cost'] = true
    end
  end

  def indexed_product_and_categories(indexed_bulk_estimate_costs)
    product_ids = indexed_bulk_estimate_costs.keys

    @indexed_product_and_categories.presence || @indexed_product_and_categories = brand
                                                                                  .products.left_outer_joins(:product_category)
                                                                                  .where(id: product_ids)
                                                                                  .select('product_categories.id AS product_category_id,
                                                                                          products.id AS product_id')
                                                                                  .group_by(&:product_category_id)
  end

  def indexed_product_and_category_groups(indexed_bulk_estimate_costs)
    product_ids = indexed_bulk_estimate_costs.keys

    @indexed_product_and_category_groups.presence || @indexed_product_and_category_groups = brand
                                                                                            .products.left_outer_joins(
                                                                                              product_category: :product_category_group
                                                                                            )
                                                                                            .where(id: product_ids)
                                                                                            .select('product_category_groups.id AS
                                                                                                     product_category_group_id,
                                                                                                     products.id AS product_id')
                                                                                            .group_by(&:product_category_group_id)
  end

  def last_minimum_costing_date(selected_location_ids)
    franchise_locations = Location.where(id: selected_location_ids).where(is_franchise: true)

    franchise_last_costing = ::Costing.done.where(location_id: franchise_locations.pluck(:id)).minimum(:end_period) if franchise_locations.present?

    if franchise_locations.count != selected_location_ids.count
      company_last_costing = ::Costing.done.where(brand_id: brand_id, location_id: nil).minimum(:end_period)
    end

    [franchise_last_costing, company_last_costing].compact.min
  end

  # From the sale detail and sale modifier, gather products that have no real costs.
  # Then search the estimate costs for them.
  # Also gather products that have incomplete estimate cost.
  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/BlockLength
  def bulk_estimate_costs_by_product(selected_location_ids)
    minimum_costing_date = last_minimum_costing_date(selected_location_ids)

    no_cost_agg_details = ::Report::Models::SaleDetailTransaction
                          .where(sale_details_condition(selected_location_ids, minimum_costing_date: minimum_costing_date).to_s)
                          .group('location_id, product_id')
                          .select(
                            <<~SQL
                              1 as COUNT, location_id, product_id
                            SQL
                          ).to_a

    no_cost_agg_modifiers = ::Report::Models::SaleDetailModifier
                            .joins(:sale_detail_transaction)
                            .where(modifiers_condition(selected_location_ids, minimum_costing_date: minimum_costing_date).to_s)
                            .group('sale_detail_modifiers.location_id,
                                    sale_detail_transactions.product_id, sale_detail_modifiers.product_id,
                                    sale_detail_modifiers.rule_cost_included_in_parent,
                                    sale_detail_modifiers.option_set_quantity,
                                    sale_detail_modifiers.quantity')
                            .select(
                              <<~SQL
                                1 as COUNT, sale_detail_modifiers.location_id, sale_detail_modifiers.product_id,
                                sale_detail_modifiers.rule_cost_included_in_parent, JSONB_AGG(sale_detail_modifiers.sale_product_ids) AS bulk_sale_product_ids,
                                option_set_quantity,
                                sale_detail_transactions.product_id as parent_product_id,
                                COUNT(DISTINCT sale_detail_modifiers.id) as sale_detail_modifiers_total_per_location,
                                sale_detail_modifiers.quantity as sale_detail_modifier_quantity
                              SQL
                            ).to_a

    follow_parent_no_cost_agg_modifiers = no_cost_agg_modifiers.filter(&:rule_cost_included_in_parent)
    follow_parent_product_ids = follow_parent_no_cost_agg_modifiers
                                .map do |modifier|
                                  modifier.bulk_sale_product_ids.map do |sale_product_ids|
                                    detail_product_id = sale_product_ids - [modifier.product_id]
                                    { modifier_product_id: modifier.product_id, detail_product_id: detail_product_id.first,
                                      location_id: modifier.location_id,
                                      count: modifier.count,
                                      option_set_quantity: modifier.option_set_quantity }
                                  end
                                end.flatten

    no_cost_aggs = [no_cost_agg_details, no_cost_agg_modifiers].flatten
    indexed_locations = brand.locations.where(id: no_cost_aggs.map(&:location_id).uniq).index_by(&:id)
    indexed_products = brand.products.includes(:recipe).where(id: no_cost_aggs.map(&:product_id).uniq).index_by(&:id)
    location_grouped_no_cost_aggs = no_cost_aggs.group_by(&:location_id)
    all_estimate_costs = []
    date = Time.now.in_time_zone(brand.timezone).to_date

    location_grouped_no_cost_aggs.each do |location_id, each_no_cost_aggs|
      product_ids = each_no_cost_aggs.map(&:product_id)
      products = product_ids.map { |product_id| indexed_products[product_id] }

      estimate_costs = Restaurant::Services::Product::BulkEstimateCostOneLocationService
                       .new(date: date, location: indexed_locations[location_id], products: products)
                       .call

      estimate_costs.map do |product_id, price|
        next if price.blank?

        all_estimate_costs << Restaurant::Poro::Product::EstimateCost.new(
          price: price,
          product_id: product_id,
          location_id: location_id,
          date: date
        )
      end

      follow_parent_product_ids.group_by { |fpp| fpp[:detail_product_id] }.each do |parent_product_id, modifiers|
        parent_product_cost = all_estimate_costs.detect do |est_cost|
          est_cost.product_id == parent_product_id && est_cost.location_id == location_id && est_cost.date == date
        end

        modifiers_ids = modifiers.map { |modifier| modifier[:modifier_product_id] }

        sale_detail_modifiers = each_no_cost_aggs.select do |sdm|
          sdm.try(:rule_cost_included_in_parent) && modifiers_ids.include?(sdm.product_id) && sdm.parent_product_id == parent_product_id
        end

        if parent_product_cost.present?
          no_cost_aggs = no_cost_aggs.reject { |sdm| sdm.product_id == parent_product_id && sdm.location_id == location_id }
        end

        next if sale_detail_modifiers.blank?

        modifiers_total = sale_detail_modifiers.map do |modifier|
          found_cost = all_estimate_costs.detect do |est_cost|
            est_cost.product_id == modifier.product_id && est_cost.location_id == location_id && est_cost.date == date
          end.try(:price)

          next 0 if found_cost.nil?

          total_per_location = modifier.count * modifier.sale_detail_modifiers_total_per_location
          found_cost.to_d * (total_per_location * modifier.sale_detail_modifier_quantity * modifier.option_set_quantity)
        end.sum

        if parent_product_cost.present?
          parent_product_cost.child_estimate_cost = modifiers_total
          next
        end

        all_estimate_costs << Restaurant::Poro::Product::EstimateCost.new(
          price: 0,
          product_id: parent_product_id,
          location_id: location_id,
          date: date,
          child_estimate_cost: modifiers_total
        )
      end
    end

    indexed_estimate_costs = all_estimate_costs.index_by do |estimate_cost|
      "#{estimate_cost.product_id}-#{estimate_cost.location_id}"
    end

    # Gather products with incomplete (not available in all selected_location_ids) estimate cost.
    incomplete_estimate_product_ids = []
    no_cost_aggs.select do |no_cost_agg|
      key = "#{no_cost_agg.product_id}-#{no_cost_agg.location_id}"
      incomplete_estimate_product_ids << no_cost_agg.product_id if indexed_estimate_costs[key].blank?
    end

    indexed_no_cost_aggs = no_cost_aggs.index_by(&:product_id)

    # Multiply estimate cost for each locations (location count from all_estimate_costs) for each product (aggregate sales by).
    all_estimate_costs.each do |estimate_cost|
      found_no_cost_agg = indexed_no_cost_aggs[estimate_cost.product_id]

      count = found_no_cost_agg.present? ? found_no_cost_agg.count : 1
      estimate_cost.price = estimate_cost.price * count
    end

    [all_estimate_costs, incomplete_estimate_product_ids, follow_parent_product_ids]
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/BlockLength

  def update_accumulated_data_per_location(accumulated_data_per_location, lines)
    lines.each do |location_data_id, line|
      accumulated_data_per_location[location_data_id] ||= {}
      # NOTE: When modifier_follow_cost_sale_detail_product_id is zero, then this line is from sale detail/modifier that follow parent
      if line['modifier_follow_cost_sale_detail_product_id'] != 0
        accumulated_data_per_location[location_data_id]['qty_with_conversion'] =
          accumulated_data_per_location[location_data_id]['qty_with_conversion'].to_d + line['qty_with_conversion'].to_d
      else
        accumulated_data_per_location[location_data_id]['qty_with_conversion_not_included_in_parent'] =
          accumulated_data_per_location[location_data_id]['qty_with_conversion_not_included_in_parent'].to_d + line['qty_with_conversion'].to_d
      end
    end

    accumulated_data_per_location
  end

  # rubocop:disable Metrics/BlockLength, Metrics/MethodLength, Metrics/AbcSize
  def update_accumulated_data(accumulated_data, sale_details, is_from_modifier: false, ignore_should_include_cost: false)
    sale_details.each do |product_id, product_hash|
      accumulated_data[product_id] ||= {}
      accumulated_data[product_id]['is_from_modifier'] = is_from_modifier
      accumulated_data[product_id]['id'] = product_hash['id']
      accumulated_data[product_id]['sku'] = product_hash['sku']
      accumulated_data[product_id]['name'] = product_hash['name']
      accumulated_data[product_id]['category'] = product_hash['category']
      accumulated_data[product_id]['modifier_follow_cost_sale_detail_product_id'] =
        product_hash['modifier_follow_cost_sale_detail_product_id']
      accumulated_data[product_id]['qty'] = accumulated_data[product_id]['qty'].to_d + product_hash['qty'].to_d
      accumulated_data[product_id]['sales_amount'] = accumulated_data[product_id]['sales_amount'].to_d +
                                                     product_hash['sales_amount'].to_d
      accumulated_data[product_id]['gross_sales_amount'] = accumulated_data[product_id]['gross_sales_amount'].to_d +
                                                           product_hash['gross_sales_amount'].to_d
      accumulated_data[product_id]['cost'] = accumulated_data[product_id]['cost'].to_d + product_hash['cost'].to_d
      accumulated_data[product_id]['discount'] = accumulated_data[product_id]['discount'].to_d + product_hash['discount'].to_d
      accumulated_data[product_id]['surcharge'] = accumulated_data[product_id]['surcharge'].to_d + product_hash['surcharge'].to_d
      accumulated_data[product_id]['refund_qty'] = accumulated_data[product_id]['refund_qty'].to_d +
                                                   product_hash['refund_qty'].to_d
      accumulated_data[product_id]['refund_amount'] = accumulated_data[product_id]['refund_amount'].to_d +
                                                      product_hash['refund_amount'].to_d
      accumulated_data[product_id]['gross_profit_percentage'] = 0
      accumulated_data[product_id]['net_amount'] = 0
      accumulated_data[product_id]['sale_percentage'] = 0
      accumulated_data[product_id]['qty_percentage'] = 0
      unless ignore_should_include_cost
        if product_hash['should_include_cost'].present?
          accumulated_data[product_id]['should_include_cost'] ||= true
          accumulated_data[product_id]['should_include_cost'] = accumulated_data[product_id]['should_include_cost'] &&
                                                                product_hash['should_include_cost']
        else
          accumulated_data[product_id]['should_include_cost'] = false
        end
      end
    end

    accumulated_data
  end
  # rubocop:enable Metrics/BlockLength, Metrics/MethodLength, Metrics/AbcSize

  # rubocop:disable Metrics/MethodLength
  def group_by_sale_details(per_location = false)
    if @group_by == PRODUCT || per_location
      "GROUPING SETS (
      (sale_detail_transactions.location_id,
       product_id,
       products.sku,
       products.name,
       product_categories.name,
       modifier_follow_cost_sale_detail_product_id
      ),
      (product_id,
       products.sku,
       products.name,
       product_categories.name,
       modifier_follow_cost_sale_detail_product_id)
      )"
    elsif @group_by == CATEGORY_GROUP
      'GROUPING SETS(
        (sale_detail_transactions.location_id,
         product_id,
         products.sku,
         products.name,
         modifier_follow_cost_sale_detail_product_id),
        (product_category_groups.id, product_category_groups.name, modifier_follow_cost_sale_detail_product_id)
       )
      '
    else
      '
        GROUPING SETS(
          (product_categories.id, product_categories.name, modifier_follow_cost_sale_detail_product_id),
          (sale_detail_transactions.location_id,
           product_id,
           products.sku,
           products.name,
           modifier_follow_cost_sale_detail_product_id)
        )
      '
    end
  end
  # rubocop:enable Metrics/MethodLength

  def select_sale_detail_modifiers_based_on_filter_group_by(per_location = false)
    if @group_by == PRODUCT || per_location
      "CASE WHEN sale_detail_modifiers.rule_cost_included_in_parent IS TRUE THEN
          sale_detail_transactions.product_id
        ELSE
          0
        END AS modifier_follow_cost_sale_detail_product_id,
        sale_detail_modifiers.product_id AS id,
        products.sku,
        products.name,
        product_categories.name AS category"
    elsif @group_by == CATEGORY_GROUP
      'CASE WHEN sale_detail_modifiers.rule_cost_included_in_parent IS TRUE THEN
          sale_detail_transactions.product_id
        ELSE
          0
        END AS modifier_follow_cost_sale_detail_product_id,
       product_category_groups.id, product_category_groups.name'
    else
      'CASE WHEN sale_detail_modifiers.rule_cost_included_in_parent IS TRUE THEN
          sale_detail_transactions.product_id
        ELSE
          0
        END AS modifier_follow_cost_sale_detail_product_id,
       product_categories.id, product_categories.name'
    end
  end

  def group_by_modifiers(per_location = false)
    if @group_by == PRODUCT || per_location
      "sale_detail_modifiers.location_id,
       sale_detail_modifiers.product_id,
       products.sku,
       products.name,
       product_categories.name,
       modifier_follow_cost_sale_detail_product_id"
    elsif @group_by == CATEGORY_GROUP
      'sale_detail_modifiers.location_id, product_category_groups.id, product_category_groups.name, modifier_follow_cost_sale_detail_product_id'
    else
      'sale_detail_modifiers.location_id, product_categories.id, product_categories.name, modifier_follow_cost_sale_detail_product_id'
    end
  end

  def grouping_sets_by_modifiers
    if @group_by == PRODUCT
      "GROUPING SETS ((
        inner_sale_detail_modifiers.location_id,
        inner_sale_detail_modifiers.id, -- actually product id
        inner_sale_detail_modifiers.sku,
        inner_sale_detail_modifiers.name, -- actually product name
        inner_sale_detail_modifiers.category,
        modifier_follow_cost_sale_detail_product_id),
       (
         inner_sale_detail_modifiers.id, -- actually product id
         inner_sale_detail_modifiers.sku,
         inner_sale_detail_modifiers.name, -- actually product name
         inner_sale_detail_modifiers.category,
         modifier_follow_cost_sale_detail_product_id))"
    elsif @group_by == CATEGORY_GROUP
      'GROUPING SETS ((
        inner_sale_detail_modifiers.location_id, inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name, modifier_follow_cost_sale_detail_product_id),
      (inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name, modifier_follow_cost_sale_detail_product_id))'
    else
      'GROUPING SETS
      ((inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name, modifier_follow_cost_sale_detail_product_id),
      (
        inner_sale_detail_modifiers.location_id, inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name, modifier_follow_cost_sale_detail_product_id))'
    end
  end

  def lookup_key(result)
    "#{result['id']}_#{result['name']}_#{result['modifier_follow_cost_sale_detail_product_id']}"
  end

  def per_location_lookup_key(result)
    "loc-#{result['location_id']}-#{result['id']}"
  end

  def modifier_per_location_lookup_key(result)
    "loc-#{result['location_id']}-#{result['id']}"
  end

  def lookup_key_product(product)
    "#{product.id}_#{product.name}_0"
  end

  def tax_of_modifiers
    "CASE WHEN sale_detail_modifiers.tax_setting = 'price_include_tax'
     THEN
        (COALESCE(sale_detail_modifiers.tax_rate, 0) + 100) / 100
     ELSE
       1
     END"
  end

  def modifiers_gross_sales_amount
    "COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_total_line_amount', '0')::decimal / #{tax_of_modifiers}"
  end

  def modifiers_discount
    "(
        (COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_prorate_discount', '0')::decimal / #{tax_of_modifiers}) +
        COALESCE(sale_detail_modifiers.meta ->> 'sales_by_free_of_charge', '0')::decimal
     )"
  end

  def modifiers_surcharge
    "COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_prorate_surcharge', '0')::decimal / #{tax_of_modifiers}"
  end

  def sale_details_condition(selected_location_ids, minimum_costing_date: nil)
    additional_conditions = ''
    if is_select_all_product && exclude_product_ids.present?
      additional_conditions = "#{additional_conditions} AND (NOT sale_detail_transactions.sale_product_ids &&
                               ARRAY[#{exclude_product_ids.join(',')}]::integer[])"
    elsif product_ids.present?
      additional_conditions = "#{additional_conditions} AND (sale_detail_transactions.sale_product_ids &&
                               ARRAY[#{product_ids.join(',')}]::integer[])"
    end

    if order_type_ids.present? && group_by == PRODUCT
      additional_conditions = "#{additional_conditions} and sale_detail_transactions.order_type_id IN (#{order_type_ids.join(',')})"
    end

    additional_conditions = "#{additional_conditions} and sale_detail_transactions.taking_id IN (#{taking_ids.join(',')})" if taking_ids.present?

    ActiveRecord::Base.sanitize_sql(
      "sale_detail_transactions.quantity > 0 AND
      sale_detail_transactions.status = 0 AND
      sale_detail_transactions.brand_id = #{brand_id} AND
      sale_detail_transactions.location_id IN (#{selected_location_ids.join(',')}) AND
      sale_detail_transactions.local_sales_time
        BETWEEN '#{minimum_costing_date || start_date}' AND '#{end_date}'
      #{additional_conditions}"
    )
  end

  def sale_details_joins
    "JOIN products ON products.id = sale_detail_transactions.product_id
     LEFT JOIN product_categories ON product_categories.id = products.product_category_id
     LEFT JOIN product_category_groups ON product_category_groups.id = product_categories.product_category_group_id"
  end

  def modifiers_condition(selected_location_ids, minimum_costing_date: nil)
    additional_conditions = ''
    if is_select_all_product && exclude_product_ids.present?
      additional_conditions = "#{additional_conditions} AND (NOT sale_detail_modifiers.sale_product_ids &&
                               ARRAY[#{exclude_product_ids.join(',')}]::integer[])"
    elsif product_ids.present?
      additional_conditions = "#{additional_conditions} AND (sale_detail_modifiers.sale_product_ids &&
                               ARRAY[#{product_ids.join(',')}]::integer[])"
    end

    if order_type_ids.present? && group_by == PRODUCT
      additional_conditions = "#{additional_conditions} and sale_detail_transactions.order_type_id IN (#{order_type_ids.join(',')})"
    end

    additional_conditions = "#{additional_conditions} and sale_detail_transactions.taking_id IN (#{taking_ids.join(',')})" if taking_ids.present?

    ActiveRecord::Base.sanitize_sql(
      "sale_detail_modifiers.product_id IS NOT NULL AND
      sale_detail_transactions.quantity > 0 AND
      sale_detail_modifiers.status = 0 AND
      sale_detail_modifiers.brand_id = #{brand_id} AND
      sale_detail_modifiers.location_id IN (#{selected_location_ids.join(',')}) AND
      sale_detail_modifiers.local_sales_time BETWEEN '#{minimum_costing_date || start_date}' AND '#{end_date}'
      #{additional_conditions}"
    )
  end

  def sale_details_tax
    "CASE WHEN public_sale_detail_transactions.tax_setting = 'price_include_tax'
     THEN
       (COALESCE(public_sale_detail_transactions.tax_rate, 0) + 100) / 100
     ELSE
       1
     END"
  end

  def return_qty_from_sale_detail
    '(COALESCE(sales_return_lines.return_quantity, 0) * -1 / sale_detail_transactions.quantity)'
  end

  def modifiers_return_quantity
    "(COALESCE(sales_return_lines.return_quantity, 0) * -1) / sale_detail_transactions.quantity *
            sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity"
  end

  def modifiers_prorate_return_quantity
    "#{modifiers_return_quantity} / sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity"
  end

  def sale_detail_select
    <<~SQL
      0 AS modifier_follow_cost_sale_detail_product_id,
      any(public_sale_detail_transactions.product_sku),
      any(public_sale_detail_transactions.product_name),
      any(public_sale_detail_transactions.product_category_name) AS category,
      any(public_sale_detail_transactions.location_id),
      any(public_sale_detail_transactions.product_id),
      SUM(quantity) AS qty,
      SUM(quantity * sale_detail_transactions.product_unit_conversion_qty) AS qty_with_conversion,
      SUM(
        COALESCE(meta ->> 'parent_rule_total_line_discount_prorate', '0')::decimal /
        #{sale_details_tax}
      ) AS sales_amount,
      SUM(
        coalesce(meta ->> 'parent_rule_total_line_amount', '0')::decimal /
        #{sale_details_tax}
      ) AS gross_sales_amount,
      SUM(COALESCE(sale_detail_transactions.meta ->> 'cost', '0')::decimal) AS cost,
      bool_and(COALESCE((sale_detail_transactions.meta ->> 'cost')::decimal >= 0, false)) AS should_include_cost,
      SUM(
        (
          (
            COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_discount', '0')::decimal /
            #{sale_details_tax}
          ) + COALESCE(sale_detail_transactions.meta ->> 'sales_by_free_of_charge', '0')::decimal
        )
      ) AS discount,
      SUM(COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_surcharge', '0')::decimal /
          #{sale_details_tax}) AS surcharge
    SQL
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  # NOTE: Ini juga harus perbaiki subquerynya kalau mw pake limit_by_to_sql, tapi changes terbaru nya ada di PR https://bitbucket.org/runchise/runchise/pull-requests/10744
  def sale_detail_transactions_base_query(selected_location_ids, per_location = false)
    subquery = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                          .by_datetime_range(start_date, end_date)
                                                          .where(location_id: @location_ids)
                                                          .where('public_sale_detail_transactions.quantity > 0')

    if is_select_all_product && exclude_product_ids.present?
      subquery = subquery.where.not("hasAny(public_sale_detail_transactions.sale_product_ids, [#{exclude_product_ids.join(',')}])")
    end
    subquery = subquery.where("hasAny(public_sale_detail_transactions.sale_product_ids, [#{product_ids.join(',')}])") if product_ids.present?

    if order_type_ids.present? && group_by == PRODUCT
      subquery = subquery.where("public_sale_detail_transactions.order_type_id IN (#{order_type_ids.join(',')}")
    end

    subquery = subquery.limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    query = ::Clickhouse::Models::SaleDetailTransaction.from("(#{subquery}) AS public_sale_detail_transactions")
                                                       .where('public_sale_detail_transactions.status = 0')

    query.where("public_sale_detail_transactions.takings_id IN (#{taking_ids.join(',')}") if taking_ids.present?

    sales_query.select(date_field + number_fields).group(group_by_sale_details(per_location).to_s)
    ::Report::Models::SaleDetailTransaction
      .joins(sale_details_joins.to_s)
      .where(sale_details_condition(selected_location_ids).to_s)
      .where(
        "CASE WHEN ? THEN sale_detail_transactions.meta ->> 'cost' IS NULL ELSE 1=1 END", per_location
      )
      .group(group_by_sale_details(per_location).to_s)
      .select(
        <<~SQL
          #{select_sale_detail_transactions_based_on_filter_group_by(per_location)},
          sale_detail_transactions.location_id,
          sale_detail_transactions.product_id AS product_id,
          SUM(quantity) AS qty,
          SUM(quantity * sale_detail_transactions.product_unit_conversion_qty) AS qty_with_conversion,
          SUM(
            COALESCE(meta ->> 'parent_rule_total_line_discount_prorate', '0')::decimal /
            #{sale_details_tax}
          ) AS sales_amount,
          SUM(
            coalesce(meta ->> 'parent_rule_total_line_amount', '0')::decimal /
            #{sale_details_tax}
          ) AS gross_sales_amount,
          SUM(COALESCE(sale_detail_transactions.meta ->> 'cost', '0')::decimal) AS cost,
          bool_and(COALESCE((sale_detail_transactions.meta ->> 'cost')::decimal >= 0, false)) AS should_include_cost,
          SUM(
            (
              (
                COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_discount', '0')::decimal /
                #{sale_details_tax}
              ) + COALESCE(sale_detail_transactions.meta ->> 'sales_by_free_of_charge', '0')::decimal
            )
          ) AS discount,
          SUM(COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_surcharge', '0')::decimal /
              #{sale_details_tax}) AS surcharge
        SQL
      )
      .lazy.map(&:attributes)
  end
  # rubocop:enable Metrics/AbcSize

  def query_sale_detail_transactions_per_location(selected_location_ids)
    sale_detail_transactions_base_query(selected_location_ids, true)
      .filter { |result| result['location_id'].present? } # obtain group set by location
      .index_by { |result| per_location_lookup_key(result) }
  end

  def query_sale_detail_transactions(selected_location_ids)
    sale_detail_transactions_base_query(selected_location_ids)
      .reject { |result| result['location_id'].present? } # skip group set by location
      .index_by { |result| lookup_key(result) }
  end

  def sale_detail_modifiers_base_query(selected_location_ids, per_location = false)
    base_query = ::Report::Models::SaleDetailModifier
                 .joins("JOIN sale_detail_transactions ON sale_detail_transactions.id = sale_detail_modifiers.sale_detail_transaction_id
        JOIN products ON products.id = sale_detail_modifiers.product_id
        LEFT JOIN product_categories ON product_categories.id = products.product_category_id
        LEFT JOIN product_category_groups ON product_category_groups.id = product_categories.product_category_group_id")
                 .where(modifiers_condition(selected_location_ids).to_s)
                 .where(
                   "CASE
                    WHEN ? THEN
                      sale_detail_modifiers.meta ->> 'cost' IS NULL
                    ELSE
                      1=1
                   END", per_location
                 )
                 .group(group_by_modifiers(per_location).to_s)
                 .select("
        #{select_sale_detail_modifiers_based_on_filter_group_by(per_location)},
        SUM(CASE WHEN sale_detail_transactions.quantity = 0 THEN
          0
        ELSE
          sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity
        END) AS qty,
        SUM(CASE WHEN sale_detail_transactions.quantity = 0 THEN
          0
        ELSE
          sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity
        END * sale_detail_modifiers.product_unit_conversion_qty) AS qty_with_conversion,
        sale_detail_modifiers.location_id,
        SUM(#{modifiers_gross_sales_amount}) AS sales_amount,
        SUM(#{modifiers_gross_sales_amount}) AS gross_sales_amount,
        SUM(COALESCE(sale_detail_modifiers.meta ->> 'cost', '0')::decimal) AS cost,
        bool_and(COALESCE((sale_detail_modifiers.meta ->> 'cost')::decimal >= 0, false)) AS should_include_cost,
        SUM(#{modifiers_discount}) AS discount,
        SUM(#{modifiers_surcharge}) AS surcharge
      ")

    with_grouping_sets = <<~SQL
      WITH inner_sale_detail_modifiers AS (#{base_query.to_sql})
      SELECT
        #{select_inner_sale_modifier},
        SUM(inner_sale_detail_modifiers.qty) AS qty,
        SUM(inner_sale_detail_modifiers.cost) AS cost,
        inner_sale_detail_modifiers.location_id,
        SUM(inner_sale_detail_modifiers.sales_amount) AS sales_amount,
        SUM(inner_sale_detail_modifiers.gross_sales_amount) AS gross_sales_amount,
        SUM(inner_sale_detail_modifiers.discount) AS discount,
        SUM(inner_sale_detail_modifiers.surcharge) AS surcharge,
        SUM(inner_sale_detail_modifiers.qty_with_conversion) AS qty_with_conversion,
        BOOL_AND(inner_sale_detail_modifiers.should_include_cost) AS should_include_cost
      FROM inner_sale_detail_modifiers
      GROUP BY #{grouping_sets_by_modifiers}
    SQL

    ::Report::Models::SaleTransaction.connection.exec_query(ActiveRecord::Base.sanitize_sql(with_grouping_sets)).lazy
  end
  # rubocop:enable Metrics/MethodLength

  def query_sale_detail_modifiers_per_location(selected_location_ids)
    sale_detail_modifiers_base_query(selected_location_ids, true)
      .filter { |result| result['location_id'].present? } # obtain group set by location
      .index_by { |result| modifier_per_location_lookup_key(result) }
  end

  def query_sale_detail_modifiers(selected_location_ids)
    sale_detail_modifiers_base_query(selected_location_ids)
      .reject { |result| result['location_id'].present? } # skip group set by location
      .index_by { |result| lookup_key(result) }
  end

  def select_inner_sale_modifier
    if @group_by == PRODUCT
      "inner_sale_detail_modifiers.location_id,
       inner_sale_detail_modifiers.id,
       inner_sale_detail_modifiers.sku,
       inner_sale_detail_modifiers.name,
       inner_sale_detail_modifiers.category,
       modifier_follow_cost_sale_detail_product_id"
    elsif @group_by == CATEGORY_GROUP
      'inner_sale_detail_modifiers.location_id,
        inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name,
        modifier_follow_cost_sale_detail_product_id'
    else
      'inner_sale_detail_modifiers.location_id,
        inner_sale_detail_modifiers.id,
        inner_sale_detail_modifiers.name,
        modifier_follow_cost_sale_detail_product_id'
    end
  end

  # rubocop:disable Metrics/MethodLength
  def sales_return_lines_base_query(selected_location_ids, refund_reasons = SalesReturn.refund_reasons.values, per_location = false)
    ::Report::Models::SalesReturnLine
      .joins("
        JOIN sales_returns ON sales_returns.id = sales_return_lines.sales_return_id
        JOIN sale_detail_transactions ON sale_detail_transactions.id = sales_return_lines.sale_detail_transaction_id
        #{sale_details_joins}
      ")
      .where("sales_returns.brand_id = #{brand_id} AND
              sales_returns.location_id IN (#{selected_location_ids.join(',')}) AND
              sales_returns.status = 0 AND sales_returns.deleted = false")
      .where("#{sale_details_condition(selected_location_ids)} AND sale_detail_transactions.quantity > 0")
      .where("CASE WHEN ? THEN sale_detail_transactions.meta ->> 'cost' IS NULL ELSE 1=1 END", per_location)
      .where('sales_returns.refund_reason IN (?)', refund_reasons)
      .group(group_by_sale_details(per_location).to_s)
      .select("
        #{select_sale_detail_transactions_based_on_filter_group_by(per_location)},
        SUM(return_quantity) AS refund_qty,
        SUM(return_quantity * sale_detail_transactions.product_unit_conversion_qty) * -1 AS qty_with_conversion,
        sale_detail_transactions.location_id,
        SUM(
          #{return_qty_from_sale_detail} *
          (COALESCE(meta ->> 'parent_rule_total_line_discount_prorate', '0')::decimal / #{sale_details_tax})
        ) AS sales_amount,
        SUM(
          #{return_qty_from_sale_detail} * -1 *
          (COALESCE(meta ->> 'parent_rule_total_line_amount', '0')::decimal / #{sale_details_tax})
        ) AS refund_amount,
        SUM(
          #{return_qty_from_sale_detail} *
          (
            (COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_discount', '0')::decimal / #{sale_details_tax}) +
            COALESCE(sale_detail_transactions.meta ->> 'sales_by_free_of_charge', '0')::decimal
          )
        ) AS discount,
        SUM(
          #{return_qty_from_sale_detail} *
          (COALESCE(sale_detail_transactions.meta ->> 'parent_rule_prorate_surcharge', '0')::decimal / #{sale_details_tax})
        ) AS surcharge
      ")
      .lazy.map(&:attributes)
  end
  # rubocop:enable Metrics/MethodLength

  def query_sales_return_lines_per_location(selected_location_ids, _refund_reasons = SalesReturn.refund_reasons.values)
    sales_return_lines_base_query(selected_location_ids, [SalesReturn.refund_reasons['item_not_delivered']], true)
      .filter { |result| result['location_id'].present? } # obtain group set by location
      .index_by { |result| per_location_lookup_key(result) }
  end

  def query_sales_return_lines(selected_location_ids)
    sales_return_lines_base_query(selected_location_ids)
      .reject { |result| result['location_id'].present? } # skip group set by location
      .index_by { |result| lookup_key(result) }
  end

  # rubocop:disable Metrics/MethodLength
  def sales_return_lines_for_modifiers_base_query(selected_location_ids,
                                                  refund_reasons = SalesReturn.refund_reasons.values, per_location = false)
    base_query = ::Report::Models::SalesReturnLine
                 .joins("
        JOIN sales_returns ON sales_returns.id = sales_return_lines.sales_return_id
        JOIN sale_detail_transactions ON sale_detail_transactions.id = sales_return_lines.sale_detail_transaction_id
        JOIN sale_detail_modifiers ON sale_detail_modifiers.sale_detail_transaction_id = sale_detail_transactions.id
        LEFT JOIN products ON products.id = sale_detail_modifiers.product_id
        LEFT JOIN product_categories ON product_categories.id = products.product_category_id
        LEFT JOIN product_category_groups ON product_category_groups.id = product_categories.product_category_group_id
      ")
                 .where("sales_returns.location_id IN (#{selected_location_ids.join(',')})")
                 .where("sale_detail_modifiers.status = 0 AND sale_detail_modifiers.local_sales_time BETWEEN '#{start_date}' AND '#{end_date}'")
                 .where("sales_returns.status = 0 AND sales_returns.deleted = false
                AND sale_detail_transactions.quantity > 0")
                 .where(
                   "CASE
                    WHEN ? THEN
                      sale_detail_modifiers.meta ->> 'cost' IS NULL
                    ELSE
                      1=1
                   END", per_location
                 )
                 .where('sales_returns.refund_reason IN (?)', refund_reasons)
                 .group(group_by_modifiers(per_location).to_s)
                 .select("
        #{select_sale_detail_modifiers_based_on_filter_group_by(per_location)},
        sale_detail_modifiers.location_id,
        SUM(#{modifiers_return_quantity} * -1) AS refund_qty,
        SUM(#{modifiers_return_quantity} * sale_detail_modifiers.product_unit_conversion_qty) AS qty_with_conversion,
        SUM(#{modifiers_prorate_return_quantity} * -1 *
            COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_total_line_amount', '0')::decimal /
            #{tax_of_modifiers}
        ) AS refund_amount,
        SUM(#{modifiers_prorate_return_quantity} *
            (
              (
                COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_prorate_discount', '0')::decimal /
                #{tax_of_modifiers}
              ) + COALESCE(sale_detail_modifiers.meta ->> 'sales_by_free_of_charge', '0')::decimal
            )
        ) AS discount,
        SUM(#{modifiers_prorate_return_quantity} *
            COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_prorate_surcharge', '0')::decimal /
            #{tax_of_modifiers}
        ) AS surcharge
      ")

    with_grouping_sets = <<~SQL
      WITH inner_sale_detail_modifiers AS (#{base_query.to_sql})
      SELECT
        #{select_inner_sale_modifier},
        SUM(inner_sale_detail_modifiers.refund_qty) AS refund_qty,
        SUM(inner_sale_detail_modifiers.qty_with_conversion) AS qty_with_conversion,
        inner_sale_detail_modifiers.location_id,
        SUM(inner_sale_detail_modifiers.refund_amount) AS refund_amount,
        SUM(inner_sale_detail_modifiers.discount) AS discount,
        SUM(inner_sale_detail_modifiers.surcharge) AS surcharge
      FROM inner_sale_detail_modifiers
      GROUP BY #{grouping_sets_by_modifiers}
    SQL

    ::Report::Models::SaleTransaction.connection.exec_query(ActiveRecord::Base.sanitize_sql(with_grouping_sets))
                                     .lazy
  end
  # rubocop:enable Metrics/MethodLength

  def query_sales_return_lines_for_modifiers_per_location(selected_location_ids)
    sales_return_lines_for_modifiers_base_query(selected_location_ids, [SalesReturn.refund_reasons['item_not_delivered']], true)
      .filter { |result| result['location_id'].present? } # skip group set by location
      .index_by { |result| modifier_per_location_lookup_key(result) }
  end

  def query_sales_return_lines_for_modifiers(selected_location_ids)
    sales_return_lines_for_modifiers_base_query(selected_location_ids)
      .reject { |result| result['location_id'].present? } # skip group set by location
      .index_by { |result| lookup_key(result) }
  end
end
# rubocop:enable Metrics/ClassLength
