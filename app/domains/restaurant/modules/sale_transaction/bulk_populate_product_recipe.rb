# NOTE: Previously: Calling includes on resource lines will always generate the includes query,
# and causing N+1 includes query since it will call as many times as resource lines count.
# Now: This service will includes all the necessary resource ONCE for all the nested resource lines.
module Restaurant::Modules::SaleTransaction::BulkPopulateProductRecipe
  def eager_load_recipes_for_sale_details_and_modifiers(sale_transaction, brand_id)
    product_ids = []
    sale_detail_product_ids = []

    sale_transaction.sale_detail_transactions.each do |sale_detail_transaction|
      product_ids << sale_detail_transaction.product_id
      sale_detail_product_ids << sale_detail_transaction.product_id
      sale_detail_transaction.sale_detail_modifiers.each do |sale_detail_modifier|
        product_ids << sale_detail_modifier.product_id
      end
    end

    recipe_query_includes = {
      brand: {},
      recipe_line_customs: [recipe_line_custom_details: %i[product product_unit]],
      recipe_lines: [:product, :product_unit, { recipe_line_substitutes: %i[product product_unit] }]
    }

    recipe_product_option_set_ids = RecipeProductOptionSet.where(brand_id: brand_id, product_id: sale_detail_product_ids)

    recipes = Recipe.includes(recipe_query_includes).where(brand_id: brand_id, product_id: product_ids).or(
      Recipe.includes(recipe_query_includes).where(recipe_product_option_set_id: recipe_product_option_set_ids)
    )

    sale_transaction.sale_details_and_modifiers_recipes = recipes
  end

  # rubocop:disable Metrics/AbcSize
  def find_and_populate_product_recipe(sale_transaction, sale_resource_line, recipe_order_type_id)
    return if sale_transaction.is_from_preorder && sale_transaction.customer_order&.production_schedules.present?

    brand_id = sale_transaction.brand_id
    eager_load_recipes_for_sale_details_and_modifiers(sale_transaction, brand_id) if sale_transaction.sale_details_and_modifiers_recipes.blank?

    # if parent already create the recipe just skip
    if sale_resource_line.is_a?(SaleDetailModifier) && sale_resource_line.sale_detail_transaction.use_product_option_set_recipe.present? &&
       sale_resource_line.meta.present? && sale_resource_line.meta['option_set_option_id'].present?
      parent_option_set_ids = sale_resource_line.sale_detail_transaction.use_product_option_set_recipe
      return if parent_option_set_ids.include?(sale_resource_line.meta['option_set_option_id'])
    end

    # select recipe product_option_set first
    if sale_resource_line.is_a?(SaleDetailTransaction) && sale_resource_line.calculated_option_set_options.present?
      recipe_product_option_set = RecipeProductOptionSet.where(brand_id: brand_id, product_id: sale_resource_line.product_id)
                                                        .where('option_set_option_ids <@ ARRAY[?]::integer[]',
                                                               sale_resource_line.calculated_option_set_options.keys)
                                                        .where(id: sale_transaction
                                                                    .sale_details_and_modifiers_recipes
                                                                    .pluck(:recipe_product_option_set_id).compact)
                                                        .first
      if recipe_product_option_set.present?
        recipe = sale_transaction.sale_details_and_modifiers_recipes.detect do |curr_recipe|
          curr_recipe.recipe_product_option_set_id == recipe_product_option_set.id
        end
      end
    end

    if recipe.nil?
      recipe = sale_transaction.sale_details_and_modifiers_recipes.detect { |curr_recipe| curr_recipe.product_id == sale_resource_line.product_id }
    end
    populate_product_recipe(sale_resource_line, recipe, recipe_order_type_id, sale_transaction.stock_outs)
  end
  # rubocop:enable Metrics/AbcSize

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def populate_product_recipe(sale_resource_line, recipe, recipe_order_type_id, stock_outs)
    return if recipe.nil?
    return unless recipe.made_to_order? && recipe.active?

    if recipe.recipe_product_option_set_id.present? && sale_resource_line.is_a?(SaleDetailTransaction)
      sale_resource_line.use_product_option_set_recipe = recipe.recipe_product_option_set.option_set_option_ids
    end

    recipe_line_customs = recipe.recipe_line_customs.filter { |line_custom| line_custom.order_type_ids.include?(recipe_order_type_id) }

    collected_recipe_lines = []
    location_id = sale_resource_line.location_id

    default_storage_section = ::Report::Models::StorageSection.find_by(location_id: location_id, section_type: 'storage',
                                                                       default_out: true)

    recipe_line_customs.each do |recipe_line_custom|
      recipe_line_custom.recipe_line_custom_details.each do |recipe_line|
        collected_recipe_lines << recipe_line.select_suitable_product_recipe_custom(location_id: location_id,
                                                                                    default_storage_section: default_storage_section)
      end
    end

    production_setting = recipe.brand.production_setting
    recipe.recipe_lines.each do |recipe_line|
      collected_recipe_lines << recipe_line.select_suitable_product_recipe(
        use_substitute: true,
        multiplier_quantity: sale_resource_line.quantity,
        stock_outs: stock_outs,
        production_setting: production_setting
      )
    end

    # use swap only apply for modifier
    if sale_resource_line.is_a?(SaleDetailModifier)
      sale_line = sale_resource_line.sale_detail_transaction
      recipe.recipe_line_swap_products.each do |swap|
        product_recipe = sale_line.product_recipe.find { |recipe_line| recipe_line['product_id'] == swap.product_from_id }
        next if product_recipe.blank?

        collected_recipe_lines << {
          product_id: product_recipe['product_id'],
          quantity: -1 * product_recipe['quantity'].to_d,
          product_unit_id: product_recipe['product_unit_id'],
          product_unit_name: product_recipe['product_unit_name'],
          product_no_stock: product_recipe['product_no_stock'],
          product_unit_conversion_qty: product_recipe['product_unit_conversion_qty'],
          storage_section_id: product_recipe['storage_section_id'],
          product: swap.product_from
        }
        next unless swap.product_to.present? && swap.product_to.allowable_product_units.include?(product_recipe['product_unit_id'])

        collected_recipe_lines << {
          product_id: swap.product_to_id,
          quantity: product_recipe['quantity'].to_d * (swap.ratio / 100.0),
          product_unit_id: product_recipe['product_unit_id'],
          product_unit_name: product_recipe['product_unit_name'],
          product_no_stock: product_recipe['product_no_stock'],
          product_unit_conversion_qty: product_recipe['product_unit_conversion_qty'],
          storage_section_id: product_recipe['storage_section_id'],
          product: swap.product_to
        }
      end
    end

    collected_grouped_recipe_lines = collected_recipe_lines
                                     .group_by { |recipe_line| recipe_line.values_at(:product_id, :product_unit_id, :storage_section_id) }
                                     .map do |group, grouped_recipe_lines|
      product_id, product_unit_id, storage_section_id = group

      total_quantity = grouped_recipe_lines.sum { |recipe_line| recipe_line[:quantity] }
      recipe_line = grouped_recipe_lines.first

      {
        product_id: product_id,
        product_unit_id: product_unit_id,
        product_unit_name: recipe_line[:product_unit_name],
        product_no_stock: recipe_line[:product_no_stock],
        product_unit_conversion_qty: recipe_line[:product_unit_conversion_qty],
        quantity: total_quantity,
        storage_section_id: storage_section_id
      }
    end

    sale_resource_line.product_recipe = collected_grouped_recipe_lines
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
