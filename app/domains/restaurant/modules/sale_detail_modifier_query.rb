module Restaurant::Modules::SaleDetailModifierQuery
  def by_date_range(start_date, end_date)
    where("#{table_name}.local_sales_time >= '#{start_date.to_date.beginning_of_day}'
             AND #{table_name}.local_sales_time <= '#{end_date.to_date.end_of_day}'")
  end

  def group_id_clickhouse
    group('id')
  end

  # don't add beginning of day and end of day, because this will use to filter date range using cut off time
  def by_datetime_range(start_date, end_date)
    where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_date, end_date)
  end

  def by_location_ids(location_ids)
    where("#{table_name}.location_id IN (?)", location_ids)
  end

  def by_brand(brand_id)
    where("#{table_name}.brand_id = ?", brand_id)
  end

  def by_brand_and_status(brand_id)
    where("#{table_name}.status = ? AND #{table_name}.brand_id = ?", SaleTransaction.statuses[:ok], brand_id)
  end

  def details_ids_by_option_set_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
      .select("DISTINCT #{table_name}.sale_detail_transaction_id")
  end

  def ids_by_option_set_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
      .select("DISTINCT #{table_name}.id")
  end

  def by_field_option_set_id_and_product_ids(brand_id, location_ids, option_set_id, products_ids)
    where("#{table_name}.option_set_id = ?
              AND #{table_name}.product_id IN (?)
              AND #{table_name}.brand_id = ?
              AND #{table_name}.location_id IN (?)",
          option_set_id, products_ids, brand_id, location_ids)
  end

  def by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    where("(#{table_name}.meta ->> 'option_set_id')::integer = ?
              AND #{table_name}.product_id IN (?)
              AND #{table_name}.brand_id = ?
              AND #{table_name}.location_id IN (?)",
          option_set_id, products_ids, brand_id, location_ids)
  end
end
