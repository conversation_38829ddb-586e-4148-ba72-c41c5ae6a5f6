module Restaurant::Modules::Report::SalesPromotions::Clickhouse::Base<PERSON>uer<PERSON>
  def base_query_transaction_manual_promo(location_ids)
    query_modifiers = discount_line_manual_promo_sql(location_ids)
    subquery_sales = ::Clickhouse::Models::SaleTransaction.with_deleted
                                                          .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                .strftime('%Y-%m-%d %H:%M:%S'),
                                                                             @end_date.to_date.end_of_day
                                                                                             .strftime('%Y-%m-%d %H:%M:%S'))
                                                          .where(location_id: location_ids)
                                                          .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')
    ::Clickhouse::Models::SaleTransaction.from("(#{subquery_sales}) AS public_sale_transactions")
                                         .joins(Arel.sql("LEFT JOIN (#{query_modifiers}) AS modifier_discounts
                                                                  ON modifier_discounts.sale_transaction_id
                                                                  = public_sale_transactions.id AND
                                                            empty(public_sale_transactions.promo_ids)"))
                                         .where('public_sale_transactions.status = 0')
                                         .where('(public_sale_transactions.discount_fee -
                                                          public_sale_transactions.loyalty_discount_fee) > 0
                                                        OR modifier_discounts.discount_line != 0')
  end

  def discount_line_manual_promo_sql(location_ids)
    subquery_sale_details = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                                       .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                            .strftime('%Y-%m-%d %H:%M:%S'),
                                                                                          @end_date.to_date.end_of_day
                                                                                                           .strftime('%Y-%m-%d %H:%M:%S'))
                                                                       .where(location_id: location_ids)
                                                                       .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    subquery_sale_modifiers = ::Clickhouse::Models::SaleDetailModifier.with_deleted
                                                                      .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                            .strftime('%Y-%m-%d %H:%M:%S'),
                                                                                         @end_date.to_date.end_of_day
                                                                                                         .strftime('%Y-%m-%d %H:%M:%S'))
                                                                      .where(location_id: location_ids)
                                                                      .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    ::Clickhouse::Models::SaleDetailModifier.from("(#{subquery_sale_modifiers}) AS public_sale_detail_modifiers")
                                            .joins(Arel.sql("JOIN (#{subquery_sale_details}) AS public_sale_detail_transactions
                                                                  ON public_sale_detail_transactions.id
                                                                  = public_sale_detail_modifiers.sale_detail_transaction_id"))
                                            .where('public_sale_detail_modifiers.product_id = 0 AND public_sale_detail_modifiers.price < 0')
                                            .where('public_sale_detail_modifiers.status = 0')
                                            .where('public_sale_detail_transactions.status = 0')
                                            .group('public_sale_detail_transactions.sale_transaction_id')
                                            .select(
                                              'public_sale_detail_transactions.sale_transaction_id AS sale_transaction_id',
                                              'SUM(-1 * public_sale_detail_modifiers.total_line_amount) AS discount_line'
                                            ).to_sql
  end

  def base_query_transaction_manual_promo_with_applied_promos(location_ids)
    query_modifiers = discount_line_manual_promo_sql_for_sales_with_applied_promos(location_ids)
    subquery_sales = ::Clickhouse::Models::SaleTransaction.with_deleted
                                                          .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                .strftime('%Y-%m-%d %H:%M:%S'),
                                                                             @end_date.to_date.end_of_day
                                                                                             .strftime('%Y-%m-%d %H:%M:%S'))
                                                          .where(location_id: location_ids)
                                                          .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    ::Clickhouse::Models::SaleTransaction.from("(#{subquery_sales}) AS public_sale_transactions")
                                         .joins(Arel.sql("JOIN (#{query_modifiers}) AS modifier_discounts
                                                                  ON modifier_discounts.sale_transaction_id
                                                                  = public_sale_transactions.id AND
                                                            notEmpty(public_sale_transactions.promo_ids)"))
                                         .where('public_sale_transactions.status = 0')
  end

  def discount_line_manual_promo_sql_for_sales_with_applied_promos(location_ids)
    subquery_sale_details = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                                       .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                            .strftime('%Y-%m-%d %H:%M:%S'),
                                                                                          @end_date.to_date.end_of_day
                                                                                                           .strftime('%Y-%m-%d %H:%M:%S'))
                                                                       .where(location_id: location_ids)
                                                                       .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    subquery_sale_modifiers = ::Clickhouse::Models::SaleDetailModifier.with_deleted
                                                                      .by_datetime_range(@start_date.to_date.beginning_of_day
                                                                                                            .strftime('%Y-%m-%d %H:%M:%S'),
                                                                                         @end_date.to_date.end_of_day
                                                                                                         .strftime('%Y-%m-%d %H:%M:%S'))
                                                                      .where(location_id: location_ids)
                                                                      .limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')

    ::Clickhouse::Models::SaleDetailModifier.from("(#{subquery_sale_modifiers}) AS public_sale_detail_modifiers")
                                            .joins(Arel.sql("JOIN (#{subquery_sale_details}) AS public_sale_detail_transactions
                                                                  ON public_sale_detail_transactions.id
                                                                  = public_sale_detail_modifiers.sale_detail_transaction_id"))
                                            .where('public_sale_detail_transactions.deleted = FALSE')
                                            .where('empty(toString(public_sale_detail_modifiers.promo_id))')
                                            .where('public_sale_detail_modifiers.status = 0')
                                            .where('public_sale_detail_modifiers.product_id = 0 AND public_sale_detail_modifiers.price < 0')
                                            .group('public_sale_detail_transactions.sale_transaction_id')
                                            .select(
                                              'public_sale_detail_transactions.sale_transaction_id AS sale_transaction_id',
                                              'SUM(-1 * public_sale_detail_modifiers.total_line_amount) AS discount_line'
                                            ).to_sql
  end
end
