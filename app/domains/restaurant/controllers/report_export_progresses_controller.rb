class Restaurant::Controllers::ReportExportProgressesController < Api::BaseController
  before_action :set_report_export_progress, only: %i[show]
  before_action :validate_permission, except: %i[index show report_types bulk_destroy]

  def index
    report_export_progresses = Restaurant::Services::Report::ReportExportProgressesListGenerator
                               .new(params.merge({ user: current_user })).call

    render json: report_export_progresses, status: :ok
  end

  def show
    label = Restaurant::Models::ReportExportProgress.label_for(@report_export_progress.report_type)

    response = @report_export_progress
               .attributes
               .except('saved_part_keys')
               .merge(signed_file_links: @report_export_progress.signed_file_links)
               .merge(report_type_label: label)

    render json: response, status: :ok
  end

  def report_types
    report_type_responses = Restaurant::Models::ReportExportProgress.report_types.keys.map do |report_type|
      label = Restaurant::Models::ReportExportProgress.label_for(report_type)

      { report_type: report_type, label: label }
    end

    render json: report_type_responses, status: :ok
  end

  def bulk_destroy
    # NOTE: No matter what, we only want to process per 100 items in a batch
    params['ids'].to_a.each_slice(100) do |hundred_ids|
      Restaurant::Models::ReportExportProgress.where(user_id: current_user.id, status: 'completed', id: hundred_ids).destroy_all
    end

    head :ok
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_report_export_progress
    @report_export_progress = current_user.report_export_progresses.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def report_export_progress_params
    params.fetch(:report_export_progress, {})
  end
end
