class BCA::Jobs::CreateUserDisbursementJob < ApplicationJob
  queue_as :exempt_from_recovery

  include BCA::Modules::Common

  INTRABANK_CODE = 'BCA'.freeze
  DELAY = 5.minutes.freeze
  MAXIMUM_RETRY = 3

  def perform(disbursement_id:, retry_count: 1, original_external_id: nil)
    @disbursement_id = disbursement_id
    @retry_count = retry_count
    @original_external_id = original_external_id

    create
  end

  private

  def create
    @disbursement = find_disbursement
    return if @disbursement.blank?

    succeded_disburse = verify_and_update_disbursement_status(@retry_count, @original_external_id)
    return if succeded_disburse

    external_id = build_external_id
    params = build_disbursement_params(external_id)
    request, response = transfer_to_bank(params)

    unless response.succeeded?
      message = build_error_message(request, response)

      raise_issue_to_sentry(message)
    end

    check_disbursement_attributes(response.body)
    update_disbursement(response, external_id)
  rescue Faraday::TimeoutError, Net::ReadTimeout => e
    retry_disbursement_job(@retry_count, external_id, e.message)
  end

  def find_disbursement
    UserDisbursement.find_by(id: @disbursement_id)
  end

  def verify_and_update_disbursement_status(retry_count, original_external_id)
    return false if retry_count == 1

    check_status_params = {
      original_external_id: original_external_id,
      service_code: find_service_code(@disbursement.disbursement_bank_code)
    }

    response = BCA::Client.check_transaction_status(check_status_params)
    response_body = response.body

    return false unless disbursement_status_complete?(response_body)

    @disbursement.update!(
      status: 'success',
      provider_reference_id: response_body['originalReferenceNo'],
      provider_raw_response: response_body,
      external_id: original_external_id
    )

    true
  end

  def build_external_id
    generate_external_id
  end

  def build_disbursement_params(external_id)
    {
      amount: @disbursement.net_received,
      currency_code: @disbursement.currency_code,
      beneficiary_bank_code: @disbursement.disbursement_bank_code,
      beneficiary_account_no: @disbursement.disbursement_bank_account_number,
      beneficiary_account_name: @disbursement.disbursement_bank_account_name,
      remark: "Disb #{@disbursement.account.brand.name.gsub(/[^a-zA-Z0-9 ]/, '').squeeze(' ').strip.first(31)}", # max 36 characters from BCA
      email: nil,
      citizenship_status: @disbursement.citizenship_status,
      external_id: external_id
    }
  end

  def transfer_to_bank(params)
    return BCA::Client.transfer_intrabank(params) if @disbursement.disbursement_bank_code == INTRABANK_CODE

    BCA::Client.transfer_interbank(params)
  end

  def check_disbursement_attributes(response_body)
    params = {
      amount: @disbursement.net_received.to_i,
      bank_account_number: @disbursement.disbursement_bank_account_number.to_s
    }

    return if valid_attributes?(response_body, params)

    message = "different disbursement amount or receiver to BCA user_disbursement_id: #{@disbursement.id}, please check!"
    Sentry.capture_exception(BCA::Errors::DifferentDisbursementAttributes.new(message))
  end

  def build_error_message(request, response)
    message = "[URGENT] failed to disburse user_disbursement: #{@disbursement.id}, please check!"
    message += "\nheaders: #{request.headers.to_json}" if response.error_signature?

    message
  end

  def update_disbursement(response, external_id)
    response_body = response.body

    @disbursement.update!(
      status: response.succeeded? ? 'success' : 'failed',
      provider_reference_id: response_body['referenceNo'],
      provider_raw_response: response_body,
      external_id: external_id
    )
  end

  def retry_disbursement_job(retry_count, external_id, message)
    if retry_count >= MAXIMUM_RETRY
      @disbursement.update!(
        status: 'failed',
        external_id: external_id,
        provider_raw_response: message
      )

      raise
    end

    BCA::Jobs::CreateUserDisbursementJob
      .set(wait: DELAY)
      .perform_later(
        disbursement_id: @disbursement.id,
        retry_count: retry_count + 1,
        original_external_id: external_id
      )
  end

  def raise_issue_to_sentry(message)
    Sentry.with_scope do |scope|
      scope.set_tags(disbursement_error: 'true')

      Sentry.capture_exception(BCA::Errors::FailedToDisburse.new(message), level: :info)
    end
  end
end
