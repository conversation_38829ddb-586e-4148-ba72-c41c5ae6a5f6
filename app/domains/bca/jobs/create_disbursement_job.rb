class BCA::Jobs::CreateDisbursementJob < ApplicationJob
  queue_as :exempt_from_recovery

  include BCA::Modules::Common
  DELAY = 5.minutes
  MAXIMUM_RETRY = 3

  def perform(location_disbursement_id:, retry_count: 1, original_external_id: nil)
    location_disbursement = LocationDisbursement.find_by(id: location_disbursement_id)
    return if location_disbursement.blank?

    succeded_disburse = verify_and_update_disbursement_status(location_disbursement, original_external_id) if retry_count > 1
    return if succeded_disburse

    external_id = generate_external_id
    params = create_disbursement_params(location_disbursement, external_id)
    request, response = location_disbursement.bank_code == 'BCA' ? BCA::Client.transfer_intrabank(params) : BCA::Client.transfer_interbank(params)

    unless response.succeeded?
      message = "[URGENT] failed to disburse location_disbursement_id: #{location_disbursement.id}, please check!"
      message += "\nheaders: #{request.headers.to_json}" if response.error_signature?

      raise_issue_to_sentry(message)
    end

    response_body = response.body
    check_disbursement_attributes(response_body, location_disbursement)

    # there is no PENDING status for BCA intrabank disbursement and Interbank BI FAST disbursement
    location_disbursement.update!(
      status: response.succeeded? ? 'COMPLETED' : 'FAILED',
      provider_reference_id: response_body['referenceNo'],
      provider_raw_response: response_body,
      external_id: external_id
    )
  rescue Faraday::TimeoutError, Net::ReadTimeout => e
    if retry_count >= MAXIMUM_RETRY
      location_disbursement.update!(
        status: 'FAILED',
        external_id: external_id,
        provider_raw_response: e.message
      )

      raise
    end

    retry_disbursement_job(location_disbursement_id, retry_count, external_id)
  end

  def retry_disbursement_job(location_disbursement_id, retry_count, external_id)
    BCA::Jobs::CreateDisbursementJob
      .set(wait: DELAY)
      .perform_later(location_disbursement_id: location_disbursement_id, retry_count: retry_count + 1, original_external_id: external_id)
  end

  private

  def verify_and_update_disbursement_status(location_disbursement, external_id)
    service_code = location_disbursement.bank_code == 'BCA' ? BCA::Constants::INTRABANK_SERVICE_CODE : BCA::Constants::INTERBANK_SERVICE_CODE
    check_status_params = {
      original_external_id: external_id,
      service_code: service_code
    }

    response = BCA::Client.check_transaction_status(check_status_params)

    response_body = response.body

    # if not succeded, then either transaction is not found or failed, can be allowed to retry
    if response_body['responseCode'] == BCA::Constants::INTERBANK_CHECK_TRANSFER_STATUS_SUCCESSFUL_CODE &&
       response_body['latestTransactionStatus'] == BCA::Constants::SUCCESSFUL_TRANSACTION_STATUS

      location_disbursement.update!(
        status: 'COMPLETED',
        provider_reference_id: response_body['originalReferenceNo'],
        provider_raw_response: response_body,
        external_id: external_id
      )

      return true
    end

    false
  end

  def create_disbursement_params(location_disbursement, external_id)
    {
      amount: location_disbursement.net_amount,
      currency_code: location_disbursement.currency_code,
      beneficiary_account_no: location_disbursement.bank_account_number,
      beneficiary_bank_code: location_disbursement.bca_interbank_code,
      beneficiary_account_name: location_disbursement.bank_account_name,
      remark: "Disb #{location_disbursement.location.name.gsub(/[^a-zA-Z0-9 ]/, '').squeeze(' ').strip.first(31)}", # max 36 characters from BCA
      email: nil,
      citizenship_status: location_disbursement.citizenship_status,
      external_id: external_id
    }
  end

  # NOTE: BCA doesn't give bank account name and bank code in the response
  def check_disbursement_attributes(response_body, location_disbursement)
    return unless response_body['responseCode'] == BCA::Constants::INTRABANK_TRANSFER_SUCCESSFULL_CODE

    return if response_body.dig('amount', 'value').to_i == location_disbursement.net_amount.to_i &&
              response_body['beneficiaryAccountNo'] == location_disbursement.bank_account_number

    message = "different disbursement amount or receiver to BCA location_disbursement_id: #{location_disbursement.id}, please check!"
    Sentry.capture_exception(BCA::Errors::DifferentDisbursementAttributes.new(message))
  end

  def raise_issue_to_sentry(message)
    Sentry.with_scope do |scope|
      scope.set_tags(disbursement_error: 'true')

      Sentry.capture_exception(BCA::Errors::FailedToDisburse.new(message), level: :info)
    end
  end
end
