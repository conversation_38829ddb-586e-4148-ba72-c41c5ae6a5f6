class Api::ProductsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('product', 'index')
  end

  def group_by_category?
    index?
  end

  def inventories_processing_status?
    index?
  end

  def inventories_quantities?
    index?
  end

  def multibrand?
    index?
  end

  def generate_barcode?
    index?
  end

  def dashboard_product_out_of_stock?
    index?
  end

  def show?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Product) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('product', 'show', locations)
  end

  def create?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Product) ? record.owner_location : user.available_locations

    check_permission_available_locations_without_default_location('product', 'create', locations)
  end

  def upload_url?
    create?
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    if record.owner_location_id_changed?
      existing_owner_location_id = record.owner_location_id_was
      existing_owner_location = user.selected_brand.locations.find(existing_owner_location_id)

      check_permission_available_locations_without_default_location('product', 'update', existing_owner_location) &&
        check_permission_available_locations_without_default_location('product', 'update', record.owner_location)
    else
      check_permission_available_locations_without_default_location('product', 'update', record.owner_location)
    end
  end

  def change_smallest_unit?
    update?
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'destroy', record.owner_location)
  end

  def stock_and_price?
    show?
  end

  def stock_by_date?
    index?
  end

  def variances?
    show?
  end

  def favorite?
    update?
  end

  def unfavorite?
    update?
  end

  def favorite_ids?
    index?
  end

  def converted_unit?
    show?
  end

  def available_stock?
    show?
  end

  def stock_show?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Product) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('product', 'stock_show', locations)
  end

  def mark_available_stock?
    return false if user.nil? || record.nil? || record[:product].nil? || record[:location].nil?

    return false if record[:product].locations_products.find_by(location_id: record[:location].id).nil?

    check_permission_available_locations_without_default_location('product', 'adjust_stock_availability', record[:location])
  end

  def bulk_mark_available_stock?
    return false if user.nil? || record.nil? || record[:products].blank? || record[:location].nil?

    locations_products_count = record[:location].locations_products.where(product_id: record[:products].pluck(:id)).count
    return false if locations_products_count != record[:products].size

    check_permission_available_locations_without_default_location('product', 'adjust_stock_availability', record[:location])
  end

  def mark_procurement_availability?
    return false if user.nil? || record.nil? || record[:product].nil? || record[:location].nil?

    return false if record[:product].locations_products.find_by(location_id: record[:location].id).nil?

    check_permission_available_locations_without_default_location('product', 'mark_procurement_availability', record[:location])
  end

  def bulk_mark_procurement_availability?
    return false if user.nil? || record.nil? || record[:products].blank? || record[:location].nil?

    locations_products_count = record[:location].locations_products.where(product_id: record[:products].pluck(:id)).count
    return false if locations_products_count != record[:products].size

    check_permission_available_locations_without_default_location('product', 'mark_procurement_availability', record[:location])
  end

  def deactivate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'deactivate', record.owner_location)
  end

  def bulk_deactivate?
    return false if user.nil? || record.nil? || record[:products].blank?

    owner_locations = record[:products].map(&:owner_location)

    check_permission_available_locations_without_default_location('product', 'deactivate', owner_locations)
  end

  def toggle_auto_prompt_option_set?
    update?
  end

  def update_option_sets?
    update?
  end

  def update_internal_price_bulk?
    bulk_update_option_sets?
  end

  def sell_price_bulk?
    bulk_update_option_sets?
  end

  def update_sell_price_bulk?
    bulk_update_option_sets?
  end

  def bulk_update_option_sets?
    update?
  end

  def reactivate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'reactivate', record.owner_location)
  end

  def bulk_reactivate?
    return false if user.nil? || record.nil? || record[:products].blank?

    owner_locations = record[:products].map(&:owner_location)

    check_permission_available_locations_without_default_location('product', 'reactivate', owner_locations)
  end

  def price_create?
    return false if user.nil?

    location_ids = record.present? && record.instance_of?(Product) ? record.location_ids : user.available_locations.pluck(:id)

    locations_users = LocationsUser.joins(:access_list).where(
      user: user,
      location_id: location_ids
    ).where("location_permission @> '{\"product\":{\"set_price\": true}}'")

    locations_users.exists?
  end

  def last_price?
    return false if user.nil?

    locations = record.present? && record.instance_of?(Location) ? record : user.available_locations

    check_permission_available_locations_without_default_location('costing', 'show_cost', locations)
  end

  def price_show?
    return false if user.nil?

    location_ids = record.present? && record.instance_of?(Product) ? record.location_ids : user.available_locations.pluck(:id)

    locations_users = LocationsUser.joins(:access_list).where(
      user: user,
      location_id: location_ids
    ).where("location_permission @> '{\"product\":{\"view_price\": true}}'")

    locations_users.exists?
  end

  def price_update?
    return false if user.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'set_price', record.owner_location)
  end

  def show_recent_orders?
    return false if user.nil? || record.nil?

    location_ids = record.present? && record.instance_of?(Product) ? record.location_ids : user.available_locations.pluck(:id)

    locations_users = LocationsUser.joins(:access_list).where(
      user: user,
      location_id: location_ids
    ).where("location_permission @> '{\"order\":{\"index\": true}}'")

    if record.present? && record.instance_of?(Product)
      locations_users.exists? && (record.location_ids - locations_users.pluck(:location_id)).empty?
    else
      locations_users.exists?
    end
  end

  def product_unit_conversions?
    show?
  end

  def procurement_units?
    show?
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'history', record.owner_location)
  end

  def stock_availability?
    return false if user.nil? || record.nil? || record[:product].nil? || record[:location].nil?

    check_permission_available_locations_without_default_location('product', 'show', record[:location])
  end

  def list_stock_availability?
    return false if user.nil? || record.nil? || record[:products].blank? || record[:location].nil?

    locations_products_count = record[:location].locations_products.where(product_id: record[:products].pluck(:id)).count
    return false if locations_products_count != record[:products].size

    check_permission_available_locations_without_default_location('product', 'show', record[:location])
  end

  def locations?
    index?
  end

  def export_recipe?
    return false if user.nil?

    check_permission_available_locations('recipe', 'export')
  end
end
