# rubocop:disable Metrics/ClassLength
module Api
  class ProductsController < Api::BaseController
    include Restaurant::Modules::Report::ProcessedReportMessageable

    before_action :check_filtered_product, only: %i[show show_recent_orders stock_and_price
                                                    change_option_set_order update_option_sets
                                                    update deactivate reactivate destroy history toggle_auto_prompt_option_set change_smallest_unit
                                                    state_acknowledgement locations]
    before_action :check_filtered_location, only: %i[stock_and_price group_by_category last_price state_acknowledgement
                                                     bulk_mark_available_stock]

    def index
      query = if request_from_pos? && Flipper.enabled?(:simple_pos_product_query)
                PosProductQuery.new(product_filter_params.merge({ current_user: current_user }))
              else
                ProductQuery.new(product_filter_params.merge({ current_user: current_user }))
              end

      query_result = query.filter
      # deprecated
      @is_group_category = product_filter_params[:group_category].eql?('true')
      @product_categories = query_result[:data] if @is_group_category
      @products = query_result[:data] unless @is_group_category

      @paging = generate_prev_next_page(query_result[:paging])
    end

    def backoffice_index
      params = product_filter_params.merge(
        {
          current_user: current_user,
          only_active_locations: 'true' # flag to filter only active locations
        }
      )
      query = Restaurant::Queries::BackofficeProductQuery.new(params)

      query_result = query.filter
      # deprecated
      @is_group_category = product_filter_params[:group_category].eql?('true')
      @product_categories = query_result[:data] if @is_group_category
      @products = query_result[:data] unless @is_group_category

      @paging = generate_prev_next_page(query_result[:paging])
    end

    # Used when want to do multibrand procurement.
    # FE will call this instead the index action.
    def multibrand
      authorize nil, policy_class: Api::ProductsPolicy

      result = Restaurant::Services::Product::MultibrandSearchGenerator
               .new(product_filter_params, current_user, current_brand)
               .call

      query_result = result.query_result

      @is_group_category = product_filter_params[:group_category].eql?('true')
      @product_categories = query_result[:data] if @is_group_category
      @products = query_result[:data] unless @is_group_category
      @sell_unit_pair = result.sell_unit_pair

      @paging = generate_prev_next_page(query_result[:paging])
    end

    def generate_barcode
      product_ids = params[:product_ids]
      products =  current_brand.products.where(id: product_ids).order(:name)
      time = Time.zone.now.in_time_zone(current_brand.timezone)
      filename = "product_barcode_#{Time.zone.now.in_time_zone(current_brand.timezone)}.pdf"

      return send_data PdfGenerators::Products::ProductBarcode.new(
        products: products, brand: current_brand, user: current_user, time: time
      ).build.render, filename: filename, type: 'application/pdf', disposition: :attachment
    end

    def export_menu
      include_option_set = (params[:include_option_set] || 'false').eql?('true')
      is_select_all_location = (params[:is_select_all_location] || 'false').eql?('true')
      respond_to do |format|
        format.json do
          render json: {}, status: :no_content
        end
        format.xlsx do
          check_email_and_perform_export_sell_price_asynchronously(Restaurant::Constants::EXCEL_REPORT, is_select_all_location, include_option_set)
        end
        format.csv do
          check_email_and_perform_export_sell_price_asynchronously(Restaurant::Constants::CSV_REPORT, is_select_all_location, include_option_set)
        end
      end
    end

    def export_buy_price
      respond_to do |format|
        format.xlsx do
          export_buy_price_check_and_send_to_email(Restaurant::Constants::EXCEL_REPORT)
        end
        format.csv do
          export_buy_price_check_and_send_to_email(Restaurant::Constants::CSV_REPORT)
        end
      end
    end

    def export_buy_price_check_and_send_to_email(report_format)
      Restaurant::Services::Report::Products::ExportBuyPriceSendToWorker
        .new(current_user, current_brand, filtered_location, report_format).call!

      render json: { message: report_will_be_processed(current_user) }, status: :ok
    end

    def check_email_and_perform_export_sell_price_asynchronously(report_format, is_select_all_location, include_option_set)
      Restaurant::Services::Report::Products::ExportSellPriceSendToWorker
        .new(current_user, current_brand, is_select_all_location, filtered_location, report_format, include_option_set).call!

      render json: { message: report_will_be_processed(current_user) }, status: :ok
    end

    def group_by_category
      query = ProductQuery.new(product_group_by_category_filter_params.merge({ current_user: current_user }))

      render json: { data: query.query_product_category_count }, status: :ok
    end

    def stock_and_price
      product_unit = (ProductUnit.where(brand_id: current_brand.id, id: params[:product_unit_id]) if params[:product_unit_id].present?)

      data = {
        system_stock: filtered_product.stock(location_id: filtered_location.id, unit_id: filtered_product.back_office_unit_id),
        stock_unit: { id: filtered_product.back_office_unit_id, name: filtered_product.back_office_unit.name },
        internal_price: filtered_product.internal_price(filtered_location.id, product_unit&.id)
      }

      render json: { data: data }, status: :ok
    end

    def update_internal_price_bulk
      product_ids = update_internal_price_params[:products]&.map { |x| x[:id].to_i }
      filtered_products = ProductQuery.find_filtered_products(current_user: current_user, product_ids: product_ids)

      render json: {}, status: :no_content and return if filtered_products.blank?

      bulk_update_internal_price_log = current_brand.bulk_update_internal_price_logs.create!(
        user: current_user, product_ids: filtered_products.ids,
        update_internal_price_params: update_internal_price_params.as_json,
        is_immediate: params[:is_immediate] == 'true',
        apply_to_pending_orders: params[:apply_to_pending_orders] == 'true',
        metadata: { products: [], order_ids: [] }
      )

      case Settings.searchkick_mode
      when :inline
        Restaurant::Jobs::Product::BulkUpdateInternalPriceJob
          .perform_now(brand_id: current_brand.id, log_id: bulk_update_internal_price_log.id)
      when :async
        Restaurant::Jobs::Product::BulkUpdateInternalPriceJob
          .perform_later(brand_id: current_brand.id, log_id: bulk_update_internal_price_log.id)
      end

      render json: {}, status: :no_content
    end

    def change_smallest_unit
      authorize filtered_product, :change_smallest_unit?, policy_class: Api::ProductsPolicy
      product_unit = current_brand.product_units.find_by(id: change_smallest_unit_params[:product_unit_id])

      raise ::Errors::InvalidParamsError, I18n.t('product.errors.product_unit_not_present') if product_unit.nil?

      raise ::Errors::InvalidParamsError, I18n.t('product.errors.converter_qty_not_present') if change_smallest_unit_params[:converted_qty].to_d.zero?

      # filtered_product.change_smallest_unit(
      #   new_product_unit: product_unit,
      #   new_convert_ratio: change_smallest_unit_params[:converted_qty],
      #   new_conversion_internal_price: change_smallest_unit_params[:internal_price]
      # )
      render json: {}, status: :no_content
    end

    # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/BlockLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    def sell_price_bulk
      product_ids = params[:product_ids]
      order_type_ids = params[:order_type_ids] ? params[:order_type_ids].split(',').map(&:to_i) : []
      location = params[:location_id] ? current_brand.locations.find_by(id: params[:location_id]) : nil
      page = (params[:page] || 1).to_i
      item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i

      query = ProductQuery.new({ current_user: current_user,
                                 ids: product_ids,
                                 location_id: location&.id,
                                 exclude_product_with_variances: 'true',
                                 skip_child_variances: 'false',
                                 page: page,
                                 item_per_page: item_per_page,
                                 sell_to_customer_type: true,
                                 export_raw: true })

      @data = []
      filtered_products = query.filter
      Product.includes(%i[owner_location sell_unit tax
                          product_price_per_order_types]).where(id: filtered_products[:data].pluck(:id))
             .order(:name).each do |product|
        authorize product, :price_update?, policy_class: Api::ProductsPolicy
        response_product = { id: product.id, sku: product.sku, name: product.name, sell_price: product.sell_price(location&.id),
                             sell_unit: { id: product.sell_unit_id, name: product.sell_unit&.name },
                             sell_tax: { id: product.sell_tax&.id, name: product.sell_tax&.name },
                             sell_tax_setting: product.sell_tax_setting,
                             sell_to_customer_type: true }
        product_price_per_order_types = current_brand.order_types.where(id: order_type_ids).map do |order_type|
          product_price_order = product.product_price_per_order_types.find do |price_order|
            price_order.location_id.nil? && price_order.order_type_id == order_type.id
          end

          if location.present?
            location_product_price_order = product.product_price_per_order_types.find do |price_order|
              price_order.location_id == location&.id && price_order.order_type_id == order_type.id
            end
          end

          {
            order_type: { id: order_type.id, name: order_type.name },
            location_id: location_product_price_order&.location_id || product_price_order&.location_id,
            id: location_product_price_order&.id || product_price_order&.id,
            sell_price: location_product_price_order&.sell_price || product_price_order&.sell_price,
            sell_tax: { id: location_product_price_order&.sell_tax_id || product_price_order&.sell_tax_id,
                        name: location_product_price_order&.sell_tax&.name || product_price_order&.sell_tax&.name },
            sell_tax_setting: location_product_price_order&.sell_tax_setting || product_price_order&.sell_tax_setting
          }
        end

        setting = ProductSettingLocation.find_by(location_id: location&.id, product_id: product.id)
        product_setting_location = if setting.present?
                                     {
                                       id: setting.id,
                                       product_id: setting.product_id,
                                       sell_price: setting&.sell_price,
                                       sell_tax: setting&.sell_tax,
                                       sell_tax_setting: setting&.sell_tax_setting
                                     }
                                   end

        response_product = response_product.merge({ product_price_per_order_types: product_price_per_order_types,
                                                    product_setting_location: product_setting_location })
        @data << response_product
      end

      render json: { data: @data }, status: :ok
    end
    # rubocop:enable Metrics/AbcSize, Metrics/MethodLength, Metrics/BlockLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

    def update_sell_price_bulk
      product_ids = update_sell_price_params[:products]&.map { |x| x[:id].to_i }
      filtered_products = ProductQuery.find_filtered_products(current_user: current_user, product_ids: product_ids)

      ActiveRecord::Base.transaction do
        filtered_products.includes([
                                     :owner_location, :back_office_unit, :product_unit, :brand, :locations, :locations_products, :tax, :sell_unit,
                                     :recipe, :outlet_to_outlet_procurement_units, :vendor_procurement_units, :last_updated_by, :variances,
                                     :variance_detail, { product_category: :product_category_group }, :product_setting_locations,
                                     :product_unit_conversions, :central_kitchen_procurement_units
                                   ]).each do |product|
          authorize product, :price_update?, policy_class: Api::ProductsPolicy
          current_param = update_sell_price_params[:products].select { |x| x[:id].to_i == product.id }.first
          product.assign_attributes(current_param.merge({ last_updated_by: current_user }))
          product.save!
        end
      end

      render json: {}, status: :no_content
    end

    def update_sell_tax_bulk
      location_ids = update_sell_tax_params[:location_ids].presence || []
      locations = current_user.available_locations.includes([:products]).where(id: location_ids)

      ActiveRecord::Base.transaction do
        if locations.count.zero?
          product_list = current_brand.products.select { |product| product.sell_to_customer_type == true }
          Product.update_sell_tax(current_brand, product_list, nil, update_sell_tax_params[:order_types])
        else
          locations.each do |location|
            product_list = location.products.select { |product| product.sell_to_customer_type(location.id) == true }
            Product.update_sell_tax(current_brand, product_list, location.id, update_sell_tax_params[:order_types])
          end
        end
      end

      render json: {}, status: :no_content
    end

    def show
      authorize filtered_product, policy_class: Api::ProductsPolicy

      @product = ProductQuery.new(show_params.merge({ current_user: current_user })).generate_filter_response(filtered_product)

      @product[:can_edit_base_unit] = filtered_product.validate_api_show_can_edit_base_unit

      @product[:has_variances] = filtered_product.variances.exists?

      @product[:can_reset_locations_products] = filtered_product.can_reset_locations_products?(current_user)

      @product[:can_toggle_order_price_editing_by_franchisor] = filtered_product.can_toggle_order_price_editing_by_franchisor?
    end

    def stock_by_date
      authorize filtered_product, policy_class: Api::ProductsPolicy

      storage_section_id = params.key?(:storage_section_id) ? params[:storage_section_id] : :any
      early_date = params[:early_date] == 'null' ? nil : params[:early_date]

      result = Restaurant::Services::Product::StockByDateService
               .new(params[:product_ids].to_s.split(','), params[:id], params[:date].to_date, early_date, storage_section_id)
               .call

      render json: result, status: :ok
    end

    def show_recent_orders
      authorize filtered_product, policy_class: Api::ProductsPolicy
      @orders = filtered_product.recent_orders
    end

    def variances
      authorize filtered_product, policy_class: Api::ProductsPolicy
      @variances = filtered_product.variances.map do |x|
        ProductQuery.new({ current_user: current_user, presentation: 'variance' })
                    .generate_filter_response(x)
      end
    end

    def last_price
      authorize filtered_location, policy_class: Api::ProductsPolicy

      data = Restaurant::Services::Product::BulkEstimateCostMultiLocationsService
             .new(
               brand: current_brand,
               location_ids: [filtered_location.id],
               date: Time.zone.now,
               include_real_cost: false,
               products: last_price_params
             )
             .call!
      new_result = data.map do |product_id, row|
        {
          id: product_id,
          price: row[filtered_location.id]
        }
      end

      render json: { data: new_result }, status: :ok
    end

    # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
    def update
      draft_product = filtered_product
      validate_procurement_units_params!(procurement_units_params, 'procurement_units_attributes')
      validate_procurement_units_params!(outlet_to_outlet_procurement_units_params, 'outlet_to_outlet_procurement_units_attributes')
      validate_procurement_units_params!(vendor_procurement_units_params, 'vendor_procurement_units_attributes')
      validate_procurement_units_params!(central_kitchen_procurement_units_params, 'central_kitchen_procurement_units_attributes')

      update_product_params = product_params
      update_product_params = update_product_params.except('internal_price') unless Api::ProductsPolicy.new(current_user,
                                                                                                            filtered_product).price_update?

      if empty_location_params?(update_product_params)
        render json: { message: I18n.t('activerecord.errors.models.product.attributes.locations.blank') }, status: :unprocessable_entity
        return
      end

      # add locations ids that the user dont have permission to
      if update_product_params[:assigned_location_ids].present?
        update_product_params[:assigned_location_ids] += draft_product.location_ids - current_user.available_locations.pluck(:id)
        update_product_params[:assigned_location_ids] = update_product_params[:assigned_location_ids].uniq
      end

      if update_product_params[:location_ids].present?
        update_product_params[:location_ids] += draft_product.location_ids - current_user.available_locations.pluck(:id)
        update_product_params[:location_ids] = update_product_params[:location_ids].uniq
      end

      # this need to be remove, once only accept locations_products
      clear_product_duplicate_params!(update_product_params)
      ActiveRecord::Base.transaction do
        if outlet_to_outlet_procurement_units_params.to_h[:outlet_to_outlet_procurement_units_attributes].blank?
          draft_product.destroy_outlet_to_outlet_procurement_units
        end

        if central_kitchen_procurement_units_params.to_h[:central_kitchen_procurement_units_attributes].blank?
          draft_product.destroy_central_kitchen_procurement_units
        end

        draft_product.destroy_vendor_procurement_units if vendor_procurement_units_params.to_h[:vendor_procurement_units_attributes].blank?

        draft_product.assign_attributes(update_product_params.merge(unit_conversions_params.to_h)
                                                             .merge(procurement_units_params.to_h)
                                                             .merge(outlet_to_outlet_procurement_units_params.to_h)
                                                             .merge(central_kitchen_procurement_units_params.to_h)
                                                             .merge(vendor_procurement_units_params.to_h)
                                                             .merge({ last_updated_by: current_user }))

        authorize draft_product, policy_class: Api::ProductsPolicy
        draft_product.valid?

        conversion_error = draft_product.validate_product_unit_conversions_destruction
        draft_product.errors.add(:product_unit, conversion_error) if conversion_error.present?

        if draft_product.errors.any?
          render json: { errors: ApplicationHelper.format_errors(draft_product.errors) }, status: :unprocessable_entity
          return
        end

        draft_product.prevent_locations_products_multiple_reindex
        draft_product.save
      end

      @product = draft_product.attributes
      @product[:locations] = draft_product.locations
    end
    # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

    def create
      create_product_params = product_params.merge(
        status: 'activated',
        brand_id: current_brand.id
      )
      validate_procurement_units_params!(procurement_units_params, 'procurement_units_attributes')
      validate_procurement_units_params!(outlet_to_outlet_procurement_units_params, 'outlet_to_outlet_procurement_units_attributes')
      validate_procurement_units_params!(vendor_procurement_units_params, 'vendor_procurement_units_attributes')
      validate_procurement_units_params!(central_kitchen_procurement_units_params, 'central_kitchen_procurement_units_attributes')
      create_product_params = create_product_params.except('internal_price') unless Api::ProductsPolicy.new(current_user, nil).price_create?
      draft_product = Product.new(create_product_params.merge(unit_conversions_params.to_h)
                                                       .merge(procurement_units_params.to_h)
                                                       .merge(outlet_to_outlet_procurement_units_params.to_h)
                                                       .merge(vendor_procurement_units_params.to_h)
                                                       .merge(central_kitchen_procurement_units_params.to_h)
                                                       .merge({ created_by: current_user,
                                                                last_updated_by: current_user }))
      authorize draft_product, policy_class: Api::ProductsPolicy

      draft_product.valid?
      if draft_product.errors.any?
        render json: { errors: ApplicationHelper.format_errors(draft_product.errors) }, status: :unprocessable_entity
        return
      end

      draft_product.save!

      @product = draft_product.attributes
      @product[:locations] = draft_product.locations

      render 'api/products/create', status: :created
    end

    def upload_url
      upload_params = presigned_url_params.merge({
                                                   file_path: 'product-image-assets',
                                                   file_key: "#{SecureRandom.uuid}-#{presigned_url_params[:filename]}",
                                                   acl: 'public-read',
                                                   supported_content_types: FileHelper::SUPPORTED_CONTENT_TYPES_TO_PNG
                                                 })

      data = FileHelper.upload_url(upload_params)

      render json: data, status: :ok
    end

    def update_option_sets
      authorize filtered_product, policy_class: Api::ProductsPolicy

      previous_option_sets = filtered_product.product_option_sets.map(&:option_set)
      ActiveRecord::Base.transaction do
        draft_product = filtered_product
        draft_product.assign_attributes(product_option_set_params.merge({ last_updated_by: current_user }))
        draft_product.valid?

        if draft_product.errors.any?
          render json: { errors: ApplicationHelper.format_errors(draft_product.errors) }, status: :unprocessable_entity
          return
        end

        draft_product.save!

        Products::Services::ProductAsOptionSetOptionUpdaterService.new(draft_product).call
      end

      response = {
        product_option_sets: filtered_product.product_option_sets.as_json(include: { option_set: { include: :option_set_options } })
      }
      trigger_check_options_availability_job(previous_option_sets)

      render json: response, status: :ok
    end

    def bulk_update_option_sets
      if product_option_set_params[:product_option_sets_attributes].blank?
        raise Errors::InvalidParamsError,
              I18n.t('option_sets.errors.empty_product_option_sets')
      end

      product_ids = product_option_set_params[:product_option_sets_attributes].map { |x| x[:product_id] }
      filtered_products = ProductQuery.find_filtered_products(current_user: current_user, product_ids: product_ids)

      ActiveRecord::Base.transaction do
        filtered_products.each do |product|
          authorize product, policy_class: Api::ProductsPolicy
          current_param = product_option_set_params[:product_option_sets_attributes].select { |x| x[:product_id] == product.id }
          draft_product = product
          draft_product.assign_attributes({
                                            product_option_sets_attributes: current_param,
                                            last_updated_by: current_user
                                          })
          draft_product.save!
        end
      end
    end

    def change_option_set_order
      authorize filtered_product, policy_class: Api::ProductsPolicy
      product_option_set = filtered_product.product_option_sets.find_by(option_set_id: params[:option_set_id])
      product_option_set.update_sequence(params[:sequence])
    end

    def favorite
      data = { product: filtered_product, location: filtered_location }
      authorize data[:product], policy_class: Api::ProductsPolicy
      filtered_product.favorite(filtered_location)
    end

    def unfavorite
      data = { product: filtered_product, location: filtered_location }
      authorize data[:product], policy_class: Api::ProductsPolicy
      filtered_product.unfavorite(filtered_location)
    end

    def favorite_ids
      product_ids = []
      filtered_location
        .locations_products
        .pos_favorited
        .select(:id, :product_id)
        .find_each(batch_size: 50) { |locations_product| product_ids << locations_product.product_id }

      render json: product_ids.to_json, status: :ok
    end

    def mark_available_stock
      data = { product: filtered_product, location: filtered_location }

      ActiveRecord::Base.transaction do
        filtered_product.lock!

        # for procurement
        allowed_mark_availability_for_procurement = Api::ProductsPolicy.new(current_user, data).mark_procurement_availability?
        if allowed_mark_availability_for_procurement && mark_available_stock_params.permit(:procurement).present?
          filtered_product.mark_available_stock(filtered_location, mark_available_stock_params,
                                                type: 'procurement')
        end

        # for sales
        allowed_mark_availability_for_sales = Api::ProductsPolicy.new(current_user, data).mark_available_stock?
        raise Errors::Unauthorized if !allowed_mark_availability_for_sales && mark_available_stock_params[:procurement].nil?

        if allowed_mark_availability_for_sales && mark_available_stock_params.except(:procurement, :id).present?
          filtered_product.mark_available_stock(filtered_location, mark_available_stock_params)
        end
      rescue ActiveRecord::RecordInvalid => e
        return render json: { errors: ApplicationHelper.format_errors(e.record.errors) }, status: :unprocessable_entity
      end
    end

    def bulk_mark_available_stock
      product_ids = params[:product_ids]
      products = ProductQuery.find_filtered_products(current_user: current_user, product_ids: product_ids, product_includes: [
                                                       :option_set_options, { locations_products: :location }
                                                     ])

      data = { products: products, location: filtered_location }
      validate_bulk_mark_available_stock(mark_available_stock_params, products)

      allowed_bulk_mark_availability_for_sales = Api::ProductsPolicy.new(current_user, data).bulk_mark_available_stock?
      raise Errors::Unauthorized if !allowed_bulk_mark_availability_for_sales && mark_available_stock_params[:procurement].nil?

      allowed_bulk_mark_availability_for_procurement = mark_available_stock_params.permit(:procurement).present? &&
                                                       Api::ProductsPolicy.new(current_user, data).bulk_mark_procurement_availability?

      ActiveRecord::Base.transaction do
        products.each do |product|
          product.lock!
          if allowed_bulk_mark_availability_for_procurement
            product.mark_available_stock(filtered_location, mark_available_stock_params, type: 'procurement')
          end
          if allowed_bulk_mark_availability_for_sales
            product.mark_available_stock(filtered_location, mark_available_stock_params.except(:procurement), is_bulk_action: true)
          end
        end
      rescue ActiveRecord::RecordInvalid => e
        return render json: { errors: ApplicationHelper.format_errors(e.record.errors) }, status: :unprocessable_entity
      end

      Products::Services::BulkUpdateItemIntegrationStatusService.new(products, filtered_location, mark_available_stock_params).call

      render json: nil, status: :no_content
    end

    def deactivate
      authorize filtered_product, policy_class: Api::ProductsPolicy
      filtered_product.deactivate!(current_user)
    end

    def bulk_deactivate
      product_ids = params[:product_ids]
      products = ProductQuery.find_filtered_products(current_user: current_user,
                                                     product_ids: product_ids,
                                                     product_includes: %i[owner_location])

      data = { products: products, location: filtered_location }

      raise Errors::Runchise::NoAccessToLocationAtAll unless Api::ProductsPolicy.new(current_user, data).bulk_deactivate?

      import_record = build_import_data('BulkDeactivateProduct', product_ids)

      Restaurant::Jobs::Product::BulkActivationJob.perform_later(brand_id: current_brand.id,
                                                                 user_id: current_user.id,
                                                                 activate: false,
                                                                 import_record_id: import_record.id)

      render json: nil, status: :no_content
    end

    def reactivate
      authorize filtered_product, policy_class: Api::ProductsPolicy
      filtered_product.reactivate!(user: current_user)
    end

    def bulk_reactivate
      product_ids = params[:product_ids]
      products = ProductQuery.find_filtered_products(current_user: current_user,
                                                     product_ids: product_ids,
                                                     product_includes: %i[owner_location])

      data = { products: products, location: filtered_location }

      raise Errors::Runchise::NoAccessToLocationAtAll unless Api::ProductsPolicy.new(current_user, data).bulk_reactivate?

      import_record = build_import_data('BulkReactivateProduct', product_ids)

      Restaurant::Jobs::Product::BulkActivationJob.perform_later(brand_id: current_brand.id,
                                                                 user_id: current_user.id,
                                                                 activate: true,
                                                                 import_record_id: import_record.id)

      render json: nil, status: :no_content
    end

    def toggle_auto_prompt_option_set
      authorize filtered_product, policy_class: Api::ProductsPolicy
      filtered_product.update!(option_set_auto_prompt: !filtered_product.option_set_auto_prompt, last_updated_by: current_user)
    end

    def destroy
      authorize filtered_product, policy_class: Api::ProductsPolicy
      return render json: { message: I18n.t('products.delete_error') }, status: :unprocessable_entity unless filtered_product.valid_for_destroy?

      filtered_product.destroy!
    end

    def history
      authorize filtered_product, policy_class: Api::ProductsPolicy
      preset_audits = filtered_product.own_and_associated_audits.reorder(created_at: :desc)
      first_request_uuid = filtered_product.audits.order(:id).first.try(:request_uuid)
      preset_audits = preset_audits.includes(%i[associated user]).reject { |x| x.associated.present? && x.request_uuid == first_request_uuid }
      @audits = generate_audit_response(preset_audits)

      render json: { audits: @audits }, status: :ok
    end

    def converted_unit
      authorize filtered_product, policy_class: Api::ProductsPolicy
      authorize filtered_product, :stock_show?, policy_class: Api::ProductsPolicy

      raise Errors::InvalidParamsError, I18n.t('activerecord.errors.unit_not_in_procurement_unit') if filtered_product_unit.blank?

      no_stock = filtered_product.no_stock?

      par_qty = no_stock ? nil : filtered_product.par_quantity_in_unit(filtered_location.id, filtered_product_unit[:id]).to_d
      stock_in_hand_qty = if no_stock
                            nil
                          else
                            filtered_product.stock(location_id: filtered_location.id,
                                                   unit_id: filtered_product_unit[:id]).to_d
                          end

      render json: { par: { qty: par_qty }, stock_in_hand: { qty: stock_in_hand_qty } }, status: :ok
    end

    def available_stock
      authorize filtered_product, policy_class: Api::ProductsPolicy
      authorize filtered_product, :stock_show?, policy_class: Api::ProductsPolicy

      raise Errors::InvalidParamsError, I18n.t('activerecord.errors.unit_not_in_procurement_unit') if filtered_product_unit.blank?

      no_stock = filtered_product.no_stock?

      available_stock_qty = if no_stock
                              nil
                            else
                              calculate_available_stock.floor(2).to_d
                            end

      render json: { available_stock: { qty: available_stock_qty } }, status: :ok
    end

    def export_recipe
      respond_to do |format|
        format.xlsx do
          export_recipe_check_and_send_to_email(Restaurant::Constants::EXCEL_REPORT)
        end
        format.csv do
          export_recipe_check_and_send_to_email(Restaurant::Constants::CSV_REPORT)
        end
      end
    end

    def export_recipe_check_and_send_to_email(report_format)
      Restaurant::Services::Report::Products::ExportRecipeSendToWorker
        .new(current_user, current_brand, filtered_location, params[:is_select_all_location], report_format).call!

      render json: { message: report_will_be_processed(current_user) }, status: :ok
    end

    def inventories_processing_status
      result = ::Restaurant::Services::Product::InventoriesProcessingIdsGenerator
               .new(current_brand, params[:products_ids], params[:location_id])
               .call

      render json: { inventory_processing_products_ids: result }, status: :ok
    end

    def inventories_quantities
      authorize filtered_location, :stock_show?, policy_class: Api::ProductsPolicy
      product_ids = Product.where(id: params[:products_ids].to_s.split(',').map(&:to_i)).ids
      result = ::Restaurant::Services::Product::InventoriesQuantitiesGenerator
               .new(current_brand, product_ids, filtered_location, params[:by_section])
               .call

      render json: result, status: :ok
    end

    # for acknowledgement push notifications
    def state_acknowledgement
      device = filtered_location.devices.find_by(id: push_notification_ack_params[:device_id])
      raise ::Errors::InvalidParamsError, I18n.t('products.errors.device_not_found') if device.blank?

      unless push_notification_ack_params[:state].in?(Products::Constants::PUSH_NOTIFICATION_STATES)
        raise ::Errors::InvalidParamsError, I18n.t('products.errors.invalid_push_notif_state')
      end

      locations_product = filtered_product.locations_products.find_by(location_id: params[:location_id])
      raise ::Errors::InvalidParamsError, I18n.t('general.error_404') if locations_product.blank?

      push_notif_ack_present = locations_product.push_notification_acknowledgements.where(
        device_id: device.id,
        unique_id: push_notification_ack_params[:unique_id],
        state: push_notification_ack_params[:state]
      ).exists?

      unless push_notif_ack_present
        locations_product.push_notification_acknowledgements.create!(
          locations_product_id: locations_product.id,
          device_id: device.id,
          unique_id: push_notification_ack_params[:unique_id],
          state: push_notification_ack_params[:state],
          metadata: {
            available_stock_flag: push_notification_ack_params[:available_stock_flag]
          }
        )
      end
      render json: nil, status: :created
    end

    def stock_availability
      product = ProductQuery.find_filtered_products(current_user: current_user, product_ids: [params[:id]], product_includes: [
                                                      :product_setting_locations, { locations_products: :location }
                                                    ]).first
      data = { product: product, location: filtered_location }
      authorize data, policy_class: Api::ProductsPolicy

      stock_availability = product.available_stock_flag(filtered_location)

      render json: { stock_availability: stock_availability }, status: :ok
    end

    def list_stock_availability
      product_ids = params[:product_ids].split(',')
      products = ProductQuery.find_filtered_products(current_user: current_user, product_ids: product_ids, product_includes: [
                                                       :product_setting_locations, { locations_products: :location }
                                                     ])

      data = { products: products, location: filtered_location }
      authorize data, policy_class: Api::ProductsPolicy

      result = products.map do |product|
        {
          id: product.id
        }.merge(product.available_stock_flag(filtered_location))
      end

      render json: { products: result }, status: :ok
    end

    def locations
      location_query = LocationQuery.new(locations_params.merge({ current_user: current_user,
                                                                  presentation: 'compact',
                                                                  ids: filtered_product.locations.map(&:id).join(',') }))
                                    .filter

      render json: { locations: location_query[:data], paging: location_query[:paging] }, status: :ok
    end

    def dashboard_product_out_of_stock
      result = ::Products::Queries::DashboardProductOutOfStockQuery.new(current_user, dashboard_product_out_of_stock_params).filter

      render json: result, status: :ok
    end

    private

    def build_import_data(action, product_ids)
      import_record = ImportData.new(
        associated_type: action,
        status: 'processing',
        user_id: current_user.id,
        brand_id: current_brand.id,
        total_count: 0,
        created_by: current_user,
        last_updated_by: current_user,
        payload: { product_ids: product_ids }
      )

      import_record.save!
      import_record
    end

    def calculate_available_stock
      location_id = params[:location_id]
      open_qty = Restaurant::Services::Report::ProductStock::OpenQuantityHashMapGenerator
                 .new(product_ids: [filtered_product.id], location_ids: [location_id], brand: current_brand)
                 .call!
      dict_key = "#{filtered_product.id}_#{location_id}"
      open_qty = open_qty[dict_key].to_d

      deactivated_storage_section_ids = StorageSection.where(location_id: location_id, status: 'deactivated').map(&:id)
      available_stock = filtered_product.stock(location_id: location_id,
                                               unit_id: filtered_product.product_unit_id,
                                               exclude_storage_section_ids: deactivated_storage_section_ids) - open_qty

      filtered_product.convert_quantity(filtered_product.product_unit_id, filtered_product_unit[:id], available_stock)
    end

    def clear_product_duplicate_params!(params)
      Restaurant::Services::DuplicateParamsCleaner
        .new(params, 'product_price_per_order_types_attributes', ['location_id', 'order_type_id'])
        .call!
    end

    def validate_procurement_units_params!(procurement_units_params, attributes)
      procurement_units_params_hash = procurement_units_params.to_h
      unit_conversions_params_hash = unit_conversions_params.to_h
      procurement_units_attributes = procurement_units_params_hash[attributes]
      unit_conversions_attributes = unit_conversions_params_hash['product_unit_conversions_attributes']

      return if procurement_units_attributes.blank?

      product_unit_ids_from_procurement_units_params = procurement_units_attributes
                                                       .filter { |attr| attr['_destroy'] != true }
                                                       .map { |attr| attr['product_unit_id'] }
      valid_product_unit_ids_for_procurement_units = []
      if unit_conversions_attributes.present?
        valid_product_unit_ids_for_procurement_units << unit_conversions_attributes
                                                        .filter { |unit_conversion_attribute| unit_conversion_attribute['_destroy'] != true }
                                                        .map do |attr|
          attr['product_unit_id']
        end
      end
      valid_product_unit_ids_for_procurement_units << product_params[:product_unit_id]

      invalid_product_unit_ids = (product_unit_ids_from_procurement_units_params - valid_product_unit_ids_for_procurement_units.flatten)
      invalid_product_unit_names = ProductUnit.where(id: invalid_product_unit_ids).pluck(:name).join(', ')

      if invalid_product_unit_ids.present?
        raise  Errors::InvalidParamsError,
               I18n.t('activerecord.errors.procurement_unit_not_present_in_unit_conversion_or_smallest_unit', unit_names: invalid_product_unit_names)
      end
    end

    def validate_bulk_mark_available_stock(mark_available_stock_params, products)
      if !mark_available_stock_params[:pos].nil? && products.any? { |product| !product.sell_to_pos }
        raise Errors::InvalidParamsError, I18n.t('products.errors.selected_products_available_in_platform', channel: 'POS')
      end

      if !mark_available_stock_params[:online_ordering].nil? && products.any? { |product| !product.sell_to_online_ordering }
        raise Errors::InvalidParamsError, I18n.t('products.errors.selected_products_available_in_platform', channel: 'Online Ordering')
      end

      if !mark_available_stock_params[:go_food].nil? && products.any? { |product| !product.sell_to_go_food }
        raise Errors::InvalidParamsError, I18n.t('products.errors.selected_products_available_in_platform', channel: 'Go Food')
      end

      if !mark_available_stock_params[:grab_food].nil? && products.any? { |product| !product.sell_to_grab_food }
        raise Errors::InvalidParamsError, I18n.t('products.errors.selected_products_available_in_platform', channel: 'Grab Food')
      end

      if !mark_available_stock_params[:shopee_food].nil? && products.any? { |product| !product.sell_to_shopee_food }
        raise Errors::InvalidParamsError, I18n.t('products.errors.selected_products_available_in_platform', channel: 'Shopee Food')
      end
    end

    def check_filtered_product
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_product.nil?
    end

    def available_locations
      current_user.available_locations.where(status: 'activated')
    end

    def filtered_product
      @filtered_product ||= ProductQuery.find_filtered_products(current_user: current_user, product_ids: [params[:id]]).first
    end

    def filtered_product_unit
      @filtered_product_unit ||= filtered_product.valid_procurement_units(true, true).detect do |procurement_unit|
        procurement_unit[:id] == params[:unit_id].to_i
      end
    end

    def check_filtered_location
      return if params['skip_location'] == 'true'

      if filtered_location.nil? && !(params[:is_select_all_location] || 'false').eql?('true')
        return render json: { message: I18n.t('general.error_404') },
                      status: :not_found
      end
    end

    def trigger_check_options_availability_job(previous_option_sets)
      previous_has_rule_minimum = previous_option_sets.any? { |option_set| option_set.rule_minimum.to_i.positive? }
      current_option_sets = filtered_product.product_option_sets.map(&:option_set)
      current_has_rule_minimum = current_option_sets.any? { |option_set| option_set.rule_minimum.to_i.positive? }

      return if !previous_has_rule_minimum && !current_has_rule_minimum

      Restaurant::Jobs::Product::CheckOptionsAvailabilityJob.perform_later(filtered_product.id)
    end

    def filtered_location
      @filtered_location ||= current_user.available_locations.find_by(id: params[:location_id])
    end

    def filtered_location_to
      @filtered_location_to ||= current_user.available_locations.find_by(id: params[:location_to_id])
    end

    def empty_location_params?(used_params)
      used_params[:is_select_all_location].to_s != 'true' &&
        used_params[:assigned_location_ids].blank? &&
        used_params[:location_ids].blank? &&
        used_params[:is_select_all_location_group].to_s != 'true' &&
        used_params[:location_group_ids].blank?
    end

    def presigned_url_params
      params.require(:presigned_url).permit(:filename, :content_type, :content_length)
    end

    def product_params
      params
        .require(:product)
        .permit(:id, :name, :sku, :upc, :description, :internal_price, :sell_price, :product_category_id,
                :product_unit_id, :back_office_unit_id, :is_select_all_location, :location_type, :image_url,
                :internal_distribution_type, :external_vendor_type, :modifier, :print_category_id,
                :internal_produce_type, :sell_to_customer_type, :allow_custom_sell_price,
                :par_quantity, :par_unit_id, :sell_unit_id, :tax_id, :sell_tax_setting, :internal_tax_id,
                :shelf_life_type, :shelf_life, :no_stock, :owner_location_id, :sell_to_pos, :sell_to_kiosk,
                :sell_to_dine_in, :sell_to_grab_food, :sell_to_go_food, :sell_to_shopee_food, :sell_to_online_ordering,
                :sell_to_procurement_from_customer, :is_select_all_location_group, :central_kitchen_back_office_unit_id,
                :outlet_back_office_unit_id,
                :order_price_editing_by_franchisor,
                location_ids: [], exclude_location_ids: [], assigned_location_ids: [], product_group_ids: [],
                location_group_ids: [], exclude_location_group_ids: [],
                product_price_per_order_types_attributes: %i[id location_id order_type_id sell_price sell_tax_id sell_tax_setting _destroy],
                product_internal_price_locations_attributes: %i[id location_id product_unit_id internal_price _destroy],
                product_setting_locations_attributes: %i[id internal_distribution_type
                                                         external_vendor_type internal_produce_type sell_to_customer_type location_id
                                                         is_par_active is_product_type_active
                                                         par_quantity par_unit_id sell_price sell_tax_id sell_tax_setting _destroy
                                                         sell_to_dine_in sell_to_grab_food sell_to_go_food sell_to_shopee_food
                                                         sell_to_pos sell_to_kiosk sell_to_online_ordering sell_to_procurement_from_customer])
    end

    def dashboard_product_out_of_stock_params
      params.permit(
        :is_select_all_product, :product_ids, :exclude_product_ids,
        :item_per_page,
        :is_select_all_location, :location_id,
        :selected_date, :last_product_id, :last_product_name,
        :is_select_all_location_group, :location_group_id
      )
    end

    def unit_conversions_params
      params
        .require(:product)
        .permit(product_unit_conversions_attributes: %i[id product_unit_id internal_price converted_qty _destroy])
    end

    def procurement_units_params
      params
        .require(:product)
        .permit(procurement_units_attributes: %i[id product_unit_id sequence _destroy])
    end

    def outlet_to_outlet_procurement_units_params
      params
        .require(:product)
        .permit(outlet_to_outlet_procurement_units_attributes: %i[id product_unit_id sequence _destroy])
    end

    def vendor_procurement_units_params
      params
        .require(:product)
        .permit(vendor_procurement_units_attributes: %i[id product_unit_id sequence _destroy])
    end

    def central_kitchen_procurement_units_params
      params
        .require(:product)
        .permit(central_kitchen_procurement_units_attributes: %i[id product_unit_id sequence _destroy])
    end

    def product_option_set_params
      params
        .require(:product)
        .permit(product_option_sets_attributes: %i[id product_id option_set_id sequence _destroy])
    end

    def inventories_info_params
      params.permit(:location_id, products_ids: [])
    end

    def update_internal_price_params
      params
        .permit(products: [:id,
                           :internal_price,
                           { product_setting_locations_attributes: %i[id location_id _destroy] },
                           { product_internal_price_locations_attributes: %i[id location_id product_unit_id internal_price _destroy] },
                           { product_unit_conversions_attributes: %i[id product_unit_id internal_price] }])
    end

    def update_sell_price_params
      params
        .permit(products: [:id, :sell_price, :tax_id, :sell_tax_setting,
                           { product_price_per_order_types_attributes: %i[id location_id order_type_id
                                                                          sell_price sell_tax_id sell_tax_setting _destroy] },
                           { product_setting_locations_attributes: %i[id location_id sell_price sell_tax_id sell_tax_setting] }])
    end

    def update_sell_tax_params
      params
        .permit({ order_types: %i[id sell_tax_setting sell_tax_id] }, location_ids: [])
    end

    def change_smallest_unit_params
      params.require(:product).permit(%i[id product_unit_id converted_qty])
    end

    def last_price_params
      params.permit(products: %i[id product_unit_id])
    end

    def mark_available_stock_params
      params.permit(%i[id pos kiosk grab_food go_food shopee_food online_ordering procurement])
    end

    def product_filter_params
      raise ::Errors::InvalidParamsError, I18n.t('products.errors.keyword_length') if params[:keyword].present? && params[:keyword].length > 100

      params
        .permit(:keyword, :sort_key, :sort_order, :page, :item_per_page, :modifier, :no_stock, :vendor_id, :location_to_brand_id,
                :location_id, :location_to_id, :status, :only_contain_recipe, :recipe_status, :recipe_type, :any_external_vendor_type,
                :group_category, :include_variance, :exclude_product_with_variances, :out_of_stock_flag,
                :external_vendor_type, :internal_distribution_type, :sell_to_customer_type, :internal_produce_type, :recipe_line_product,
                :show_pos_position, :ids, :category_ids, :exclude_category_ids, :exclude_ids, :presentation, :variance_presentations,
                :sell_to_dine_in, :sell_to_grab_food, :sell_to_go_food, :sell_to_shopee_food, :sell_to_online_ordering, :sell_to_pos,
                :sell_to_procurement, :available_stock_flag_procurement, :available_stock_flag_pos, :available_stock_flag_grab_food,
                :available_stock_flag_go_food, :available_stock_flag_shopee_food, :available_stock_flag_online_ordering,
                :sell_to_procurement_from_customer, :sell_to_kiosk, :show_permissions, :show_detail_course, :location_group_id,
                :all_location_froms, :location_from_ids, :exclude_location_from_ids, :filter_by_sub_brand, :location_from_brand_id,
                :exclude_used_in_recipe_line, :skip_child_variances, :procurement_from_customer, :multibrand_category_ids,
                :promo_is_select_all_location_to_ids, :promo_location_to_type, :promo_location_to_ids, :filter_product_type_behaviour,
                :valid_for_production, :valid_for_role_and_outlet, :allow_procurement_out_of_stock_flag, :check_empty_stock,
                :is_select_all_location, :out_of_stock_flag, :include_option_set_recipes)
    end

    def product_group_by_category_filter_params
      params
        .permit(:keyword, :status, :location_id, :location_to_id, :no_stock, :exclude_product_with_variances, :any_external_vendor_type,
                :recipe_line_product, :internal_distribution_type, :external_vendor_type, :sell_to_customer_type, :exclude_ids,
                :sort_key, :sort_order, :exclude_category_ids, :vendor_id, :skip_location, :valid_for_role_and_outlet, :valid_for_production,
                :sell_to_grab_food, :sell_to_go_food, :sell_to_shopee_food, :sell_to_online_ordering, :filter_product_type_behaviour,
                :internal_produce_type, :only_contain_recipe, :recipe_type, :recipe_status, :skip_child_variances)
    end

    def show_params
      params.permit(:show_permissions)
    end

    def push_notification_ack_params
      params.permit(:device_id, :location_id, :unique_id, :state,
                    available_stock_flag: %i[pos go_food grab_food shopee_food online_ordering])
    end

    def locations_params
      params
        .permit(:keyword, :page, :item_per_page)
    end
  end
end
# rubocop:enable Metrics/ClassLength
