class Api::Import::ProductsController < Api::BaseController
  skip_before_action :authenticate_user!, only: %i[template]
  skip_before_action :check_ip!, only: %i[template]
  before_action :validate_permission, except: %i[template]

  def create
    ImportHelper.validate_csv_file(file_url: create_params[:import_file][:url])

    import_record = ImportData.new(
      associated_type: Product.name,
      status: 'processing',
      user_id: current_user.id,
      brand_id: current_brand.id,
      total_count: 0,
      created_by: current_user,
      last_updated_by: current_user,
      payload: { file_name: create_params[:import_file][:name], file_url: create_params[:import_file][:url], errors: [] },
      trigger_create_job: true
    )

    if import_record.save
      head :created
    else
      render json: { errors: ApplicationHelper.format_errors(import_record.errors.messages) }, status: :unprocessable_entity
      return
    end
  end

  def show
    data = ImportData.find_by(user_id: current_user.id, brand_id: current_brand.id, id: params[:id])

    return render json: { message: I18n.t('general.error_404') }, status: :not_found if data.nil?

    result = ImportData.build_result(data)

    if ['BulkDeactivateProduct', 'BulkReactivateProduct'].include?(data.associated_type)
      result.merge!(
        success_messages: data.payload&.dig('product_names') || []
      )
    end

    render json: { import_data: result }, status: :ok
  end

  def template
    respond_to do |format|
      format.csv do
        headers['Content-Type'] ||= 'text/csv'
      end
    end
  end

  def upload_url
    upload_params = presigned_url_params.merge({
                                                 file_path: 'products',
                                                 file_key: "#{SecureRandom.uuid}-#{presigned_url_params[:filename]}"
                                               })

    data = FileHelper.upload_url(upload_params)

    render json: data, status: :ok
  end

  private

  def presigned_url_params
    params.require(:presigned_url).permit(:filename, :content_type, :content_length)
  end

  def create_params
    params.require(:product).permit(import_file: %i[name url])
  end
end
