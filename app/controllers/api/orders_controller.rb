module Api
  # rubocop:disable Metrics/ClassLength
  class OrdersController < Api::BaseController
    include Restaurant::Modules::Notification::OrderNotifiable
    include Restaurant::Concerns::Procurement::Accessable
    include Restaurant::Modules::Procurement::Constants

    before_action :check_filtered_order,
                  only: %i[show update update_shipping_fee history show_with_delivery
                           invoice approve void payment_status open_quantity_order_lines check_validity_before_close]
    before_action :check_filtered_order_related_transaction, only: %i[related_transactions]
    before_action :set_request_variant, only: %i[show invoice online_payment_invoices]
    skip_before_action :validate_permission, only: %i[create update]

    before_action :validate_reminder_pay, only: %i[remind_payment]
    before_action :validate_pay, only: %i[pay_detail pay]
    before_action :validate_shipping_fee_pay, only: %i[shipping_fee_pay_detail shipping_fee_pay]
    before_action :validate_payment_method, only: %i[pay_detail pay shipping_fee_pay_detail shipping_fee_pay]
    before_action :validate_enable_request_delivery_date, only: %i[create update]

    rescue_from Pundit::NotAuthorizedError, with: :no_permission

    def index
      assigned_order_nature = assign_order_nature_params!

      query_result = ::PostgresOrderQueryIndex.new(
        order_filter_params.merge({
                                    current_user: current_user,
                                    order_nature: assigned_order_nature
                                  })
      ).call_and_build_index!
      @orders = query_result[:data]
      @paging = generate_prev_next_page(query_result[:paging])
    end

    def assign_order_nature_params!
      # as for now 4 Nov 2025 , incoming order can only select 1 location, outgoing can be more than 1
      location_to_ids = order_filter_params[:location_to_ids].to_s.split(',').map(&:to_i)
      if location_to_ids.size == 1
        Restaurant::Constants::INCOMING
      else
        Restaurant::Constants::OUTGOING
      end
    end

    def open_quantity_order_lines
      order_detail_lines = select_open_quantity_order_lines
      render json: { order_detail_lines: order_detail_lines }
    end

    def check_validity_before_close
      order_detail_lines = select_open_quantity_order_lines
      invalid_promos = filtered_order.display_invalid_promos_before_close

      render json: { open_quantity_order_lines: order_detail_lines, inapplicable_promos: invalid_promos }
    end

    def select_open_quantity_order_lines
      filtered_order.order_transaction_lines
                    .includes(:product, :product_unit, :delivery_transaction_lines,
                              order_transaction_line_fulfillments: :delivery_transaction_lines)
                    .select { |order_detail_line| order_detail_line.open_qty.positive? }.map do |order_detail_line|
        {
          product_name: order_detail_line.product.name,
          quantity: order_detail_line.open_qty,
          product_unit_name: order_detail_line.product_unit.name
        }
      end
    end

    def location_from
      extra_filter = {}
      if location_filter_params[:location_to_id].present? && location_filter_params[:location_to_type].present?
        case location_filter_params[:location_to_type]
        when 'Location'
          location = Location.find_by(id: location_filter_params[:location_to_id])
          extra_filter = { exclude_ids: location.id.to_s }
        when 'Vendor'
          vendor = Vendor.find_by(id: location_filter_params[:location_to_id])
          extra_filter = { allow_external_vendor: true } if vendor
        end
      end

      is_bulk_order_param = { is_bulk_order_destination: location_filter_params['is_bulk_order'] == 'true' }

      location_query = LocationQuery.new(location_filter_params.merge(extra_filter)
                                                               .merge(is_bulk_order_param)
                                                               .merge({ status: 'activated',
                                                                        current_user: current_user,
                                                                        permission: { order: { index: true } } })).filter
      @data = location_query[:data].map do |curr_location|
        LocationHelper.build_order_delivery_location(location: curr_location, type: 'Location')
      end

      @paging = location_query[:paging]
      @has_all_locations_access = all_locations_access_with_permission?
    end

    # rubocop:disable Metrics/MethodLength
    def location_to
      location = nil
      if location_filter_params[:location_from_id].present? &&
         location_filter_params[:location_from_type] == 'Location'
        location = Location.find_by(id: location_filter_params[:location_from_id])
      end

      exclude_vendor = location_filter_params[:exclude_vendor] == 'true'

      models = if exclude_vendor
                 [Location]
               else
                 [Location, Vendor]
               end

      data = []
      page = location_filter_params[:page].present? ? location_filter_params[:page].to_i : 1
      if Location.exists? || Vendor.exists?
        data = Searchkick.search location_filter_params[:keyword].presence || '*',
                                 models: models,
                                 where: where_condition_for_location_to(location),
                                 match: :word_middle,
                                 # prioritize self brand over another brand (multibrand procurement)
                                 boost_where: { brand_id: { value: current_brand.id, factor: 2 } },
                                 indices_boost: { Location => 5, Vendor => 1 }, # prioritize location over vendor
                                 page: page,
                                 per_page: if location_filter_params[:item_per_page].present?
                                             location_filter_params[:item_per_page].to_i
                                           else
                                             Settings.default_location_per_page
                                           end,
                                 order: [{ _score: :desc }, { name: :asc }]
      end
      @data = data.map do |location_or_vendor|
        LocationHelper.build_order_delivery_location(location: location_or_vendor, type: location_or_vendor.class.name)
      end

      @paging = {
        current_page: page,
        total_item: data.try(:total_count) || 0
      }
    end
    # rubocop:enable Metrics/MethodLength

    def available_delivery_location_from
      @data = Restaurant::Services::DeliveryTransaction::AvailableOrderLocationsForNewDelivery.new(
        params: params, origin_order_location: Restaurant::Constants::LOCATION_TO, current_brand: current_brand
      ).call

      render json: { data: @data }
    end

    def available_delivery_location_to
      # I think fulfillment_location_id is not used for now but the code works fine, so I will not remove it from development of RR-2603
      origin_order_location = if params[:fulfillment_location_id].present?
                                Restaurant::Constants::ORDER_TYPE_FULFILLMENT
                              else
                                Restaurant::Constants::LOCATION_FROM
                              end

      @data = Restaurant::Services::DeliveryTransaction::AvailableOrderLocationsForNewDelivery.new(
        params: params, origin_order_location: origin_order_location, current_brand: current_brand
      ).call

      render json: { data: @data }
    end

    def new
      @order_detail = OrderTransaction.new(new_params.merge({ status: order_params[:status] || 'pending',
                                                              order_date: Time.zone.now.strftime('%d/%m/%Y'),
                                                              brand: current_brand,
                                                              user: current_user,
                                                              created_by: current_user,
                                                              last_updated_by: current_user }))
      authorize @order_detail, :create?, policy_class: Api::OrdersPolicy

      # call this function to populate price not for validation purposes
      @order_detail.valid?

      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, @order_detail).price_show?
      params = {
        current_user: current_user,
        order: @order_detail,
        can_manage_price_and_discount: @can_manage_price_and_discount,
        is_new_order: true
      }
      order_data = Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call
      @order_detail = order_data[:order_detail]
      @order_detail_lines = order_data[:order_detail_lines]

      render 'api/orders/new'
    end

    def create
      @order_detail = OrderTransaction.new(order_params.merge({ status: order_params[:status] || 'pending',
                                                                brand: current_brand,
                                                                user: current_user,
                                                                created_by: current_user,
                                                                last_updated_by: current_user,
                                                                is_auto_generated_number: order_params[:order_no].blank? }))

      authorize @order_detail, :create?, policy_class: Api::OrdersPolicy

      Restaurant::Services::Procurement::ExternalOrderPricingChecker
        .new(@order_detail, current_user).call!

      @order_detail.shipping_fee = @order_detail.shipping_fee_was unless Api::OrdersPolicy.new(current_user, @order_detail).create_shipping_fee?
      @order_detail.valid?

      if @order_detail.errors.any?
        response_body = { errors: ApplicationHelper.format_errors(@order_detail.errors) }

        recommended_values = @order_detail.lines_recommended_values.compact
        response_body[:lines_recommended_values] = recommended_values if recommended_values.present?

        custom_error_codes = @order_detail.custom_error_codes.presence || []
        response_body[:custom_error_codes] = custom_error_codes if custom_error_codes.present?
        render json: response_body, status: :unprocessable_entity
        return
      end

      @order_detail = Restaurant::Services::OrderTransaction::CreateAndSendNotifications.new(@order_detail, current_user).call!

      render 'api/orders/create', status: :created
    end

    def bulk_create
      location_to = current_brand.locations.find_by(id: bulk_create_params[:location_to_id])
      authorize location_to, policy_class: Api::OrdersPolicy

      only_validation = params['only_validation']
      klass = if only_validation
                Restaurant::Services::Procurement::OrderBulkValidator
              else
                Restaurant::Services::Procurement::OrderBulkCreator
              end

      result = klass.new(current_user, current_brand, bulk_create_params).call

      render json: { new_order_numbers: result.newly_created_order_numbers, invalid_location_from_ids: result.invalid_location_from_ids },
             status: :created
    end

    def update
      authorize filtered_order, policy_class: Api::OrdersPolicy

      update_shipping_fee_permission = Api::OrdersPolicy.new(current_user, filtered_order).update_shipping_fee?

      filtered_params = order_params.dup

      update_order_params =
        case filtered_order.updateable(current_user)
        when :all
          filtered_params = filtered_params.except(:shipping_fee) unless update_shipping_fee_permission
          filtered_params = filtered_params.except(:request_delivery_date) if check_allow_request_delivery_date
          filtered_params
        when :prices
          filtered_params[:order_transaction_lines_attributes]&.select! { |line| line[:id].present? }
          filtered_params = filtered_params.permit(:notes, :shipping_fee, order_transaction_lines_attributes: %i[id product_buy_price])
          filtered_params = filtered_params.except(:shipping_fee) unless update_shipping_fee_permission
          filtered_params
        when :shipping_fee
          return update_shipping_fee
        else
          raise ::Errors::UnprocessableEntity, I18n.t('orders.errors.not_valid_for_update')
        end

      @order_detail, errors = Restaurant::Services::OrderTransaction::UpdateAndSendNotifications.new(
        filtered_order, current_user, update_order_params, update_shipping_fee_permission
      ).call!

      if errors
        error_object = { errors: errors }
        lines_recommended_values = filtered_order.lines_recommended_values.compact
        error_object = error_object.merge(lines_recommended_values: lines_recommended_values) if lines_recommended_values.present?

        render json: error_object, status: :unprocessable_entity
      end
    end

    def payment_info
      authorize filtered_order, policy_class: Api::OrdersPolicy

      order_nature = payment_info_params[:order_nature] || nil

      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?

      response = {
        show_payment_button: @can_manage_price_and_discount ? !filtered_order.hide_payment_button?(current_user) : false,
        show_reminder_button: !filtered_order.hide_reminder_button?(current_user, order_nature),
        show_manual_payment: !filtered_order.hide_manual_payment?(current_user, order_nature)
      }
      render json: response.as_json
    end

    def remind_payment
      authorize filtered_order, policy_class: Api::OrdersPolicy

      response = Restaurant::Services::Procurement::PaymentReminder
                 .new(filtered_order, current_user).call

      render json: response.as_json
    end

    def shipping_fee_pay_detail
      authorize filtered_order_for_payment_generator, policy_class: Api::OrdersPolicy

      response = Restaurant::Services::Procurement::ShippingFeePayDetailResponseGenerator
                 .new(filtered_order_for_payment_generator, pay_detail_params).call

      response[:order_detail].delete(:rate)
      render json: response.as_json
    end

    def shipping_fee_pay
      authorize filtered_order, policy_class: Api::OrdersPolicy

      response = Restaurant::Services::OrderTransaction::ShippingFeePaymentQuotation
                 .new(filtered_order: filtered_order, pay_params: pay_params, current_user: current_user)
                 .call

      render json: response.as_json, status: :created
    end

    def pay_detail
      authorize filtered_order_for_payment_generator, policy_class: Api::OrdersPolicy

      response = Restaurant::Services::Procurement::OrderPayDetailResponseGenerator
                 .new(filtered_order_for_payment_generator, pay_detail_params).call

      response[:order_detail].delete(:rate)
      render json: response.as_json
    end

    def pay
      authorize filtered_order, policy_class: Api::OrdersPolicy

      response = Restaurant::Services::OrderTransaction::PaymentQuotation
                 .new(filtered_order: filtered_order, pay_params: pay_params, current_user: current_user)
                 .call

      render json: response.as_json, status: :created
    end

    def upload_url
      data = upload('procurement-manual-payment')

      render json: data
    end

    def order_attachments_upload_url
      data = upload('order-attachments')

      render json: data
    end

    def hide_online_payment_display
      authorize filtered_order, policy_class: Api::OrdersPolicy
      filtered_order.after_payment = true
      filtered_order.update!(online_payment_display: false)

      head :ok
    end

    def hide_online_shipping_fee_payment_display
      authorize filtered_order, policy_class: Api::OrdersPolicy
      filtered_order.after_payment = true
      filtered_order.update!(online_shipping_fee_payment_display: false)

      head :ok
    end

    def hide_online_shipping_fee_added_display
      authorize filtered_order, policy_class: Api::OrdersPolicy
      filtered_order.after_payment = true
      filtered_order.update!(online_shipping_fee_added_display: false)

      head :ok
    end

    def update_shipping_fee
      authorize filtered_order, policy_class: Api::OrdersPolicy

      draft_order = Restaurant::Services::Procurement::UpdateShippingFeeHandler
                    .new(filtered_order, current_user, update_shipping_fee_params).call

      if draft_order.errors.any?
        render json: { errors: ApplicationHelper.format_errors(draft_order.errors) }, status: :unprocessable_entity
        return
      end

      notification_type = Notification.notification_types.key(Notification.notification_types['app_notif_procurement_order_incoming_order'])
      if draft_order.non_procurement_payment_push_notification?
        Restaurant::Services::OrderTransaction::NotificationJob.new(
          draft_order, NotificationHelper::ORDER_ACTION_UPDATE_SHIPPING_FEE, notification_type
        ).call
      end

      multibrand_master_order = draft_order.multibrand_master_order
      if multibrand_master_order.present?
        generate_notification(multibrand_master_order, NotificationHelper::ORDER_ACTION_UPDATE_SHIPPING_FEE,
                              notification_type)
      end

      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, draft_order).price_show?

      params = {
        current_user: current_user,
        order: draft_order,
        can_manage_price_and_discount: @can_manage_price_and_discount
      }
      order_data = Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call

      @order_detail = order_data[:order_detail]
    end

    # rubocop:disable Metrics/MethodLength
    # FE will use this API on UI "Print Invoice" when procurement_payment_settings.enable IS TRUE
    def online_payment_invoices
      authorize filtered_order_with_fulfillments, policy_class: Api::OrdersPolicy
      authorize filtered_order_with_fulfillments, :export?, policy_class: Api::OrdersPolicy

      order = filtered_order_with_fulfillments
      @group_by = params[:group_by].to_s
      @is_group_by_category = params[:group_by] == GROUP_BY_CATEGORY
      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, order).price_show?
      @can_see_payment_status = if order.location_from_is_franchise? || order.is_multibrand? || order.from_customer? || order.external?
                                  Api::OrdersPolicy.new(current_user, order).payment_status?
                                else
                                  false
                                end

      filename, online_payment_invoice_params = online_payment_invoices_pdf_response
      pdf_file = PdfGenerators::Procurements::OnlinePaymentInvoice.new(
        **online_payment_invoice_params.merge(document_type: :invoice)
      ).build.render

      respond_to do |format|
        format.pdf.none do
          return send_data pdf_file, filename: "#{filename}.pdf", type: 'application/pdf', disposition: :attachment
        end
        format.pdf.link do
          url = FileHelper.upload_file(
            file: pdf_file,
            bucket: ENV['AWS_BUCKET'],
            acl: 'public-read',
            file_path: 'order-online-payment-invoices-pdf-assets',
            file_name: "#{FileHelper.safe_file_name(filename)}.pdf",
            content_type: 'application/pdf'
          )

          return render json: { url: YourlsClient::YourlsWrapper.url_shortener(url) }
        end
      end
    end
    # rubocop:enable Metrics/MethodLength

    # rubocop:disable Layout/LineLength, Metrics/MethodLength, Metrics/AbcSize
    def show
      authorize filtered_order_with_fulfillments, policy_class: Api::OrdersPolicy
      has_show_price_permission = Api::OrdersPolicy.new(current_user, filtered_order_with_fulfillments).price_show?
      is_order_request = params[:is_order_request] == 'true'
      intent_to_hide_price = params[:hide_price] == 'true' || is_order_request
      order = filtered_order_with_fulfillments
      @can_manage_price_and_discount = has_show_price_permission && !intent_to_hide_price
      @can_see_payment_status = if order.location_from_is_franchise? || order.is_multibrand? || order.from_customer? || order.external?
                                  Api::OrdersPolicy.new(current_user, order).payment_status?
                                else
                                  false
                                end
      @is_group_by_category = params[:group_by] == GROUP_BY_CATEGORY
      @show_product_details = params[:show_product_details] == 'true'
      @update_to_latest = params[:update_to_latest] == 'true'
      @show_vendor_products = params[:show_vendor_products] == 'true'
      @show_permission = params.fetch(:show_permission, false) == 'true'
      @group_by = params[:group_by].to_s
      @order_nature = params[:order_nature]

      respond_to do |format|
        format.json do
          order_json_response
        end
        format.pdf.none do
          filename, pdf_generator_params = order_show_pdf_response
          head(:forbidden) and return if pdf_generator_params.blank?

          send_data build_pdf_file(pdf_generator_params, order), filename: "#{filename}.pdf", type: 'application/pdf', disposition: :attachment
        end
        format.pdf.link do
          filename, pdf_generator_params = order_show_pdf_response
          head(:forbidden) and return if pdf_generator_params.blank?

          safe_filename = FileHelper.safe_file_name("#{current_brand.id}-#{filename}")

          pdf_file = build_pdf_file(pdf_generator_params, order)

          url = FileHelper.upload_file(
            file: pdf_file,
            bucket: ENV['AWS_BUCKET'],
            acl: 'public-read',
            file_path: 'orders-pdf-assets',
            file_name: "#{safe_filename}.pdf",
            content_type: 'application/pdf'
          )

          render json: { url: YourlsClient::YourlsWrapper.url_shortener(url) }
        end
      end
    end
    # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

    # Should be the form to create order fulfillment (want to create order fulfillment).
    # Order ID param is original order.
    def fulfillment
      filtered_order = available_orders.includes(:order_transaction_lines, parent_order_transaction: :order_transaction_lines).find_by! id: params[:id]

      authorize filtered_order, policy_class: Api::OrdersPolicy
      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?
      @can_see_payment_status = Api::OrdersPolicy.new(current_user, filtered_order).payment_status?
      @update_to_latest = fulfillment_params[:update_to_latest] == 'true'
      is_group_by_category = fulfillment_params[:group_by] == GROUP_BY_CATEGORY
      multibrand_fulfillment = params[:multibrand_fulfillment] == 'true'

      respond_to do |format|
        format.json do
          service_params = {
            current_user: current_user,
            order: filtered_order,
            can_manage_price_and_discount: @can_manage_price_and_discount,
            can_see_payment_status: @can_see_payment_status,
            is_group_by_category: is_group_by_category,
            update_to_latest: @update_to_latest,
            location_to: {
              id: fulfillment_params[:location_to_id],
              type: fulfillment_params[:location_to_type]
            },
            show_vendor_products: fulfillment_params[:show_vendor_products],
            is_edit: true
          }

          order_data = build_fulfillment_order_data(service_params, multibrand_fulfillment)

          response = { order_detail: order_data[:order_detail], order_detail_lines: order_data[:order_detail_lines] }
          response.merge({ total_products: order_data[:total_products] }) if is_group_by_category

          render json: response.as_json
        end
      end
    end
    # rubocop:enable Layout/LineLength

    def build_fulfillment_order_data(service_params, multibrand_fulfillment)
      if multibrand_fulfillment
        multibrand_setting = current_brand.as_buyer_multibrand_procurement_settings.joins(:seller_brand)
                                          .find_by(seller_brand_id: params[:location_to_brand_id])
        raise ::Errors::UnprocessableEntity, I18n.t('orders.errors.brand_not_available_for_multibrand_procurement') if multibrand_setting.blank?

        location_to = multibrand_setting.seller_brand.locations.find_by(id: params[:location_to_id])
        raise ::Errors::UnprocessableEntity, I18n.t('orders.errors.brand_not_available_for_multibrand_procurement') if location_to.blank?

        Restaurant::Services::Procurement::MultibrandOrderDetailFulfillmentBuilder
          .new(
            params: service_params.merge({ location_to: { type: 'Location', id: location_to.id } }),
            order_line_fulfillment_detail_builder_klass: Restaurant::Services::Procurement::MultibrandOrderDetailLineFulfillmentBuilder
          )
          .call
      else
        Restaurant::Services::Procurement::OrderFulfillmentDetailResponseBuilder
          .new(params: service_params)
          .call
      end
    end

    # Want to edit the order fulfillment
    # Order ID param is fulfillment order.
    def fulfillment_edit
      filtered_order = available_orders.includes(:order_transaction_lines,
                                                 parent_order_transaction: :order_transaction_lines).find_by! id: params[:id]

      authorize filtered_order, policy_class: Api::OrdersPolicy
      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?
      is_group_by_category = params[:group_by] == GROUP_BY_CATEGORY
      multibrand_fulfillment = params[:multibrand_fulfillment] == 'true'

      respond_to do |format|
        format.json do
          service_params = {
            current_user: current_user,
            order: filtered_order,
            can_manage_price_and_discount: @can_manage_price_and_discount,
            is_group_by_category: is_group_by_category,
            route_fulfillment_order: true,
            show_product_details: false
          }

          order_data = if multibrand_fulfillment
                         location_to = filtered_order.location_to

                         Restaurant::Services::Procurement::MultibrandOrderDetailFulfillmentBuilder
                           .new(
                             params: service_params.merge({ location_to: { type: 'Location', id: location_to.id } }),
                             order_line_fulfillment_detail_builder_klass: Restaurant::Services::Procurement::MultibrandOrderDetailLineBuilder
                           )
                           .call
                       end

          response = { order_detail: order_data[:order_detail], order_detail_lines: order_data[:order_detail_lines] }
          response.merge({ total_products: order_data[:total_products] }) if is_group_by_category

          render json: response.as_json
        end
      end
    end

    def order_json_response
      params = {
        current_user: current_user,
        order: filtered_order,
        can_manage_price_and_discount: @can_manage_price_and_discount,
        can_see_payment_status: @can_see_payment_status,
        is_group_by_category: @is_group_by_category,
        show_product_details: @show_product_details,
        update_to_latest: @update_to_latest,
        show_vendor_products: @show_vendor_products,
        show_permission: @show_permission,
        group_by: @group_by,
        order_nature: @order_nature
      }

      order_data = order_detail_builder_klass(params)

      @order_detail = order_data[:order_detail]
      @order_detail_lines = order_data[:order_detail_lines]
      @total_products = order_data[:total_products]
      @brand_has_category_group = current_brand.product_category_groups.exists?
    end

    def order_show_pdf_response
      valid_location = filtered_order_with_fulfillments.location_to_type == Vendor.name ||
                       filtered_order_with_fulfillments.location_from_type == Customer.name ||
                       filtered_order_with_fulfillments.is_multibrand?
      if Api::OrdersPolicy.new(current_user, filtered_order_with_fulfillments).export?
        has_category_group = current_brand.product_category_groups.exists?
        @order_pdf_detail = OrderHelper.build_order_pdf_detail(filtered_order_with_fulfillments,
                                                               is_group_by_category: @is_group_by_category,
                                                               can_manage_price_and_discount: @can_manage_price_and_discount,
                                                               current_user: current_user,
                                                               has_category_group: has_category_group,
                                                               group_by: @group_by)
        @can_manage_price_and_discount_in_pdf = if valid_location
                                                  @can_manage_price_and_discount
                                                else
                                                  @can_manage_price_and_discount && filtered_order_with_fulfillments.location_from_is_franchise?
                                                end

        filename = I18n.t('orders.order_no', no: filtered_order_with_fulfillments.order_no)
        is_order_request = params[:is_order_request] == 'true'
        filename = "#{I18n.t('orders.request')} #{filename}" if is_order_request
        return [filename, {
          order_pdf_detail: @order_pdf_detail,
          is_order_invoice: false,
          can_manage_price_and_discount: @can_manage_price_and_discount_in_pdf,
          can_see_payment_status: @can_see_payment_status,
          is_order_request: is_order_request,
          is_group_by_category: @is_group_by_category,
          has_category_group: has_category_group,
          group_by: @group_by
        }]
      end
    end

    def online_payment_invoices_pdf_response
      invoices = filtered_order_with_fulfillments.order_transaction_invoices.order(:id)
      items_invoice = invoices.first
      invoiceable = items_invoice.presence || filtered_order_with_fulfillments
      order_number = invoiceable.try(:invoice_no).presence || "order no. #{filtered_order_with_fulfillments.order_no}"
      build_items_invoice_attributes(items_invoice, filtered_order_with_fulfillments)
      build_shipping_fee_invoice_attributes
      if Api::OrdersPolicy.new(current_user, filtered_order_with_fulfillments).export?
        has_category_group = current_brand.product_category_groups.exists?
        @order_pdf_detail = OrderHelper.build_order_pdf_detail(invoiceable,
                                                               is_group_by_category: @is_group_by_category,
                                                               can_manage_price_and_discount: @can_manage_price_and_discount,
                                                               current_user: current_user,
                                                               has_category_group: has_category_group,
                                                               group_by: @group_by)
        filename = I18n.t('orders.invoice_no', no: order_number)
        return [filename, online_payment_invoice_params.merge({ has_category_group: has_category_group, group_by: @group_by })]
      end
    end

    def order_invoice_pdf_response
      if Api::OrdersPolicy.new(current_user, filtered_order_with_fulfillments).export? && @can_manage_price_and_discount
        @is_order_invoice = true
        @can_manage_price_and_discount_in_pdf = (
          check_invoice_pdf_valid_location(filtered_order_with_fulfillments) || filtered_order_with_fulfillments.is_multibrand?
        ) && @can_manage_price_and_discount

        has_category_group = current_brand.product_category_groups.exists?
        @order_pdf_detail = OrderHelper.build_order_pdf_detail(filtered_order_with_fulfillments,
                                                               is_group_by_category: @is_group_by_category,
                                                               can_manage_price_and_discount: @can_manage_price_and_discount_in_pdf,
                                                               current_user: current_user,
                                                               has_category_group: has_category_group,
                                                               group_by: @group_by)

        {
          order_pdf_detail: @order_pdf_detail,
          is_order_invoice: @is_order_invoice,
          can_manage_price_and_discount: @can_manage_price_and_discount_in_pdf,
          is_group_by_category: @is_group_by_category,
          has_category_group: has_category_group,
          group_by: @group_by
        }
      end
    end

    def order_detail_builder_klass(params)
      if filtered_order.order_fulfillment? && filtered_order.is_multibrand?
        Restaurant::Services::Procurement::MultibrandOrderDetailFulfillmentDisplay
          .new(params: params, order_line_fulfillment_detail_builder_klass: Restaurant::Services::Procurement::MultibrandOrderDetailLineBuilder)
          .call
      elsif filtered_order.order_fulfillment?
        Restaurant::Services::Procurement::OrderFulfillmentDetailResponseBuilder.new(params: params).call
      else
        Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call
      end
    end

    def history
      authorize filtered_order, policy_class: Api::OrdersPolicy
      preset_audits = filtered_order.own_and_associated_audits.includes(:auditable).reorder(created_at: :desc)
      first_request_uuid = filtered_order.audits.order(:id).first.try(:request_uuid)
      preset_audits = preset_audits.includes(%i[associated user]).reject { |x| x.associated.present? && x.request_uuid == first_request_uuid }
      @audits = generate_audit_response(preset_audits)

      can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?
      unless can_manage_price_and_discount
        @audits.each_with_index do |audit, audit_index|
          next if audit[:descriptions].blank?

          description = audit[:descriptions].join(' ')
          whitelist_amount_field.each do |field|
            @audits[audit_index][:descriptions] = [I18n.t('general.error_401')] if description.include?(field)
          end
        end
      end

      render json: { audits: @audits }
    end

    def whitelist_amount_field
      ['total_amount', 'shipping_fee', 'total_tax', 'product_buy_price', 'tax_rate']
    end

    def show_with_delivery
      authorize filtered_order, policy_class: Api::OrdersPolicy
      can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?

      params = {
        current_user: current_user,
        order: filtered_order,
        can_manage_price_and_discount: can_manage_price_and_discount,
        simple_mode: true
      }

      order_detail = if filtered_order.order_fulfillment?
                       Restaurant::Services::Procurement::OrderFulfillmentDetailResponseBuilder.new(params: params).call
                     else
                       Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call
                     end

      @order_detail = order_detail[:order_detail]
      @order_detail_lines = OrderHelper.build_order_detail_lines_with_delivery(
        order: filtered_order,
        can_manage_price_and_discount: can_manage_price_and_discount
      )
    end

    def related_transactions
      order_used = filtered_order_related_transaction
      authorize order_used, policy_class: Api::OrdersPolicy
      @related_transactions = Restaurant::Services::Procurement::RelatedTransactionsGenerator.new(order_used, current_user).call

      render json: { related_transactions: @related_transactions.as_json }
    end

    def check_filtered_order_related_transaction
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_order_related_transaction.nil?
    end

    def filtered_order_related_transaction
      @filtered_order_related_transaction ||= available_orders.includes(order_transaction_fulfillments: :delivery_transactions)
                                                              .find_by! id: params[:id]
    end

    # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
    def invoice
      authorize filtered_order_with_fulfillments, policy_class: Api::OrdersPolicy
      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order_with_fulfillments).price_show?
      @is_group_by_category = params[:group_by] == GROUP_BY_CATEGORY
      @group_by = params[:group_by].to_s

      respond_to do |format| # rubocop:disable Metrics/BlockLength
        format.json do
          params = {
            current_user: current_user,
            order: filtered_order_with_fulfillments,
            can_manage_price_and_discount: @can_manage_price_and_discount,
            is_group_by_category: @is_group_by_category
          }
          order_data = Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call

          @order_detail = order_data[:order_detail]
          @order_detail_lines = order_data[:order_detail_lines]
          @total_products = order_data[:total_products]
        end

        format.pdf.none do
          pdf_generator_params = order_invoice_pdf_response
          head(:forbidden) and return if pdf_generator_params.blank?

          filename = "#{I18n.t('orders.invoice_no', no: filtered_order_with_fulfillments.order_no)}.pdf"

          pdf_file = PdfGenerators::Procurements::Order.new(**pdf_generator_params.merge(document_type: :invoice))
                                                       .build.render
          send_data pdf_file, filename: filename, type: 'application/pdf', disposition: :attachment
        end
        format.pdf.link do
          pdf_generator_params = order_invoice_pdf_response
          head(:forbidden) and return if pdf_generator_params.blank?

          filename_prefix = "#{current_brand.id}-#{filtered_order_with_fulfillments.id}"
          filename = FileHelper.safe_file_name(
            "#{filename_prefix}-#{I18n.t('orders.order_no', no: filtered_order_with_fulfillments.order_no)}"
          )

          pdf_file = PdfGenerators::Procurements::Order.new(**pdf_generator_params.merge(document_type: :invoice))
                                                       .build.render

          url = FileHelper.upload_file(
            file: pdf_file,
            bucket: ENV['AWS_BUCKET'],
            acl: 'public-read',
            file_path: 'orders-pdf-assets',
            file_name: "#{filename}.pdf",
            content_type: 'application/pdf'
          )

          render json: { url: YourlsClient::YourlsWrapper.url_shortener(url) }
        end
      end
    end
    # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

    def check_invoice_pdf_valid_location(filtered_order)
      location_from_franchise = filtered_order.location_from_type == 'Location' && filtered_order.location_from.is_franchise
      valid_location_from = location_from_franchise || filtered_order.location_from_type == 'Customer'

      valid_location_from && filtered_order.location_to_type == 'Location'
    end

    def approve
      authorize filtered_order, policy_class: Api::OrdersPolicy
      error_message = I18n.t('orders.status_invalid') unless filtered_order.pending?

      params = approval_params

      if error_message.present?
        render json: { message: error_message }, status: :unprocessable_entity
        return
      end

      raise ::Errors::UnprocessableEntity, I18n.t('orders.errors.can_only_be_done_in_sender') if filtered_order.original_multibrand_order?

      Restaurant::Services::Procurement::OrderApprover
        .new(filtered_order, current_user, params[:order_nature]).call

      render json: nil, status: :no_content
    end

    def void
      authorize filtered_order, policy_class: Api::OrdersPolicy
      unless filtered_order.pending?
        render json: { message: I18n.t('orders.status_invalid') }, status: :unprocessable_entity
        return
      end

      params = approval_params
      params[:order_nature] == 'incoming' ? filtered_order.location_to_id : filtered_order.location_from_id

      Restaurant::Services::Procurement::OrderVoider
        .new(
          order_transaction: filtered_order,
          void_params: void_params,
          current_user: current_user,
          order_nature: params[:order_nature]
        )
        .call

      render json: nil, status: :no_content
    end

    def close
      authorize filtered_order, policy_class: Api::OrdersPolicy
      unless filtered_order.processing?
        render json: { message: I18n.t('orders.status_invalid') }, status: :unprocessable_entity
        return
      end

      errors = Restaurant::Services::Procurement::OrderCloser
               .new(filtered_order, close_params, current_user)
               .call

      render json: { message: errors[:errors] }, status: :unprocessable_entity and return if errors[:errors].present?

      @can_manage_price_and_discount = Api::OrdersPolicy.new(current_user, filtered_order).price_show?
      @is_group_by_category = params[:group_by] == GROUP_BY_CATEGORY
      order_json_response
    end

    def payment_status
      authorize filtered_order, policy_class: Api::OrdersPolicy
      Restaurant::Services::Procurement::ManualUpdatePaymentStatusChecker.new(filtered_order).call!

      raise ::Errors::UnprocessableEntity, I18n.t('orders.errors.can_only_be_done_in_sender') if filtered_order.original_multibrand_order?

      if filtered_order.void?
        render json: { message: I18n.t('orders.status_invalid') }, status: :unprocessable_entity
        return
      end

      filtered_order.skip_update_duplicate = true
      filtered_order.update!(payment_status: order_params[:payment_status], audit_custom_action: 'record_payment', last_updated_by: current_user)

      multibrand_master_order = filtered_order.multibrand_master_order
      if multibrand_master_order.present?
        multibrand_master_order.skip_update_duplicate = true
        multibrand_master_order.update!(payment_status: order_params[:payment_status], audit_custom_action: 'record_payment',
                                        last_updated_by: current_user)
      end
    end

    def order_lines
      authorize filtered_order, policy_class: Api::OrdersPolicy

      order_lines = filtered_order.order_transaction_lines.map do |detail|
        Restaurant::Services::Procurement::OrderLineDetailResponseBuilder.new(params: {
                                                                                order: filtered_order,
                                                                                order_transaction_line: detail,
                                                                                show_product_details: true,
                                                                                location_from_id: filtered_order.location_from_id,
                                                                                current_user: current_user
                                                                              }).call
      end

      render json: { order_lines: order_lines }
    end

    def unavailable_products
      order_type_fulfillment = unavailable_products_params[:order_type] == Restaurant::Constants::ORDER_TYPE_FULFILLMENT
      filtered_order_for_unavailable_products.order_type_fulfillment = order_type_fulfillment

      authorize filtered_order_for_unavailable_products, policy_class: Api::OrdersPolicy

      product_ids = filtered_order_for_unavailable_products.order_transaction_lines.map(&:product_id)
      unless order_type_fulfillment
        products = ProductQuery.new(unavailable_products_params.merge({ current_user: current_user, no_stock: 'false',
                                                                        status: 'activated', presentation: 'ids_only', current_page: 1,
                                                                        item_per_page: filtered_order_for_unavailable_products
                                                                                        .order_transaction_lines
                                                                                        .count,
                                                                        ids: product_ids.join(',') })).filter
        product_ids = products[:data].map { |product| product[:id] }
      end

      location_to = {
        id: unavailable_products_params[:location_to_id],
        type: unavailable_products_params[:location_to_type]
      }

      unavailable_products = Restaurant::Services::Procurement::UnavailableProducts
                             .new(filtered_order_for_unavailable_products, product_ids, location_to, order_type_fulfillment, current_user).call

      return render json: { unavailable_products: unavailable_products }
    end

    def multibrand_unavailable_products
      authorize filtered_order, policy_class: Api::OrdersPolicy

      unavailable_products = Restaurant::Services::Procurement::MultibrandUnavailableProducts
                             .new(filtered_order, unavailable_products_params, current_user).call

      return render json: { unavailable_products: unavailable_products }
    end

    def unavailable_units
      product_units = []
      params = unavailable_units_params

      valid_location_from_type, valid_location_to_type = if unavailable_units_params[:order_type] == Restaurant::Constants::ORDER_TYPE_FULFILLMENT
                                                           [[Location.name], [Location.name, Vendor.name]]
                                                         else
                                                           [[Location.name, Customer.name], [Location.name]]
                                                         end

      invalid_location_types = valid_location_from_type.exclude?(params[:location_from_type]) ||
                               valid_location_to_type.exclude?(params[:location_to_type])

      return render json: { product_units: product_units } if params[:order_transaction_line_ids].blank? || invalid_location_types

      product_units = Restaurant::Services::Procurement::UnavailableUnits.new(params, current_user).call

      return render json: { product_units: product_units }
    end

    def customers
      query_result = CustomerQuery.new(
        customer_filter_params.merge({ current_user: current_user, is_customers_filter_on_orders_page: 'true' })
      ).filter

      paging = generate_prev_next_page(query_result[:paging])

      return render json: { customers: query_result[:data], paging: paging }
    end

    def approvals
      order_nature = params[:order_nature]
      approvals = Restaurant::Services::Procurement::ApprovalGenerator.new(filtered_order_with_fulfillments, order_nature).call!

      return render json: { approvals: approvals }
    end

    def no_permission(exception)
      return no_permission_to_update_payment_status if exception.query == 'payment_status?'

      render json: { message: I18n.t('general.error_401') }, status: :forbidden
    end

    def no_permission_to_update_payment_status
      render json: { message: I18n.t('orders.errors.cannot_use_manual_update_payment_status') }, status: :forbidden
    end

    def remind_approvals
      authorize filtered_order, policy_class: Api::OrdersPolicy

      params = remind_approval_params

      order_nature = params[:order_nature]
      location_id = order_nature == 'outgoing' || filtered_order.to_vendor? ? filtered_order.location_from_id : filtered_order.location_to_id
      user_ids = params[:user_ids]
      access_list_ids = params[:role_ids]

      response = Restaurant::Services::Procurement::OrderApprovalReminder
                 .new(filtered_order, user_ids, location_id, access_list_ids).call

      render json: response.as_json
    end

    def approval_users
      authorize filtered_order, policy_class: Api::OrdersPolicy

      access_list_ids = filtered_order.approvals.map(&:access_list_id)
      location = params[:order_nature] == 'incoming' ? filtered_order.location_to : filtered_order.location_from

      response = Restaurant::Services::Users::ListGenerator
                 .new(params.merge(status: Restaurant::Constants::ACTIVE, access_list_ids: access_list_ids),
                      location).call

      render json: response
    end

    private

    def all_locations_access_with_permission?
      brand_active_location_ids = current_user.selected_brand.locations.active.pluck(:id)
      user_accessible_location_ids = current_user.available_locations.active.pluck(:id)

      return false unless (brand_active_location_ids - user_accessible_location_ids).empty?

      locations_with_permission = LocationsUser.joins(:access_list).where(
        user: current_user,
        location_id: brand_active_location_ids
      ).where("location_permission @> '{\"order\":{\"index\": true}}'").pluck(:location_id)

      (brand_active_location_ids - locations_with_permission).empty?
    end

    def customer_filter_params
      params
        .permit(:status, :page, :item_per_page, :keyword)
    end

    def upload(file_path)
      upload_params = presigned_url_params.merge({
                                                   file_path: file_path,
                                                   file_key: "#{SecureRandom.uuid}-#{presigned_url_params[:filename]}"
                                                 })

      FileHelper.upload_url(upload_params)
    end

    def validate_outlet_to_outlet
      raise Errors::InvalidParamsError, I18n.t('orders.errors.outlet_to_outlet_not_supported') unless filtered_order.allow_procurement_payment?
    end

    def validate_pay
      validate_pay_locations
      # Reject if already paid
      raise Payment::Errors::AlreadyPaid, I18n.t('orders.errors.already_paid') if filtered_order.procurement_paid?

      validate_approval

      raise Errors::InvalidParamsError, I18n.t('orders.errors.already_void') if filtered_order.void?
    end

    def validate_reminder_pay
      validate_pay_locations

      raise Payment::Errors::AlreadyPaid, I18n.t('orders.errors.already_paid') if filtered_order.procurement_paid?
      raise Errors::InvalidParamsError, I18n.t('orders.errors.already_void') if filtered_order.void?
    end

    def validate_shipping_fee_pay
      validate_pay_locations

      raise Payment::Errors::AlreadyPaid, I18n.t('orders.errors.already_paid') if filtered_order.shipping_fee_paid?
      raise Payment::Errors::AlreadyPaid, I18n.t('orders.errors.already_paid') if filtered_order.paid?
      raise Errors::InvalidParamsError, I18n.t('orders.errors.must_pay_items_first_or_together') if filtered_order.order_transaction_invoices.blank?
      raise Errors::InvalidParamsError, I18n.t('orders.errors.already_void') if filtered_order.void?
    end

    # For now, only postpaid payment need to check for approval.
    # Except for multibrand duplicated non fulfillment order which ignores procurement payment settings.
    def validate_approval
      return unless filtered_order.payment_receiver_brand.setup_procurement_payment_setting.postpaid_enabled?
      return if filtered_order.multibrand_duplicated_non_fulfillment_order?

      if filtered_order.pending?
        raise Errors::InvalidParamsError,
              I18n.t('orders.errors.need_approval', location_name: filtered_order.location_to.name)
      end
    end

    def validate_pay_locations
      return if filtered_order.multibrand_duplicated_non_fulfillment_order?

      # Reject if not from franchise.
      raise Errors::InvalidParamsError, I18n.t('orders.errors.not_from_franchise') unless filtered_order.location_from_is_franchise?
      raise Errors::InvalidParamsError, I18n.t('orders.errors.not_to_central_kitchen') unless filtered_order.location_allow_procurement_payment?
    end

    def validate_payment_method
      return if filtered_order.multibrand_duplicated_non_fulfillment_order?

      Restaurant::Services::Procurement::PaymentMethodChecker
        .new(order: filtered_order, payment_method: params[:payment_method], payment_method_type: params[:payment_method_type])
        .call!
    end

    def build_items_invoice_attributes(items_invoice, order)
      @is_order_invoice = true
      @can_manage_price_and_discount_in_pdf = @can_manage_price_and_discount
      @payment_date = Restaurant::Services::Procurement::InvoicePaymentDateGenerator.new(
        items_invoice: items_invoice, order: order,
        device_time: params[:device_time], device_timezone: params[:device_timezone]
      ).call!
      @payment_method = items_invoice&.online_payments&.first&.external_type&.humanize
      @payment_amount = items_invoice ? items_invoice&.online_payments&.first&.amount.to_f : nil
      @items_invoice_no = items_invoice&.invoice_no
      @items_invoice_date = items_invoice&.created_at&.strftime('%d/%m/%Y')
      @shipping_fee_invoice = if filtered_order.order_transaction_invoices.shipping_fee.present?
                                filtered_order.order_transaction_invoices.shipping_fee.last
                              elsif filtered_order.order_transaction_invoices&.items&.first&.shipping_fee.to_d.positive?
                                nil
                              else
                                shipping_fee = filtered_order.shipping_fee
                                Restaurant::Models::OrderTransactionInvoice
                                  .new(shipping_fee: shipping_fee, total_amount: shipping_fee)
                              end
      @items_paid = filtered_order.items_paid
    end

    def build_shipping_fee_invoice_attributes
      return if @shipping_fee_invoice.blank?

      @shipping_fee_payment_date = @shipping_fee_invoice&.paid_at&.strftime('%d/%m/%Y')
      @shipping_fee_payment_method = @shipping_fee_invoice&.online_payments&.first&.external_type&.humanize

      shipping_fee_online_payments = @shipping_fee_invoice&.online_payments
      @shipping_fee_payment_amount = shipping_fee_online_payments.present? ? shipping_fee_online_payments&.first&.amount.to_f : nil
      @shipping_fee_paid = filtered_order.shipping_fee_paid?
    end

    def build_pdf_file(pdf_generator_params, order)
      if order.from_customer?
        PdfGenerators::Procurements::OrderFromCustomer.new(**pdf_generator_params).build.render
      else
        PdfGenerators::Procurements::Order.new(**pdf_generator_params).build.render
      end
    end

    def where_condition_for_location_to(location_from)
      Restaurant::Services::Locations::WithMultibrandLocationToElasticSearchConditionsGenerator
        .new(current_user, location_from, location_filter_params).call
    end

    def check_allow_request_delivery_date
      params[:order_transaction][:request_delivery_date].present? && !current_brand.setup_procurement_setting_enable_request_delivery_date?
    end

    def validate_enable_request_delivery_date
      raise Errors::InvalidParamsError, I18n.t('orders.errors.request_delivery_date_not_enabled') if check_allow_request_delivery_date
    end

    def available_orders
      location_ids = current_user.available_locations.pluck(:id)
      OrderTransaction.where(brand: current_brand)
                      .where('(location_to_id IN (?) and location_to_type = ?)
                              OR (location_from_id IN (?) and location_from_type = ?)
                              OR (fulfillment_location_id IN (?))', location_ids,
                             'Location', location_ids,
                             'Location', location_ids)
                      .includes(order_transaction_lines: %i[product product_unit])
    end

    def filtered_order
      @filtered_order ||= available_orders.find_by! id: params[:id]
    end

    def filtered_order_for_payment_generator
      @filtered_order_for_payment_generator = available_orders
                                              .include_all_procurement_assocations
                                              .find_by! id: params[:id]
    end

    def filtered_order_for_unavailable_products
      @filtered_order_for_unavailable_products ||= available_orders.includes(
        order_transaction_lines:
        [:product, :product_unit, :delivery_transaction_lines,
         { order_transaction_line_fulfillments: :delivery_transaction_lines }]
      )
                                                                   .find_by! id: params[:id]
    end

    def filtered_order_with_fulfillments
      @filtered_order_with_fulfillments ||= available_orders.includes(
        order_transaction_lines: %i[order_transaction_line_fulfillments delivery_transaction_lines]
      ).find_by! id: params[:id]
    end

    def check_filtered_order
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_order.nil?
    end

    def location_filter_params
      params
        .permit(:keyword, :page, :item_per_page, :location_to_id, :location_to_type, :is_bulk_order, :branch_type, :multibrand_fulfillment,
                :location_from_id, :location_from_type, :procurement_enable_sell_to_customer, :order_type, :exclude_location, :exclude_vendor)
    end

    def update_shipping_fee_params
      params
        .require(:order_transaction)
        .permit(:shipping_fee, :notes, :request_delivery_date, order_attachments: %i[name url from_camera skip_validation])
    end

    def order_params
      params
        .require(:order_transaction)
        .permit(:payment_status, :order_date, :order_no,
                :user_from_id, :location_from_id, :location_from_type,
                :location_to_id, :location_to_type, :fulfillment_location_id,
                :shipping_fee, :notes, :parent_order_transaction_id, :request_delivery_date,
                order_transaction_lines_attributes: [
                  :id, :product_id, :product_unit_id, :parent_order_line_id,
                  :product_buy_price, :tax_id, :tax_name, :tax_rate,
                  :product_qty, :discount, :discount_total, :total_amount, :_destroy,
                  { metadata: {} }
                ],
                applied_promos: [
                  :procurement_promo_id, :promo_name, :promo_amount, :start_date, :end_date, :combine_promo,
                  {
                    promo_rule: [
                      :id, :procurement_promo_id, :total_min, :total_min_exclude_categories_ids, :product_condition,
                      :apply_to_type, :minimum_purchase_products_type,
                      { promo_rule_minimum_purchase_products: %i[id product_id product_unit_id quantity],
                        promo_rule_apply_to_products: %i[id product_id product_unit_id] }
                    ],
                    promo_reward: %i[id procurement_promo_id template discount_amount discount_maximum],
                    lines_with_promo: %i[index discount]
                  }
                ],
                order_attachments: %i[name url from_camera skip_validation])
    end

    def bulk_create_params
      params
        .permit(
          :order_date, :all_location_froms, :location_to_id, :notes, :only_validation, :request_delivery_date,
          location_from_ids: [], exclude_location_from_ids: [],
          order_transaction_lines_attributes: %i[product_id product_qty product_unit_id],
          order_attachments: %i[name url from_camera skip_validation]
        )
    end

    def new_params
      params
        .require(:order_transaction)
        .permit(:location_from_id, :location_from_type,
                :location_to_id, :location_to_type, :request_delivery_date,
                order_transaction_lines_attributes: %i[product_id product_unit_id])
    end

    def fulfillment_params
      params.permit(:location_to_id, :location_to_type, :group_by, :show_vendor_products, :update_to_latest)
    end

    def void_params
      params.require(:order_transaction).permit(:void_notes)
    end

    def close_params
      params.require(:order_transaction)
            .permit(:closed_notes)
            .merge({ allow_invalid_promos_to_be_removed: params[:allow_invalid_promos_to_be_removed] })
    end

    def set_request_variant
      request.variant = :link if params[:as_link] == 'true'
    end

    def order_filter_params
      params
        .permit(:keyword, :status, :payment_status, :page, :item_per_page,
                :start_date, :end_date, :exclude_order_ids, :fulfillment_location_id,
                :location_from_ids, :exclude_location_from_ids,
                :location_to_ids, :exclude_location_to_ids,
                :can_create_delivery, :exclude_order_fulfillment,
                :is_select_all_customer_from, :customer_from_ids, :exclude_customer_from_ids,
                :vendor_to_ids, :exclude_vendor_to_ids, :showing_order,
                :request_delivery_start_date, :request_delivery_end_date)
    end

    def pay_detail_params
      params.permit(:payment_method, :payment_method_type)
    end

    def pay_params
      params.permit(
        :payment_method,
        :payment_method_type,
        :mobile_number,
        credit_card_detail: %i[
          token_id
          authentication_id
          card_cvn
        ],
        manual_payment_detail: [:date, :notes, { proofs: %i[url name] }]
      )
    end

    def presigned_url_params
      params.require(:presigned_url).permit(:filename, :content_type, :content_length)
    end

    def online_payment_invoice_params
      {
        order_pdf_detail: @order_pdf_detail,
        is_order_invoice: @is_order_invoice,
        can_manage_price_and_discount: @can_manage_price_and_discount_in_pdf,
        can_see_payment_status: @can_see_payment_status,
        is_group_by_category: @is_group_by_category,
        items_paid: @items_paid,
        payment_date: @payment_date,
        payment_method: @payment_method,
        payment_amount: @payment_amount,
        shipping_fee_invoice: @shipping_fee_invoice,
        shipping_fee_paid: @shipping_fee_paid,
        shipping_fee_payment_date: @shipping_fee_payment_date,
        shipping_fee_payment_method: @shipping_fee_payment_method,
        shipping_fee_payment_amount: @shipping_fee_payment_amount,
        items_invoice_no: @items_invoice_no,
        items_invoice_date: @items_invoice_date
      }
    end

    def payment_info_params
      params.permit(:order_nature)
    end

    def unavailable_products_params
      params.permit(
        :location_id,
        :location_to_id,
        :location_to_type,
        :fulfillment_location_id,
        :exclude_product_with_variances,
        :recipe_line_product,
        :internal_distribution_type,
        :filter_by_sub_brand,
        :sell_to_customer_type,
        :procurement_from_customer,
        :order_type,
        :location_to_brand_id
      )
    end

    def unavailable_units_params
      params.permit(
        :order_transaction_line_ids,
        :location_from_id,
        :location_from_type,
        :location_to_id,
        :location_to_type,
        :order_type
      )
    end

    def approval_params
      params.permit(
        :order_nature
      )
    end

    def remind_approval_params
      params.permit(
        :order_nature,
        role_ids: [],
        user_ids: []
      )
    end

    def approval_users_params
      params.permit(:keyword, :page, :item_per_page, :order_nature)
    end
  end
  # rubocop:enable Metrics/ClassLength
end
