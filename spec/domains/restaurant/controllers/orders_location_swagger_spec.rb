require './spec/shared/procurements'
require './spec/shared/procurement_fulfillments'
require './spec/shared/procurement_payments'
require './spec/shared/online_payments'
require './spec/shared/order_transaction_invoices'
require './spec/shared/invoices'
require './spec/shared/orders'
require './spec/shared/swagger'
require './spec/shared/locations'
require './spec/shared/locations_examples'
require './spec/shared/customers'
require './spec/shared/upload_url'
require './spec/shared/multi_brand_procurement_settings'

describe 'api/orders', type: :request, search: true do
  include_context 'procurements creations'
  include_context 'procurement payments'
  include_context 'online payments creations'
  include_context 'restaurant procurement orders creations'
  include_context 'order transaction invoices creations'
  include_context 'invoices creations'
  include_context 'swagger after response'
  include_context 'locations creations'
  include_context 'customers creations'
  include_context "procurement fulfillments creations"
  include_context 'multi brand procurement settings creations'

  # cleaning up
  before(:all) do
    OrderTransaction.reindex
  end

  before(:each) do
    @header = authentication_header(owner)
    central_kitchen
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:employee) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }
  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_3.id]) }

  let(:unused_product_unit) { create(:product_unit, brand: brand) }
  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product) { create(:product, brand: brand, product_unit: product_unit) }
  let(:order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_buy_price: product.internal_price(nil, product_unit.id),
                                            product_unit_id: product_unit.id)
    build(:order_params, location_from_id: owned_branch_2.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:franchise_order_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte.product_unit_id),
                                            product_unit_id: latte.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_1.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen) }

  let(:other_order) { create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: owned_branch_2, location_to: central_kitchen) }
  let(:order_owned_branch_3) do
    create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: owned_branch_3, location_to: central_kitchen)
  end

  let(:vendor) { create(:vendor, :active, location_ids: [owned_branch_2.id, central_kitchen.id], brand: brand) }
  let(:external_order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: owned_branch_2, location_to: vendor) }
  let(:external_order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_unit_id: product_unit.id)
    build(:order_external_params, location_to_id: vendor.id, location_to_type: 'Vendor',
                                  location_from_id: owned_branch_1.id, location_from_type: 'Location',
                                  order_transaction_lines_attributes: [order_lines])
  end

  let(:external_order) do
    create(:order_with_lines, brand: brand,
                              location_from: owned_branch_2,
                              location_to: vendor)
  end

  let(:external_order_to_ck) do
    create(:order_with_lines, brand: brand,
                              location_from_type: 'Vendor',
                              location_from: vendor,
                              location_to: central_kitchen)
  end

  let(:employee_access_list) do
    location_user = LocationsUser.find_by(location: owned_branch_2, user: employee)
    location_user.access_list
  end

  let(:employee_access_list_location_owner_ck) do
    employee.add_permission_to_location(central_kitchen, owner, AccessList.location_owner)
    location_user = LocationsUser.find_by(location: central_kitchen, user: employee)
    location_user.access_list
  end

  let(:owner_access_list) do
    location_user = LocationsUser.find_by(location: owned_branch_2, user: owner)
    location_user.access_list
  end

  path '/api/orders/location_from' do
    get('location_from order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_keyword' }
      parameter name: :page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_page' }
      parameter name: :item_per_page, in: :query, required: false, type: :string,
                schema: { '$ref' => '#/components/parameters/parameter_item_per_page' }
      parameter name: :location_from_id, in: :query, type: :string, required: false
      parameter name: :location_from_type, in: :query, type: :string, required: false
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :location_to_type, in: :query, type: :string, required: false
      parameter name: :branch_type, in: :query, type: :string, required: false
      parameter name: :is_bulk_order, in: :query, type: :string, required: false

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_location_from_order'

        before do
          central_kitchen
          central_kitchen_2
          vendor_1
          vendor_2
          owned_branch_1
          owned_branch_2
          franchise_branch_1
          franchise_branch_2
        end

        context "when location to isn't specified" do
          before do |example|
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(6)
            expect(response_body).to have_key('has_all_locations_access')
            expect(response_body['has_all_locations_access']).to be_in([true, false])
          end
        end

        context 'when some locations are inactive' do
          before do |example|
            owned_branch_1.update!(status: 1)
            owned_branch_1.reindex

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all locations' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(5)
            expect(response_body['data'].map { |data| data['name'] }).to_not include(owned_branch_1.name.to_s)
          end
        end

        context 'when with branch_type param' do
          let(:branch_type) { 'outlet' }

          before do |example|
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return locations with such branch_type' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(4)

            location_names = response_body['data'].map { |location| location['name'] }
            expect(location_names).to match_array(
              ["Franchise Location Ciputat",
              "Franchise Location Rawa Buaya",
              "Owned Location Parung",
              "Owned Location Sudirman"]
            )
          end
        end

        context 'when location to CK' do
          let(:location_to_id) { central_kitchen.id.to_s }
          let(:location_to_type) { 'Location' }

          before do |example|
            owned_branch_1.update!(status: 1)

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return filtered locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(4)
            expect(response_body['data'].map { |data| data['name'] }).to_not include(owned_branch_1.name.to_s)
            expect(response_body['data'].map { |data| data['name'] }).to_not include(central_kitchen.name.to_s)
            expect(response_body['data'].map { |data| data['name'] }).to include(central_kitchen_2.name.to_s)
          end
        end

        context 'when location to CK and is_bulk_order is true' do
          let(:location_to_id) { central_kitchen.id.to_s }
          let(:location_to_type) { 'Location' }
          let(:is_bulk_order) { 'true' }

          before do |example|
            owned_branch_1.update!(status: 1)
            owned_branch_2
            owned_branch_3

            owner.selected_brand = brand
            owner.locations_users.find_by(location: owned_branch_2).delete
            owner.locations_users.find_by(location: owned_branch_3).delete

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return filtered locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] }).to match_array(
              ["Franchise Location Ciputat", "Franchise Location Rawa Buaya", "Owned Location Sudirman", "Owned Location Sukamulya"]
            )
          end
        end

        context 'when location to Vendor' do
          let(:location_to_id) { vendor_1.id.to_s }
          let(:location_to_type) { 'Vendor' }

          before do |example|
            owned_branch_1.update!(status: 1)

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return filtered locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(5)
            expect(response_body['data'].map { |data| data['name'] }).to_not include(owned_branch_1.name.to_s)
            expect(response_body['data'].map { |data| data['name'] }).to_not include(vendor_1.name.to_s)
            expect(response_body).to have_key('has_all_locations_access')
            expect(response_body['has_all_locations_access']).to be_in([true, false])
          end
        end

        context 'when user has access and permission to all brand locations' do
          before do |example|
            brand.locations.active.each do |location|
              owner.add_permission_to_location(location, owner, AccessList.location_owner) unless owner.locations_users.exists?(location: location)
            end
            Location.reindex

            submit_request(example.metadata)
          end

          it 'should return has_all_locations_access as true' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['has_all_locations_access']).to eq(true)
          end
        end

        context 'when user does not have access to all brand locations' do
          let(:limited_user) { create(:confirmed_user) }
          let(:"Brand-UUID") do
            limited_user.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
          end

          before do |example|
            limited_user.add_permission_to_location(owned_branch_1, owner, AccessList.location_owner)
            limited_user.locations_users.where.not(location: owned_branch_1).delete_all

            @header = authentication_header(limited_user)
            Location.reindex

            submit_request(example.metadata)
          end

          it 'should return has_all_locations_access as false' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['has_all_locations_access']).to eq(false)
          end
        end
      end
    end
  end

  path '/api/orders/location_to' do
    get('location_to order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_keyword' }
      parameter name: :page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_page' }
      parameter name: :item_per_page, in: :query, required: false, type: :string,
                schema: { '$ref' => '#/components/parameters/parameter_item_per_page' }
      parameter name: :location_from_id, in: :query, type: :string, required: false
      parameter name: :location_from_type, in: :query, type: :string, required: false
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :location_to_type, in: :query, type: :string, required: false
      parameter name: :procurement_enable_sell_to_customer, in: :query, type: :string, required: false
      parameter name: :is_bulk_order, in: :query, type: :string, required: false
      parameter name: :order_type, in: :query, type: :string, required: false
      parameter name: :exclude_vendor, in: :query, type: :string, required: false
      parameter name: :multibrand_fulfillment, in: :query, type: :string, required: false, description: 'Used in multibrand fulfillment form'

      let(:order_type) { '' }

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_location_from_order'

        context 'when exclude franchise' do
          let(:order_type) { 'fulfillment' }
          let(:location_from_id) { franchise_branch_1.id }
          let(:location_from_type) { 'Location' }

          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1
            vendor_2
            owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update_columns(procurement_enable_outlet_to_outlet: true)
            franchise_branch_1.update!(central_kitchen_ids: [central_kitchen.id, central_kitchen_2.id],
                                       procurement_enable_outlet_to_outlet: true, procurement_enable_franchise_to_franchise: true)
            franchise_branch_2

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should return only vendors and internal locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].size).to eql(6)
            expect(response_body['data'].map { |data| data['name'] }).to match_array([
              central_kitchen.name.to_s,
              central_kitchen_2.name.to_s,
              vendor_1.name.to_s,
              vendor_2.name.to_s,
              owned_branch_1.name.to_s,
              owned_branch_2.name.to_s
            ])
          end
        end

        context 'when multibrand_fulfillment, buyer is CK' do
          let(:multibrand_fulfillment) { 'true' }
          let(:location_from_id) { central_kitchen.id.to_s }
          let(:location_from_type) { 'Location' }

          before do |example|
            central_kitchen

            vendor_1
            vendor_2.location_ids = []
            vendor_2.save
            owned_branch_1.update!(
              central_kitchen_ids: owned_branch_1.central_kitchen_ids
            )

            central_kitchen_2
            franchise_branch_1

            brand_2_central_kitchen
            brand_2_franchise_branch_1

            brand_3_central_kitchen
            brand_3_owned_branch_1

            brand_1_brand_2_procurement_setting
            brand_1_brand_3_procurement_setting

            central_kitchen.update!(
              other_brand_central_kitchen_ids: [
                brand_3_central_kitchen.id
              ]
            )

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all ck only' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] }).to eq(
              [
                central_kitchen_2.name.to_s,
                brand_3_central_kitchen.name,
                brand_2_central_kitchen.name.to_s,
                vendor_2.name
              ]
            )
          end
        end

        context 'when multibrand_fulfillment, buyer is internal non franchise' do
          let(:multibrand_fulfillment) { 'true' }
          let(:location_from_id) { owned_branch_1.id.to_s }
          let(:location_from_type) { 'Location' }

          before do |example|
            central_kitchen

            vendor_1
            vendor_2.location_ids = []
            vendor_2.save
            owned_branch_1.update!(
              central_kitchen_ids: owned_branch_1.central_kitchen_ids
            )

            central_kitchen_2
            franchise_branch_1

            brand_2_central_kitchen
            brand_2_franchise_branch_1

            brand_3_central_kitchen
            brand_3_owned_branch_1

            brand_1_brand_2_procurement_setting
            brand_1_brand_3_procurement_setting

            owned_branch_1.update!(
              other_brand_central_kitchen_ids: [
                brand_3_central_kitchen.id
              ]
            )

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            brand_2_central_kitchen # created but not set as other_brand_central_kitchen_ids

            submit_request(example.metadata)
          end

          it 'should be able to return selected ck only' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] }).to eq(
              [
                central_kitchen.name.to_s,
                brand_3_central_kitchen.name,
                vendor_2.name
              ]
            )
          end
        end

        context 'when multibrand_fulfillment, buyer is internal franchise' do
          let(:multibrand_fulfillment) { 'true' }
          let(:location_from_id) { franchise_branch_1.id.to_s }
          let(:location_from_type) { 'Location' }

          before do |example|
            central_kitchen

            vendor_1
            vendor_2.location_ids = []
            vendor_2.save
            owned_branch_1.update!(
              central_kitchen_ids: owned_branch_1.central_kitchen_ids
            )

            central_kitchen_2
            franchise_branch_1

            brand_2_central_kitchen
            brand_2_franchise_branch_1

            brand_3_central_kitchen
            brand_3_owned_branch_1

            brand_1_brand_2_procurement_setting
            brand_1_brand_3_procurement_setting

            franchise_branch_1.update(
              other_brand_central_kitchen_ids: [
                brand_3_central_kitchen.id,
                brand_2_central_kitchen.id
              ]
            )

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all ck only' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] }).to eq(
              [
                central_kitchen.name,
                brand_3_central_kitchen.name,
                brand_2_central_kitchen.name.to_s,
                vendor_2.name,
                vendor_1.name
              ]
            )
          end
        end

        context "when location from isn't specified" do
          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1
            vendor_2
            owned_branch_1
            owned_branch_2
            franchise_branch_1
            franchise_branch_2

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all locations & vendors' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(8)
          end
        end

        context "when location from isn't specified and is_bulk_order is true" do
          let(:is_bulk_order) { 'true' }

          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1
            vendor_2
            owned_branch_1
            owned_branch_2
            franchise_branch_1
            franchise_branch_2

            owner.selected_brand = brand
            owner.locations_users.where(location: central_kitchen_2).delete_all

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return ck type only' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].size).to eql(1)

            location_names = response_body['data'].map { |location| location['name'] }
            expect(location_names).to match_array(
              ["Central Kitchen Location Pasar Jeruk"]
            )
          end
        end

        context "when location from isn't specified, and some locations & vendors are inactive" do
          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1.update!(status: 1)
            vendor_2
            owned_branch_1.update!(status: 1)
            owned_branch_2
            franchise_branch_1
            franchise_branch_2

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].size).to eql(6)
            expect(response_body['data'].map { |data| data['name'] }).to_not include([owned_branch_1.name.to_s, vendor_1.name.to_s])
          end
        end

        context "when location from isn't specified, and some locations are inactive" do
          before do |example|
            central_kitchen
            central_kitchen_2
            owned_branch_1.update!(status: 1)
            owned_branch_2
            franchise_branch_1
            franchise_branch_2

            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should be able to return all locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].size).to eql(5)
            expect(response_body['data'].map { |data| data['name'] }).to_not include([owned_branch_1.name.to_s])
          end
        end

        context 'when location from is specified' do
          before do
            central_kitchen
            central_kitchen_2
            owned_branch_1.update!(
              central_kitchen_ids: owned_branch_1.central_kitchen_ids
            )
            owned_branch_2
            vendor_1.location_ids = [central_kitchen.id.to_s]
            vendor_1.save
            vendor_2.location_ids = []
            vendor_2.save
            franchise_branch_1.update!(
              central_kitchen_ids: franchise_branch_1.central_kitchen_ids
            )
            franchise_branch_2

            owned_branch_2.update!(
              central_kitchen_ids: owned_branch_2.central_kitchen_ids
            )
          end

          context 'when location from CK' do
            let(:location_from_id) { central_kitchen.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              central_kitchen_3
              owner.selected_brand = brand
              owner.locations_users.find_by(location: central_kitchen_3).delete

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations regardless of access' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(4)
              expect(response_body['data'].map { |data| data['name'] }).to_not include([central_kitchen.name.to_s])
              expect(response_body['data'].map do |data|
                       data['name']
                     end).to match_array([central_kitchen_2.name.to_s, central_kitchen_3.name.to_s, vendor_1.name.to_s, vendor_2.name.to_s])
            end
          end

          context 'when location from CK not allow external vendor' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              vendor_1.location_ids = [owned_branch_1.id.to_s]
              vendor_1.save
              vendor_2.location_ids = [owned_branch_1.id.to_s]
              vendor_2.save
              owned_branch_1.update!(allow_external_vendor: false)

              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should not return vendors' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body).to eq(
                {"data"=>
                [{"id"=>central_kitchen.id,
                  "name"=>central_kitchen.name,
                  "initial"=>central_kitchen.initial,
                  "branch_type"=>"central_kitchen",
                  "is_franchise"=>false,
                  "brand_id"=>brand.id,
                  "type"=>"Location"}],
                "paging"=>{"current_page"=>1, "total_item"=>1}}
              )
            end
          end

          context 'when location from outlet but not enable outlet to outlet' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              franchise_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              franchise_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(2)
              expect(response_body['data'].map { |data| data['name'] }).to match_array([central_kitchen.name.to_s, vendor_2.name.to_s])
            end
          end

          context 'when location from outlet and has multiple ck' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              owned_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id] + owned_branch_1.central_kitchen_ids)

              Vendor.reindex
              Location.reindex # manually reindex
              Location.search_index.refresh

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(3)
              expect(response_body['data'].map { |data| data['name'] }).to_not include([owned_branch_1.name.to_s])
              expect(response_body['data'].map { |data| data['name'] }).to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, central_kitchen_2.name.to_s])
            end
          end

          context 'when location from outlet and has multiple ck only' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              owned_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id])

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(2)
              expect(response_body['data'].map { |data| data['name'] }).to_not include([owned_branch_1.name.to_s])
              expect(response_body['data'].map { |data| data['name'] }).to match_array([vendor_2.name.to_s, central_kitchen_2.name.to_s])
            end
          end

          context 'when no ck1' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              owned_branch_1.update!(central_kitchen_ids: [])

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(1)
              expect(response_body['data'].map { |data| data['name'] }).to_not include([owned_branch_1.name.to_s])
              expect(response_body['data'].map { |data| data['name'] }).to match_array([vendor_2.name.to_s])
            end
          end

          context 'when location from outlet & enable outlet to outlet' do
            let(:location_from_id) { owned_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              franchise_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              franchise_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              owned_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(3)
              expect(response_body['data'].map { |data| data['name'] }).to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, owned_branch_2.name.to_s])
            end
          end

          context 'when location from franchise but not enable outlet to outlet' do
            let(:location_from_id) { franchise_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              franchise_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              owned_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(2)
              expect(response_body['data'].map { |data| data['name'] }).to match_array([central_kitchen.name.to_s, vendor_2.name.to_s])
            end
          end

          context 'when location from franchise and enable outlet to outlet, but not franchise to franchise' do
            let(:location_from_id) { franchise_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              franchise_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              franchise_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              owned_branch_1.update!(procurement_enable_outlet_to_outlet: true)
              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(4)
              expect(response_body['data'].map { |data| data['name'] }).to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, owned_branch_1.name.to_s, owned_branch_2.name.to_s])
            end
          end

          context 'when location from franchise and enable outlet to outlet, and franchise to franchise' do
            let(:location_from_id) { franchise_branch_1.id.to_s }
            let(:location_from_type) { 'Location' }
            let(:procurement_setting) {
              {
                procurement_enable_outlet_to_outlet: true,
                procurement_enable_franchise_to_franchise: true
              }
            }

            before do |example|
              franchise_branch_1.update_columns(**procurement_setting)
              franchise_branch_2.update_columns(**procurement_setting)

              owned_branch_1.update_columns(**procurement_setting)
              owned_branch_2.update_columns(**procurement_setting)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should be able to return all ck, outlets and franchises' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].size).to eql(5)
              expect(response_body['data'].map { |data| data['name'] })
                .to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, owned_branch_1.name.to_s,
                                 owned_branch_2.name.to_s, franchise_branch_2.name.to_s])
            end
          end

          context 'when location from franchise and enable outlet to outlet, and franchise to franchise,
            and one location_to didnt enable franchise_to_franchise' do
              let(:location_from_id) { franchise_branch_1.id.to_s }
              let(:location_from_type) { 'Location' }
              let(:procurement_setting) {
                  {
                  procurement_enable_outlet_to_outlet: true,
                  procurement_enable_franchise_to_franchise: true
                  }
              }

              before do |example|
                franchise_branch_1.update_columns(**procurement_setting)
                franchise_branch_2.update_columns(**procurement_setting)
                franchise_branch_2.update_columns(procurement_enable_franchise_to_franchise: false)

                owned_branch_1.update_columns(**procurement_setting)
                owned_branch_2.update_columns(**procurement_setting)

                Vendor.reindex
                Location.reindex # manually reindex

                submit_request(example.metadata)
              end

              it 'should be able to return all ck and outlets' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'].size).to eql(4)
                  expect(response_body['data'].map { |data| data['name'] })
                  .to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, owned_branch_1.name.to_s,
                                      owned_branch_2.name.to_s])
              end
          end

          context 'when procurement_enable_sell_to_customer is true' do
            let(:procurement_enable_sell_to_customer) { 'true' }

            before do |example|
              central_kitchen
              central_kitchen_2.update!(procurement_enable_sell_to_customer: true)
              owned_branch_1.update!(procurement_enable_sell_to_customer: true)
              owned_branch_2
              vendor_1.location_ids = [central_kitchen.id.to_s]
              vendor_1.save
              vendor_2.location_ids = []
              vendor_2.save
              franchise_branch_1
              franchise_branch_2

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should correctly return location where procurement_enable_sell_to_customer is true' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].map do |data|
                data['name']
              end).to match_array([central_kitchen_2.name.to_s, owned_branch_1.name.to_s])
            end
          end

          context 'when location from enable outlet to outlet' do
            let(:location_from_id) { owned_branch_2.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should return ck together with the outlets that enable outlet to outlet' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].map { |data| data['name'] })
                .to match_array([central_kitchen.name.to_s, vendor_2.name.to_s, owned_branch_3.name.to_s])
            end
          end

          context 'when location from enable outlet to outlet but no others' do
            let(:location_from_id) { owned_branch_2.id.to_s }
            let(:location_from_type) { 'Location' }

            before do |example|
              owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

              Vendor.reindex
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'should return ck and vendor only' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'].map { |data| data['name'] })
                .to match_array([central_kitchen.name.to_s, vendor_2.name.to_s])
            end
          end
        end

        context "when location from isn't specified and exclude_vendor is true" do
          let(:exclude_vendor) { 'true' }

          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1
            vendor_2
            owned_branch_1
            owned_branch_2
            franchise_branch_1
            franchise_branch_2

            Location.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should be able to return all locations & vendors' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].map { |data| data['name']}).to eql(
              [central_kitchen_2.name, central_kitchen.name,
               franchise_branch_1.name, franchise_branch_2.name,
               owned_branch_1.name, owned_branch_2.name]
            )
          end
        end

        context 'when filtered using partial keyword' do
          let(:order_type) { 'fulfillment' }
          let(:location_from_id) { franchise_branch_1.id }
          let(:location_from_type) { 'Location' }
          let(:keyword) { 'sudir' }

          before do |example|
            central_kitchen
            central_kitchen_2
            vendor_1
            vendor_2
            owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update_columns(procurement_enable_outlet_to_outlet: true)
            franchise_branch_1.update!(central_kitchen_ids: [central_kitchen.id, central_kitchen_2.id],
                                       procurement_enable_outlet_to_outlet: true, procurement_enable_franchise_to_franchise: true)
            franchise_branch_2

            Vendor.reindex # manually reindex
            Location.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should return only vendors and internal locations' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].size).to eql(1)
            expect(response_body['data'].map { |data| data['name'] }).to match_array([
              owned_branch_2.name.to_s
            ])
          end
        end
      end
    end
  end

  path '/api/orders/available_delivery_location_to' do
    get('available_delivery_location_to order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_from_id, in: :query, type: :string, required: false
      parameter name: :location_from_type, in: :query, type: :string, required: false
      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_order_available_delivery_location_to'

        context 'default' do
          let(:location_from_id) { central_kitchen.id }
          let(:location_from_type) { 'Location' }

          before do |example|
            other_order
            owned_branch_2
            order_owned_branch_3
            other_order.approve(employee)
            order_owned_branch_3.approve(employee)
            employee_access_list.location_permission['order']['index'] = true
            employee_access_list.save!

            location_user = LocationsUser.find_by(user_id: employee.id)
            location_user.access_list = sub_branch_permission
            location_user.save

            Location.reindex
            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            response_body = JSON.parse(response.body)

            location_ids = response_body['data'].map { |x| x['id'] }
            expect(location_ids).to include(owned_branch_2.id)
            expect(location_ids).to include(owned_branch_3.id)
          end
        end

        context 'when has order from CK to CK' do
          before do |example|
            order_transaction_location_from_is_franchise.update!(status: 1)
            order_transaction_ck_to_ck.update!(status: 1)

            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns location to CK' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].map do |data|
                     data['name']
                   end).to match_array([order_transaction_location_from_is_franchise.location_from_name.to_s,
                                        order_transaction_ck_to_ck.location_from_name.to_s])
          end
        end

        context 'when location from is specified, and order has order from CK to CK' do
          let(:location_from_id) { central_kitchen_2.id }
          let(:location_from_type) { 'Location' }

          before do |example|
            order_transaction_location_from_is_franchise.update!(status: 1)
            order_transaction_ck_to_ck.update!(status: 1)

            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns location to CK' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].map { |data| data['name'] }).to eql([order_transaction_ck_to_ck.location_from_name.to_s])
          end
        end

        context 'when has order from outlet to outlet' do
          before do |example|
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

            order_transaction_outlet_to_outlet.update!(status: 'processing')

            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns location from outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] })
              .to match_array([owned_branch_3.name])
          end
        end
      end
    end
  end

  path '/api/orders/available_delivery_location_from' do
    get('available_delivery_location_from order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_order_available_delivery_location_from'

        context 'when has order from CK to CK' do
          before do |example|
            order_transaction_location_from_is_franchise.update!(status: 1)
            order_transaction_ck_to_ck.update!(status: 1)

            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns location from CK' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data'].map { |data| data['name'] }).to match_array([order_transaction_location_from_is_franchise.location_to_name,
                                                                                      order_transaction_ck_to_ck.location_to_name])
          end
        end

        context 'when has order from outlet to outlet' do
          before do |example|
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

            order_transaction_outlet_to_outlet.update!(status: 'processing')

            Location.search_index.refresh
            OrderTransaction.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns location from outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['data'].map { |data| data['name'] })
              .to match_array([owned_branch_2.name])
          end
        end
      end
    end
  end
end
