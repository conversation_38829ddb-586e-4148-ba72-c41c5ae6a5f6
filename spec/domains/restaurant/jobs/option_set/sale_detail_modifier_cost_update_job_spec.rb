require './spec/shared/customer_orders'
require './spec/shared/payment_methods'
require './spec/shared/inventory_purchase_cards'
require './spec/shared/costings'

RSpec.describe Restaurant::Jobs::OptionSet::SaleDetailModifierCostUpdateJob, type: :job, clickhouse: true do
  include_context "customer orders creations"
  include_context "payment methods creations"
  include_context "inventory_purchase_cards creations"
  include_context "costings creations"

  context 'when initially default in child' do
    let(:sale_transaction) do
      travel_to 5.days.ago

      latte_owned_branch_card_1
      few_sugar_owned_branch_card_1

      travel_back

      travel_to 1.days.ago

      sale_transaction = Delivery::Services::SaleTransactionCreator.new(completed_customer_delivery_order_with_option_set, payment_method).call

      travel_back

      sale_transaction
    end

    before do
      sale_transaction
      SaleTransaction.all.each do |sale|
        sale.sale_detail_transactions.each do |sale_detail|
          create(
              :inventory,
              product: sale_detail.product,
              location: sale.location,
              in_stock: sale_detail.quantity,
              stock_date: sale.local_sales_time,
              resource: sale,
              resource_line: sale_detail
            )
        end
      end
      costing = Costing.create!(brand_id: brand.id, location_id: nil, start_period: Time.zone.today - 7.day,
                                end_period: Time.zone.today - 1.day)
      costing_consumers_all_locations(costing)
      replicate_data_to_clickhouse!
    end

    it 'should make the option set cost to be in child' do
      sale_details_attributes = sale_transaction.reload.sale_detail_transactions.map do |sale_detail_transaction|
        {
          product_id: sale_detail_transaction.product_id,
          product_unit_id: sale_detail_transaction.product_unit_id,
          price: sale_detail_transaction.price.to_f,
          quantity: sale_detail_transaction.quantity.to_f,
          total_line_amount: sale_detail_transaction.total_line_amount.to_f,
          total_amount: sale_detail_transaction.total_amount.to_f,
          meta: sale_detail_transaction.meta,
          total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
          total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f,
          sale_product_ids: sale_detail_transaction.sale_product_ids
        }
      end

      expect(sale_details_attributes.count).to eq(2)
      expect(sale_details_attributes[0]).to eq(
        {
          :product_id=>latte.id,
          :product_unit_id=>latte.sell_unit_id,
          :price=>1800.0,
          :quantity=>1.0,
          :total_line_amount=>1800.0,
          :total_amount=>1945.0,
          :meta=>
          {"parent_rule_total_line_amount"=>"1800.0",
            "parent_rule_prorate_discount"=>"0.0",
            "parent_rule_prorate_surcharge"=>"0.0",
            "sales_by_free_of_charge"=>"0.0",
            "cost"=>"3000.0",
            "sales_feed_adjustment_notes" => "-",
            "tax_percentage_is_adjusted"=>false,
            "self_cost"=>"3000.0",
            "parent_rule_total_line_discount_prorate"=>"1800.0"
          },
          :total_amount_prorate_discount=>1945.0,
          :total_line_discount_prorate=>1800.0,
          :sale_product_ids=>[latte.id]
        }
      )
      expect(sale_details_attributes[1]).to eq(
        {
          :product_id=>cheese_burger.id,
          :product_unit_id=>cheese_burger.sell_unit_id,
          :price=>0.0,
          :quantity=>1.0,
          :total_line_amount=>0.0,
          :total_amount=>0.0,
          :meta=>
          {"parent_rule_total_line_amount"=>"0.0",
            "parent_rule_prorate_discount"=>"0.0",
            "parent_rule_prorate_surcharge"=>"0.0",
            "sales_by_free_of_charge"=>"0.0",
            "cost"=>"0.0",
            "sales_feed_adjustment_notes" => "-",
            "self_cost"=>"0.0",
            "tax_percentage_is_adjusted"=>false,
            "parent_rule_total_line_discount_prorate"=>"0.0"
          },
          :total_amount_prorate_discount=>0.0,
          :total_line_discount_prorate=>0.0,
          :sale_product_ids=>[cheese_burger.id]
        }
      )

      sale_modifiers_attributes = sale_transaction.reload.sale_detail_modifiers.map do |sale_detail_modifier|
        {
          product_id: sale_detail_modifier.product_id,
          product_unit_id: sale_detail_modifier.product_unit_id,
          price: sale_detail_modifier.price.to_f,
          quantity: sale_detail_modifier.quantity.to_f,
          total_line_amount: sale_detail_modifier.total_line_amount.to_f,
          meta: sale_detail_modifier.meta,
          total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f,
          sale_product_ids: sale_detail_modifier.sale_product_ids
        }
      end

      expect(sale_modifiers_attributes).to match_array(
        [{:product_id=>few_sugar.id,
        :product_unit_id=>few_sugar.sell_unit_id,
        :price=>80.0,
        :quantity=>1.0,
        :total_line_amount=>80.0,
        :meta=>
         {"option_set_id"=>sugar_level.id,
          "option_set_name"=>sugar_level.name,
          "option_set_option_id"=>few_sugar_level.id,
          "show_item"=>nil,
          "tax_percentage_is_adjusted"=>false,
          "parent_rule_prorate_discount"=>"0.0",
          "parent_rule_prorate_surcharge"=>"0.0",
          "parent_rule_total_line_amount"=>"80.0",
          "sales_by_free_of_charge"=>"0.0",
          "cost"=>"830.0",
          "self_cost"=>"830.0",
          "parent_rule_total_amount_prorate_discount"=>"80.0"},
        :sale_product_ids=>[few_sugar.id],
        :total_amount_prorate_discount=>80.0},
       {:product_id=>few_ice.id,
        :product_unit_id=>few_ice.sell_unit_id,
        :price=>65.0,
        :quantity=>1.0,
        :total_line_amount=>65.0,
        :meta=>
         {"option_set_id"=>ice_level.id,
          "option_set_name"=>ice_level.name,
          "option_set_option_id"=>few_ice_level.id,
          "show_item"=>nil,
          "tax_percentage_is_adjusted"=>false,
          "parent_rule_prorate_discount"=>"0.0",
          "parent_rule_prorate_surcharge"=>"0.0",
          "parent_rule_total_line_amount"=>"65.0",
          "sales_by_free_of_charge"=>"0.0",
          "cost"=>"0.0",
          "self_cost"=>"0.0",
          "parent_rule_total_amount_prorate_discount"=>"65.0"},
        :sale_product_ids=>[few_ice.id],
        :total_amount_prorate_discount=>65.0}]
      )
    end

    context 'when option set move to parent' do
      before do
        latte
        sale_transaction
        sale_transaction.sale_detail_modifiers.update_all(rule_cost_included_in_parent: true)

        sugar_level.reload.update_columns(rule_cost_included_in_parent: false)
        ice_level.reload.update_columns(rule_cost_included_in_parent: true)

        sugar_level.reload.update!(rule_cost_included_in_parent: true)
        sale_transaction.reload
        replicate_data_to_clickhouse!
      end

      it 'should move parent rule cost to parent' do
        sale_details_attributes = sale_transaction.sale_detail_transactions.order(product_id: :asc).map do |sale_detail_transaction|
          {
            product_id: sale_detail_transaction.product_id,
            product_unit_id: sale_detail_transaction.product_unit_id,
            price: sale_detail_transaction.price.to_f,
            quantity: sale_detail_transaction.quantity.to_f,
            total_line_amount: sale_detail_transaction.total_line_amount.to_f,
            total_amount: sale_detail_transaction.total_amount.to_f,
            meta: sale_detail_transaction.meta,
            total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
            total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f,
            sale_product_ids: sale_detail_transaction.sale_product_ids
          }
        end

        expect(sale_details_attributes.count).to eq(2)
        expect(sale_details_attributes[0]).to eq(
          {
            :product_id=>latte.id,
            :product_unit_id=>latte.sell_unit_id,
            :price=>1800.0,
            :quantity=>1.0,
            :total_line_amount=>1800.0,
            :total_amount=>1945.0,
            :meta=>
            {"parent_rule_total_line_amount"=>"1945.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "sales_by_free_of_charge"=>"0.0",
              "cost"=>"3830.0",
              "sales_feed_adjustment_notes" => "-",
              "self_cost"=>"3000.0",
              "tax_percentage_is_adjusted"=>false,
              "parent_rule_total_line_discount_prorate"=>"1945.0"
            },
            :total_amount_prorate_discount=>1945.0,
            :sale_product_ids=>[few_sugar.id, few_ice.id, latte.id],
            :total_line_discount_prorate=>1800.0
          }
        )
        expect(sale_details_attributes[1]).to eq(
          {
            :product_id=>cheese_burger.id,
            :product_unit_id=>cheese_burger.sell_unit_id,
            :price=>0.0,
            :quantity=>1.0,
            :total_line_amount=>0.0,
            :total_amount=>0.0,
            :meta=> {
              "cost"=>"0.0",
              "self_cost"=>"0.0",
              "sales_feed_adjustment_notes" => "-",
              "sales_by_free_of_charge"=>"0.0",
              "parent_rule_total_line_amount"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "tax_percentage_is_adjusted"=>false,
              "parent_rule_total_line_discount_prorate"=>"0.0"},
            :total_amount_prorate_discount=>0.0,
            :sale_product_ids=>[cheese_burger.id],
            :total_line_discount_prorate=>0.0
          }
        )

        sale_modifiers_attributes = sale_transaction.sale_detail_modifiers.map do |sale_detail_modifier|
          sale_detail_modifier.meta.merge!(sale_detail_modifier.parent_rule_metadata)

          {
            product_id: sale_detail_modifier.product_id,
            product_unit_id: sale_detail_modifier.product_unit_id,
            price: sale_detail_modifier.price.to_f,
            quantity: sale_detail_modifier.quantity.to_f,
            total_line_amount: sale_detail_modifier.total_line_amount.to_f,
            meta: sale_detail_modifier.meta,
            total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f,
            sale_product_ids: sale_detail_modifier.sale_product_ids
          }
        end

        expect(sale_modifiers_attributes).to match_array(
          [{:product_id=>few_sugar.id,
          :product_unit_id=>few_sugar.sell_unit_id,
          :price=>80.0,
          :quantity=>1.0,
          :total_line_amount=>80.0,
          :meta=>
           {
            "cost"=>"0.0",
            "self_cost"=>"830.0",
            "show_item"=>nil,
            "option_set_id"=>sugar_level.id,
            "option_set_name"=>"Sugar Level",
            "option_set_option_id"=>sugar_level.option_set_options.detect { |row| row.product_id == few_sugar.id }.id,
            "sales_by_free_of_charge"=>"0.0",
            "tax_percentage_is_adjusted"=>false,
            "parent_rule_prorate_discount"=>"0.0",
            "parent_rule_prorate_surcharge"=>"0.0",
            "parent_rule_total_line_amount"=>"0.0",
            "parent_rule_total_amount_prorate_discount"=>"0.0",
            :parent_rule_total_line_amount=>0.0,
            :parent_rule_prorate_discount=>0.0,
            :parent_rule_prorate_surcharge=>0.0,
            :sales_by_free_of_charge=>0.0,
            :parent_rule_total_amount_prorate_discount=>0.0
          },
          :sale_product_ids=>[few_sugar.id, latte.id],
          :total_amount_prorate_discount=>80.0},
         {:product_id=>few_ice.id,
          :product_unit_id=>few_ice.sell_unit_id,
          :price=>65.0,
          :quantity=>1.0,
          :total_line_amount=>65.0,
          :meta=>
           {"option_set_id"=>ice_level.id,
            "option_set_name"=>ice_level.name,
            "option_set_option_id"=>few_ice_level.id,
            "cost"=>"0.0",
            "self_cost"=>"0.0",
            "show_item"=>nil,
            "sales_by_free_of_charge"=>"0.0",
            "tax_percentage_is_adjusted"=>false,
            "parent_rule_prorate_discount"=>"0.0",
            "parent_rule_prorate_surcharge"=>"0.0",
            "parent_rule_total_line_amount"=>"65.0",
            "parent_rule_total_amount_prorate_discount"=>"65.0",
            :parent_rule_total_line_amount=>0.0,
            :parent_rule_prorate_discount=>0.0,
            :parent_rule_prorate_surcharge=>0.0,
            :sales_by_free_of_charge=>0.0,
            :parent_rule_total_amount_prorate_discount=>0.0},
          :sale_product_ids=>[few_ice.id, latte.id],
          :total_amount_prorate_discount=>65.0}]
        )
      end

      context 'when option set move to child again' do
        before do
          sale_transaction
          sale_transaction.sale_detail_modifiers.each { |sale_detail_modifier| sale_detail_modifier.update_columns(rule_cost_included_in_parent: false) }

          sugar_level.reload.update_columns(rule_cost_included_in_parent: true)
          ice_level.reload.update_columns(rule_cost_included_in_parent: false)

          sugar_level.reload.update!(rule_cost_included_in_parent: false)
          sale_transaction.reload
          replicate_data_to_clickhouse!
        end

        it 'should make the option set cost to be in child' do
          sale_details_attributes = sale_transaction.reload.sale_detail_transactions.order(product_id: :asc).map do |sale_detail_transaction|
            {
              product_id: sale_detail_transaction.product_id,
              product_unit_id: sale_detail_transaction.product_unit_id,
              price: sale_detail_transaction.price.to_f,
              quantity: sale_detail_transaction.quantity.to_f,
              total_line_amount: sale_detail_transaction.total_line_amount.to_f,
              total_amount: sale_detail_transaction.total_amount.to_f,
              meta: sale_detail_transaction.meta,
              total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
              total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f,
              sale_product_ids: sale_detail_transaction.sale_product_ids
            }
          end

          expect(sale_details_attributes.count).to eq(2)
          expect(sale_details_attributes[0]).to eq(
            {
              :product_id=>latte.id,
              :product_unit_id=>latte.sell_unit_id,
              :price=>1800.0,
              :quantity=>1.0,
              :total_line_amount=>1800.0,
              :total_amount=>1945.0,
              :meta=> {
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_amount"=>"1800.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "sales_feed_adjustment_notes" => "-",
                "tax_percentage_is_adjusted"=>false,
                "cost"=>"3000.0",
                "self_cost"=>"3000.0",
                "parent_rule_total_line_discount_prorate"=>"1800.0"
              },
              :total_amount_prorate_discount=>1945.0,
              :total_line_discount_prorate=>1800.0,
              :sale_product_ids=>[latte.id]
            }
          )
          expect(sale_details_attributes[1]).to eq(
            {
              :product_id=>cheese_burger.id,
              :product_unit_id=>cheese_burger.sell_unit_id,
              :price=>0.0,
              :quantity=>1.0,
              :total_line_amount=>0.0,
              :total_amount=>0.0,
              :meta=> {
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_amount"=>"0.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "sales_feed_adjustment_notes" => "-",
                "tax_percentage_is_adjusted"=>false,
                "cost"=>"0.0",
                "self_cost"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"0.0"
              },
              :total_amount_prorate_discount=>0.0,
              :total_line_discount_prorate=>0.0,
              :sale_product_ids=>[cheese_burger.id]
            }
          )

          sale_modifiers_attributes = sale_transaction.reload.sale_detail_modifiers.map do |sale_detail_modifier|
            {
              product_id: sale_detail_modifier.product_id,
              product_unit_id: sale_detail_modifier.product_unit_id,
              price: sale_detail_modifier.price.to_f,
              quantity: sale_detail_modifier.quantity.to_f,
              total_line_amount: sale_detail_modifier.total_line_amount.to_f,
              meta: sale_detail_modifier.meta,
              total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f,
              sale_product_ids: sale_detail_modifier.sale_product_ids
            }
          end

          expect(sale_modifiers_attributes).to match_array(
            [{:product_id=>few_sugar.id,
            :product_unit_id=>few_sugar.sell_unit_id,
            :price=>80.0,
            :quantity=>1.0,
            :total_line_amount=>80.0,
            :meta=>
             {"option_set_id"=>sugar_level.id,
              "option_set_name"=>sugar_level.name,
              "option_set_option_id"=>few_sugar_level.id,
              "show_item"=>nil,
              "tax_percentage_is_adjusted"=>false,
              "sales_by_free_of_charge"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"80.0",
              "cost"=>"830.0",
              "self_cost"=>"830.0",
              "parent_rule_total_amount_prorate_discount"=>"80.0"},
            :sale_product_ids=>[few_sugar.id],
            :total_amount_prorate_discount=>80.0},
           {:product_id=>few_ice.id,
            :product_unit_id=>few_ice.sell_unit_id,
            :price=>65.0,
            :quantity=>1.0,
            :total_line_amount=>65.0,
            :meta=>
             {"option_set_id"=>ice_level.id,
              "option_set_name"=>ice_level.name,
              "option_set_option_id"=>few_ice_level.id,
              "show_item"=>nil,
              "tax_percentage_is_adjusted"=>false,
              "sales_by_free_of_charge"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"65.0",
              "cost"=>"0.0",
              "self_cost"=>"0.0",
              "parent_rule_total_amount_prorate_discount"=>"65.0"},
            :sale_product_ids=>[few_ice.id],
            :total_amount_prorate_discount=>65.0}]
          )
        end

        context 'when option set move to child again' do
          before do
            sale_transaction
            sale_transaction.sale_detail_modifiers.each { |sale_detail_modifier| sale_detail_modifier.update_columns(rule_cost_included_in_parent: false) }

            sugar_level.reload.update_columns(rule_cost_included_in_parent: true)
            ice_level.reload.update_columns(rule_cost_included_in_parent: false)

            sugar_level.reload.update!(rule_cost_included_in_parent: false)
            sale_transaction.reload
            replicate_data_to_clickhouse!
          end

          it 'should make the option set cost to be in child' do
            sale_details_attributes = sale_transaction.reload.sale_detail_transactions.order(product_id: :asc).map do |sale_detail_transaction|
              {
                product_id: sale_detail_transaction.product_id,
                product_unit_id: sale_detail_transaction.product_unit_id,
                price: sale_detail_transaction.price.to_f,
                quantity: sale_detail_transaction.quantity.to_f,
                total_line_amount: sale_detail_transaction.total_line_amount.to_f,
                total_amount: sale_detail_transaction.total_amount.to_f,
                meta: sale_detail_transaction.meta,
                total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
                total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f,
                sale_product_ids: sale_detail_transaction.sale_product_ids
              }
            end

            expect(sale_details_attributes.count).to eq(2)
            expect(sale_details_attributes[0]).to eq(
              {
                :product_id=>latte.id,
                :product_unit_id=>latte.sell_unit_id,
                :price=>1800.0,
                :quantity=>1.0,
                :total_line_amount=>1800.0,
                :total_amount=>1945.0,
                :meta=> {
                  "parent_rule_total_line_amount"=>"1800.0",
                  "parent_rule_prorate_discount"=>"0.0",
                  "parent_rule_prorate_surcharge"=>"0.0",
                  "sales_feed_adjustment_notes" => "-",
                  "sales_by_free_of_charge"=>"0.0",
                  "tax_percentage_is_adjusted"=>false,
                  "cost"=>"3000.0",
                  "self_cost"=>"3000.0",
                  "parent_rule_total_line_discount_prorate"=>"1800.0"
                },
                :total_amount_prorate_discount=>1945.0,
                :total_line_discount_prorate=>1800.0,
                :sale_product_ids=>[latte.id]
              }
            )
            expect(sale_details_attributes[1]).to eq(
              {
                :product_id=>cheese_burger.id,
                :product_unit_id=>cheese_burger.sell_unit_id,
                :price=>0.0,
                :quantity=>1.0,
                :total_line_amount=>0.0,
                :total_amount=>0.0,
                :meta=> {
                  "parent_rule_total_line_amount"=>"0.0",
                  "parent_rule_prorate_discount"=>"0.0",
                  "parent_rule_prorate_surcharge"=>"0.0",
                  "sales_feed_adjustment_notes" => "-",
                  "sales_by_free_of_charge"=>"0.0",
                  "cost"=>"0.0",
                  "self_cost"=>"0.0",
                  "tax_percentage_is_adjusted"=>false,
                  "parent_rule_total_line_discount_prorate"=>"0.0"
                },
                :total_amount_prorate_discount=>0.0,
                :total_line_discount_prorate=>0.0,
                :sale_product_ids=>[cheese_burger.id]
              }
            )

            sale_modifiers_attributes = sale_transaction.reload.sale_detail_modifiers.map do |sale_detail_modifier|
              {
                product_id: sale_detail_modifier.product_id,
                product_unit_id: sale_detail_modifier.product_unit_id,
                price: sale_detail_modifier.price.to_f,
                quantity: sale_detail_modifier.quantity.to_f,
                total_line_amount: sale_detail_modifier.total_line_amount.to_f,
                meta: sale_detail_modifier.meta,
                total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f,
                sale_product_ids: sale_detail_modifier.sale_product_ids
              }
            end.index_by { |modifier| modifier[:product_id] }

            expect(sale_modifiers_attributes.count).to eq(2)
            expect(sale_modifiers_attributes[few_ice.id]).to eq({
              :product_id=>few_ice.id,
              :product_unit_id=>few_ice.sell_unit_id,
              :price=>65.0,
              :quantity=>1.0,
              :total_line_amount=>65.0,
              :meta=> {
                "option_set_id"=>ice_level.id,
                "option_set_name"=>ice_level.name,
                "option_set_option_id"=>few_ice_level.id,
                "show_item"=>nil,
                "tax_percentage_is_adjusted"=>false,
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "parent_rule_total_line_amount"=>"65.0",
                "sales_by_free_of_charge"=>"0.0",
                "cost"=>"0.0",
                "self_cost"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>"65.0"
              },
              :sale_product_ids=>[few_ice.id],
              :total_amount_prorate_discount=>65.0
            })
            expect(sale_modifiers_attributes[few_sugar.id]).to eq({
              :product_id=>few_sugar.id,
              :product_unit_id=>few_sugar.sell_unit_id,
              :price=>80.0,
              :quantity=>1.0,
              :total_line_amount=>80.0,
              :meta=> {
                "option_set_id"=>sugar_level.id,
                "option_set_name"=>sugar_level.name,
                "option_set_option_id"=>few_sugar_level.id,
                "show_item"=>nil,
                "tax_percentage_is_adjusted"=>false,
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "parent_rule_total_line_amount"=>"80.0",
                "sales_by_free_of_charge"=>"0.0",
                "cost"=>"830.0",
                "self_cost"=>"830.0",
                "parent_rule_total_amount_prorate_discount"=>"80.0"
              },
              :sale_product_ids=>[few_sugar.id],
              :total_amount_prorate_discount=>80.0
            })
          end
        end
      end

      context 'when option set move to parent again' do
        before do
          latte
          sale_transaction.sale_detail_modifiers.each { |sale_detail_modifier| sale_detail_modifier.update_columns(rule_cost_included_in_parent: true) }
          sugar_level.reload.update_columns(rule_cost_included_in_parent: false)
          ice_level.reload.update_columns(rule_cost_included_in_parent: true)

          sugar_level.reload.update!(rule_cost_included_in_parent: true)
          sale_transaction.reload
          replicate_data_to_clickhouse!
        end

        it 'should move parent rule cost to parent' do
          sale_details_attributes = sale_transaction.sale_detail_transactions.order(product_id: :asc).map do |sale_detail_transaction|
            {
              product_id: sale_detail_transaction.product_id,
              product_unit_id: sale_detail_transaction.product_unit_id,
              price: sale_detail_transaction.price.to_f,
              quantity: sale_detail_transaction.quantity.to_f,
              total_line_amount: sale_detail_transaction.total_line_amount.to_f,
              total_amount: sale_detail_transaction.total_amount.to_f,
              meta: sale_detail_transaction.meta,
              total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
              total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f,
              sale_product_ids: sale_detail_transaction.sale_product_ids.sort
            }
          end

          expect(sale_details_attributes).to match_array(
            [{:product_id=>latte.id,
              :product_unit_id=>latte.sell_unit_id,
              :price=>1800.0,
              :quantity=>1.0,
              :total_line_amount=>1800.0,
              :total_amount=>1945.0,
              :meta=>
              {"parent_rule_total_line_amount"=>"1945.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "sales_by_free_of_charge"=>"0.0",
                "sales_feed_adjustment_notes" => "-",
                "tax_percentage_is_adjusted"=>false,
                "cost"=>"3830.0",
                "self_cost"=>"3000.0",
                "parent_rule_total_line_discount_prorate"=>"1945.0"},
              :total_amount_prorate_discount=>1945.0,
              :sale_product_ids=>[few_sugar.id, few_ice.id, latte.id].sort,
              :total_line_discount_prorate=>1800.0},
            {:product_id=>cheese_burger.id,
              :product_unit_id=>cheese_burger.sell_unit_id,
              :price=>0.0,
              :quantity=>1.0,
              :total_line_amount=>0.0,
              :total_amount=>0.0,
              :meta=>
              {"parent_rule_total_line_amount"=>"0.0",
                "cost"=>"0.0",
                "self_cost"=>"0.0",
                "sales_feed_adjustment_notes" => "-",
                "tax_percentage_is_adjusted"=>false,
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"0.0"},
              :total_amount_prorate_discount=>0.0,
              :sale_product_ids=>[cheese_burger.id].sort,
              :total_line_discount_prorate=>0.0}]
          )

          sale_modifiers_attributes = sale_transaction.sale_detail_modifiers.map do |sale_detail_modifier|
            sale_detail_modifier.meta.merge!(sale_detail_modifier.parent_rule_metadata)

            {
              product_id: sale_detail_modifier.product_id,
              product_unit_id: sale_detail_modifier.product_unit_id,
              price: sale_detail_modifier.price.to_f,
              quantity: sale_detail_modifier.quantity.to_f,
              total_line_amount: sale_detail_modifier.total_line_amount.to_f,
              meta: sale_detail_modifier.meta,
              total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f,
              sale_product_ids: sale_detail_modifier.sale_product_ids
            }
          end.index_by { |x| x[:product_id]}

          expect(sale_modifiers_attributes.count).to eq(2)

          expect(sale_modifiers_attributes[few_sugar.id]).to eq({
            :product_id=>few_sugar.id,
            :product_unit_id=>few_sugar.sell_unit_id,
            :price=>80.0,
            :quantity=>1.0,
            :total_line_amount=>80.0,
            :meta=> {
              "option_set_id"=>sugar_level.id,
              "option_set_name"=>sugar_level.name,
              "option_set_option_id"=>few_sugar_level.id,
              "cost"=>"0.0",
              "self_cost"=>"830.0",
              "show_item"=>nil,
              "sales_by_free_of_charge"=>"0.0",
              "tax_percentage_is_adjusted"=>false,
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"0.0",
              "parent_rule_total_amount_prorate_discount"=>"0.0",
              :parent_rule_total_line_amount=>0.0,
              :parent_rule_prorate_discount=>0.0,
              :parent_rule_prorate_surcharge=>0.0,
              :sales_by_free_of_charge=>0.0,
              :parent_rule_total_amount_prorate_discount=>0.0
            },
            :sale_product_ids=>[few_sugar.id, latte.id],
            :total_amount_prorate_discount=>80.0
          })

          expect(sale_modifiers_attributes[few_ice.id]).to eq({
            :product_id=>few_ice.id,
            :product_unit_id=>few_ice.sell_unit_id,
            :price=>65.0,
            :quantity=>1.0,
            :total_line_amount=>65.0,
            :meta=> {
              "option_set_id"=>ice_level.id,
              "option_set_name"=>ice_level.name,
              "option_set_option_id"=>few_ice_level.id,
              "cost"=>"0.0",
              "self_cost"=>"0.0",
              "show_item"=>nil,
              "sales_by_free_of_charge"=>"0.0",
              "tax_percentage_is_adjusted"=>false,
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"65.0",
              "parent_rule_total_amount_prorate_discount"=>"65.0",
              :parent_rule_total_line_amount=>0.0,
              :parent_rule_prorate_discount=>0.0,
              :parent_rule_prorate_surcharge=>0.0,
              :sales_by_free_of_charge=>0.0,
              :parent_rule_total_amount_prorate_discount=>0.0
            },
            :sale_product_ids=>[few_ice.id, latte.id],
            :total_amount_prorate_discount=>65.0
          })
        end
      end
    end
  end

  context 'when initially default in child and meta is nil' do
    let(:sale_transaction) do
      Delivery::Services::SaleTransactionCreator.new(completed_customer_delivery_order_with_option_set, payment_method).call
    end

    before do
      sale_transaction
      sale_transaction.sale_detail_transactions.each do |sale_detail_transaction|
        sale_detail_transaction.update_columns(meta: nil)
      end

      sale_transaction.sale_detail_modifiers.each do |sale_detail_modifier|
        sale_detail_modifier.update_columns(meta: nil)
      end
      replicate_data_to_clickhouse!
    end

    it 'should make the option set cost to be in child' do
      sale_details_attributes = sale_transaction.sale_detail_transactions.map do |sale_detail_transaction|
        {
          product_id: sale_detail_transaction.product_id,
          product_unit_id: sale_detail_transaction.product_unit_id,
          price: sale_detail_transaction.price.to_f,
          quantity: sale_detail_transaction.quantity.to_f,
          total_line_amount: sale_detail_transaction.total_line_amount.to_f,
          total_amount: sale_detail_transaction.total_amount.to_f,
          meta: sale_detail_transaction.meta,
          total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
          total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f
        }
      end

      expect(sale_details_attributes).to match_array(
        [{:product_id=>latte.id,
          :product_unit_id=>latte.sell_unit_id,
          :price=>1800.0,
          :quantity=>1.0,
          :total_line_amount=>1800.0,
          :total_amount=>1945.0,
          :meta=>nil,
          :total_amount_prorate_discount=>1945.0,
          :total_line_discount_prorate=>1800.0},
        {:product_id=>cheese_burger.id,
          :product_unit_id=>cheese_burger.sell_unit_id,
          :price=>0.0,
          :quantity=>1.0,
          :total_line_amount=>0.0,
          :total_amount=>0.0,
          :meta=>nil,
          :total_amount_prorate_discount=>0.0,
          :total_line_discount_prorate=>0.0}]
      )

      sale_modifiers_attributes = sale_transaction.sale_detail_modifiers.map do |sale_detail_modifier|
        {
          product_id: sale_detail_modifier.product_id,
          product_unit_id: sale_detail_modifier.product_unit_id,
          price: sale_detail_modifier.price.to_f,
          quantity: sale_detail_modifier.quantity.to_f,
          total_line_amount: sale_detail_modifier.total_line_amount.to_f,
          meta: sale_detail_modifier.meta,
          total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f
        }
      end

      expect(sale_modifiers_attributes).to match_array(
        [{:product_id=>few_sugar.id,
        :product_unit_id=>few_sugar.sell_unit_id,
        :price=>80.0,
        :quantity=>1.0,
        :total_line_amount=>80.0,
        :meta=>nil,
        :total_amount_prorate_discount=>80.0},
       {:product_id=>few_ice.id,
        :product_unit_id=>few_ice.sell_unit_id,
        :price=>65.0,
        :quantity=>1.0,
        :total_line_amount=>65.0,
        :meta=>nil,
        :total_amount_prorate_discount=>65.0}]
      )
    end

    context 'when option set move to parent' do
      before do
        latte
        sale_transaction
        sale_transaction.sale_detail_modifiers.each { |sale_detail_modifier| sale_detail_modifier.update_columns(rule_cost_included_in_parent: true) }
        sugar_level.reload.update_columns(rule_cost_included_in_parent: false)
        sugar_level.reload.update!(rule_cost_included_in_parent: true)
        sale_transaction.reload
        replicate_data_to_clickhouse!
      end

      it 'should move parent rule cost to parent' do
        sale_details_attributes = sale_transaction.sale_detail_transactions.map do |sale_detail_transaction|
          {
            product_id: sale_detail_transaction.product_id,
            product_unit_id: sale_detail_transaction.product_unit_id,
            price: sale_detail_transaction.price.to_f,
            quantity: sale_detail_transaction.quantity.to_f,
            total_line_amount: sale_detail_transaction.total_line_amount.to_f,
            total_amount: sale_detail_transaction.total_amount.to_f,
            meta: sale_detail_transaction.meta,
            total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
            total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f
          }
        end

        expect(sale_details_attributes).to match_array(
          [{:product_id=>cheese_burger.id,
            :product_unit_id=>cheese_burger.sell_unit_id,
            :price=>0.0,
            :quantity=>1.0,
            :total_line_amount=>0.0,
            :total_amount=>0.0,
            :meta=>nil,
            :total_amount_prorate_discount=>0.0,
            :total_line_discount_prorate=>0.0},
          {:product_id=>latte.id,
            :product_unit_id=>latte.sell_unit_id,
            :price=>1800.0,
            :quantity=>1.0,
            :total_line_amount=>1800.0,
            :total_amount=>1945.0,
            :meta=>
            {"sales_by_free_of_charge"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"1945.0",
              "parent_rule_total_line_discount_prorate"=>"1945.0"},
            :total_amount_prorate_discount=>1945.0,
            :total_line_discount_prorate=>1800.0}]
        )

        sale_modifiers_attributes = sale_transaction.sale_detail_modifiers.map do |sale_detail_modifier|
          sale_detail_modifier.meta.merge!(sale_detail_modifier.parent_rule_metadata)

          {
            product_id: sale_detail_modifier.product_id,
            product_unit_id: sale_detail_modifier.product_unit_id,
            price: sale_detail_modifier.price.to_f,
            quantity: sale_detail_modifier.quantity.to_f,
            total_line_amount: sale_detail_modifier.total_line_amount.to_f,
            meta: sale_detail_modifier.meta,
            total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f
          }
        end

        expect(sale_modifiers_attributes).to match_array(
          [{:product_id=>few_sugar.id,
          :product_unit_id=>few_sugar.sell_unit_id,
          :price=>80.0,
          :quantity=>1.0,
          :total_line_amount=>80.0,
          :meta=>{
            "sales_by_free_of_charge"=>"0.0",
            "parent_rule_prorate_discount"=>"0.0",
            "parent_rule_prorate_surcharge"=>"0.0",
            "parent_rule_total_line_amount"=>"0.0",
            "parent_rule_total_amount_prorate_discount"=>"0.0",
            :parent_rule_total_line_amount=>0.0,
            :parent_rule_prorate_discount=>0.0,
            :parent_rule_prorate_surcharge=>0.0,
            :sales_by_free_of_charge=>0.0,
            :parent_rule_total_amount_prorate_discount=>0.0
          },
          :total_amount_prorate_discount=>80.0},
         {:product_id=>few_ice.id,
          :product_unit_id=>few_ice.sell_unit_id,
          :price=>65.0,
          :quantity=>1.0,
          :total_line_amount=>65.0,
          :meta=>{:parent_rule_total_line_amount=>0.0,
            :parent_rule_prorate_discount=>0.0,
            :parent_rule_prorate_surcharge=>0.0,
            :sales_by_free_of_charge=>0.0,
            :parent_rule_total_amount_prorate_discount=>0.0},
          :total_amount_prorate_discount=>65.0}]
        )
      end

      context 'when option set move to child again' do
        before do
          sale_transaction
          sugar_level.reload.update!(rule_cost_included_in_parent: false)
          sale_transaction.reload
          replicate_data_to_clickhouse!
        end

        it 'should make the option set cost to be in child' do
          sale_details_attributes = sale_transaction.sale_detail_transactions.map do |sale_detail_transaction|
            sale_detail_transaction.meta ||= {}
            sale_detail_transaction.meta.merge!(sale_detail_transaction.parent_rule_metadata)

            {
              product_id: sale_detail_transaction.product_id,
              product_unit_id: sale_detail_transaction.product_unit_id,
              price: sale_detail_transaction.price.to_f,
              quantity: sale_detail_transaction.quantity.to_f,
              total_line_amount: sale_detail_transaction.total_line_amount.to_f,
              total_amount: sale_detail_transaction.total_amount.to_f,
              meta: sale_detail_transaction.meta,
              total_amount_prorate_discount: sale_detail_transaction.total_amount_prorate_discount.to_f,
              total_line_discount_prorate: sale_detail_transaction.total_line_discount_prorate.to_f
            }
          end

          expect(sale_details_attributes).to match_array(
            [{:product_id=>cheese_burger.id,
              :product_unit_id=>cheese_burger.sell_unit_id,
              :price=>0.0,
              :quantity=>1.0,
              :total_line_amount=>0.0,
              :total_amount=>0.0,
              :meta=>{:parent_rule_total_line_amount=>0.0,
                :parent_rule_prorate_discount=>0.0,
                :parent_rule_prorate_surcharge=>0.0,
                :sales_by_free_of_charge=>0.0,
                :parent_rule_total_line_discount_prorate=>0.0},
              :total_amount_prorate_discount=>0.0,
              :total_line_discount_prorate=>0.0},
            {:product_id=>latte.id,
              :product_unit_id=>latte.sell_unit_id,
              :price=>1800.0,
              :quantity=>1.0,
              :total_line_amount=>1800.0,
              :total_amount=>1945.0,
              :meta=>
              {"sales_by_free_of_charge"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"1865.0",
              "parent_rule_total_line_discount_prorate"=>"1865.0",
              :parent_rule_total_line_amount=>0.1865e4,
              :parent_rule_prorate_discount=>0.0,
              :parent_rule_prorate_surcharge=>0.0,
              :sales_by_free_of_charge=>0.0,
              :parent_rule_total_line_discount_prorate=>0.1865e4},
              :total_amount_prorate_discount=>1945.0,
              :total_line_discount_prorate=>1800.0}]
          )

          sale_modifiers_attributes = sale_transaction.sale_detail_modifiers.map do |sale_detail_modifier|
            sale_detail_modifier.meta.merge!(sale_detail_modifier.parent_rule_metadata)

            {
              product_id: sale_detail_modifier.product_id,
              product_unit_id: sale_detail_modifier.product_unit_id,
              price: sale_detail_modifier.price.to_f,
              quantity: sale_detail_modifier.quantity.to_f,
              total_line_amount: sale_detail_modifier.total_line_amount.to_f,
              meta: sale_detail_modifier.meta,
              total_amount_prorate_discount: sale_detail_modifier.total_amount_prorate_discount.to_f
            }
          end

          expect(sale_modifiers_attributes).to match_array(
            [{:product_id=>few_sugar.id,
            :product_unit_id=>few_sugar.sell_unit_id,
            :price=>80.0,
            :quantity=>1.0,
            :total_line_amount=>80.0,
            :meta=>{"sales_by_free_of_charge"=>"0.0",
              "parent_rule_prorate_discount"=>"0.0",
              "parent_rule_prorate_surcharge"=>"0.0",
              "parent_rule_total_line_amount"=>"80.0",
              "parent_rule_total_amount_prorate_discount"=>"80.0",
              :parent_rule_total_line_amount=>0.8e2,
              :parent_rule_prorate_discount=>0.0,
              :parent_rule_prorate_surcharge=>0.0,
              :sales_by_free_of_charge=>0.0,
              :parent_rule_total_amount_prorate_discount=>0.8e2},
            :total_amount_prorate_discount=>80.0},
           {:product_id=>few_ice.id,
            :product_unit_id=>few_ice.sell_unit_id,
            :price=>65.0,
            :quantity=>1.0,
            :total_line_amount=>65.0,
            :meta=>{:parent_rule_total_line_amount=>0.0,
              :parent_rule_prorate_discount=>0.0,
              :parent_rule_prorate_surcharge=>0.0,
              :sales_by_free_of_charge=>0.0,
              :parent_rule_total_amount_prorate_discount=>0.0},
            :total_amount_prorate_discount=>65.0}]
          )
        end
      end
    end
  end
end
