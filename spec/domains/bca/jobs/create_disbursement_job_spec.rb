require './spec/shared/access_lists'
require './spec/shared/bca.rb'

describe BCA::Jobs::CreateDisbursementJob, type: :job do
  include_context 'access lists creations'
  include_context 'BCA setup'

  let!(:owner) { create(:hq_owner) }
  let(:brand) { owner.active_brand }
  let(:central_kitchen_location) do
    build(
      :location_params, {
      name: "Central & Kitchen",
      initial: "Initial #{SecureRandom.hex}",
      brand_id: brand.id,
      branch_type: "central_kitchen"
    }
    )
  end
  let(:central_kitchen) do
    owner.create_new_location(central_kitchen_location)
  end
  let(:account) { central_kitchen.account }
  let!(:location_disbursement) do
    create(
      :location_disbursement,
      location: central_kitchen,
      net_amount: 10_000,
      bank_code: 'BCA',
      bank_account_number: '**********'
    )
  end

  let!(:interbank_location_disbursement) do
    create(
      :location_disbursement,
      location: central_kitchen,
      net_amount: 10_000,
      bank_code: 'BALI',
      bca_interbank_code: 'ABALIDBS',
      bank_account_number: '***************',
      bank_account_name: '<PERSON><PERSON> <PERSON>land<PERSON>'
    )
  end

  let!(:invalid_interbank_location_disbursement) do
    create(
      :location_disbursement,
      location: central_kitchen,
      net_amount: 10_000,
      bank_code: 'BALI',
      bca_interbank_code: 'ABALIDBS',
      bank_account_number: '*********',
      bank_account_name: 'Yories Yolanda'
    )
  end

  before do
    travel_to Time.utc(2025, 01, 31, 07, 53, 45)
    @redis_client = RedisConnector.instance.redis
    @redis_client.set(BCA::Constants::ACCESS_TOKEN_CACHE_KEY, 'yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G')
  end

  context "with succeeded disbursement" do
    context 'when intrabank disbursement' do # BCA to BCA
      before do
        stub_succeeded_transfer_intrabank(central_kitchen)
      end

      it "updates location disbursement status to COMPLETED and save the response" do
        expect do
          described_class.perform_now(location_disbursement_id: location_disbursement.id)
          location_disbursement.reload
        end.to change(location_disbursement, :status).from(nil).to('COMPLETED')
           .and change(location_disbursement, :provider_reference_id).from(nil).to('**************')
           .and change(location_disbursement, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(location_disbursement.provider_raw_response['responseCode']).to eq(BCA::Constants::INTRABANK_TRANSFER_SUCCESSFULL_CODE)
        expect(location_disbursement.provider_raw_response['responseMessage']).to eq('Successful')
        expect(location_disbursement.provider_raw_response['beneficiaryAccountNo']).to eq('**********')
        expect(location_disbursement.provider_raw_response['amount']).to eq({
                                                                              'value'=>'10000.00',
                                                                              'currency'=>'IDR'
                                                                            })
      end
    end

    context 'when interbank disbursement' do # BCA to other bank
      before do
        stub_succeeded_transfer_interbank
      end

      it "updates location disbursement status to COMPLETED and save the response" do
        expect do
          described_class.perform_now(location_disbursement_id: interbank_location_disbursement.id)
          interbank_location_disbursement.reload
        end.to change(interbank_location_disbursement, :status).from(nil).to('COMPLETED')
           .and change(interbank_location_disbursement, :provider_reference_id).from(nil).to('20250404000000046296199')
           .and change(interbank_location_disbursement, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(interbank_location_disbursement.provider_raw_response['responseCode']).to eq(BCA::Constants::INTERBANK_TRANSFER_SUCCESSFULL_CODE)
        expect(interbank_location_disbursement.provider_raw_response['responseMessage']).to eq('Successful')
        expect(interbank_location_disbursement.provider_raw_response['beneficiaryBankCode']).to eq('ABALIDBS')
        expect(interbank_location_disbursement.provider_raw_response['beneficiaryAccountNo']).to eq('***************')
        expect(interbank_location_disbursement.provider_raw_response['additionalInfo']).to eq({'bifastId'=>'20250404O000132645'})
        expect(interbank_location_disbursement.provider_raw_response['amount']).to eq({
                                                                                        'value'=>'10000.00',
                                                                                        'currency'=>'IDR'
                                                                                      })
      end
    end

    context 'when got timeout' do
      before do
        stub_read_timeout_transfer_interbank
      end

      context 'when first attempt' do
        it "should retry the job with 5 minutes delay" do
          expect do
            described_class.perform_now(location_disbursement_id: interbank_location_disbursement.id)
          end.to not_change { interbank_location_disbursement.reload.status }.from(nil)

          job = Sidekiq::Worker.jobs.find {
            |job| job['wrapped'] == 'BCA::Jobs::CreateDisbursementJob' &&
              job['args'].first['arguments'].first['location_disbursement_id'] == interbank_location_disbursement.id &&
              job['args'].first['arguments'].first['retry_count'] == 2
          }
          expect(job).to be_present
          expect(job['at'] - job['created_at']).to eq(BCA::Jobs::CreateDisbursementJob::DELAY)
        end
      end

      context 'when second attempt' do
        before do
          stub_succeeded_check_transfer_status
        end

        it "should check transfer status based on external_id and return the job if has been successfully transferred" do
          expect do
            described_class.perform_now(location_disbursement_id: interbank_location_disbursement.id, retry_count: 2, original_external_id: '81710243110853040011956756626251')
            interbank_location_disbursement.reload
          end.to change(interbank_location_disbursement, :status).from(nil).to('COMPLETED')
             .and change(interbank_location_disbursement, :provider_reference_id).from(nil).to('250616095602492610')
             .and change(interbank_location_disbursement, :external_id).from(nil).to('81710243110853040011956756626251') # this is random generated 32 digit number

          expect(interbank_location_disbursement.provider_raw_response['responseCode']).to eq(BCA::Constants::INTERBANK_CHECK_TRANSFER_STATUS_SUCCESSFUL_CODE)
          expect(interbank_location_disbursement.provider_raw_response['responseMessage']).to eq('Successful')
          expect(interbank_location_disbursement.provider_raw_response['beneficiaryBankCode']).to eq('ABALIDBS')
          expect(interbank_location_disbursement.provider_raw_response['beneficiaryAccountNo']).to eq('***************')
          expect(interbank_location_disbursement.provider_raw_response['amount']).to eq({
                                                                                          'value'=>'10000.00',
                                                                                          'currency'=>'IDR'
                                                                                        })
        end
      end

      context 'when already reach maximum retry and transaction is still not found' do
        before do
          stub_not_found_transfer_status
          allow(BCA::Client).to receive(:transfer_interbank).and_raise(Faraday::TimeoutError)
        end

        it "should check transfer status based on external_id and return the job if has been successfully transferred" do
          expect do
            described_class.perform_now(location_disbursement_id: interbank_location_disbursement.id, retry_count: 3)
          end.to raise_error(Faraday::TimeoutError)

          expect(interbank_location_disbursement.reload.provider_raw_response).to eq('timeout')
          expect(interbank_location_disbursement.status).to eq('FAILED')
          expect(interbank_location_disbursement.external_id).to be_present
        end
      end
    end
  end

  context 'when failed to disburse' do
    context 'when intrabank disbursement' do
      before do
        stub_failed_transfer_intrabank(central_kitchen)
      end

      it "updates location disbursement status to FAILED and save the response" do
        expect do
          described_class.perform_now(location_disbursement_id: location_disbursement.id)
          location_disbursement.reload
        end.to change(location_disbursement, :status).from(nil).to('FAILED')
           .and change(location_disbursement, :provider_reference_id).from(nil).to('**************')
           .and change(location_disbursement, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(location_disbursement.provider_raw_response['responseCode']).to eq('4031714')
        expect(location_disbursement.provider_raw_response['responseMessage']).to eq('Insufficient funds')
        expect(location_disbursement.provider_raw_response['beneficiaryAccountNo']).to eq('**********')
        expect(location_disbursement.provider_raw_response['amount']).to eq({
                                                                              'value'=>'1000000.00',
                                                                              'currency'=>'IDR'
                                                                            })
      end
    end

    context 'when interbank disbursement' do
      before do
        stub_failed_transfer_interbank
      end

      it "updates location disbursement status to FAILED and save the response" do
        expect do
          described_class.perform_now(location_disbursement_id: invalid_interbank_location_disbursement.id)
          invalid_interbank_location_disbursement.reload
        end.to change(invalid_interbank_location_disbursement, :status).from(nil).to('FAILED')
           .and change(invalid_interbank_location_disbursement, :provider_reference_id).from(nil).to('')
           .and change(invalid_interbank_location_disbursement, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(invalid_interbank_location_disbursement.provider_raw_response['responseCode']).to eq('4041811')
        expect(invalid_interbank_location_disbursement.provider_raw_response['responseMessage']).to eq('Invalid Account')
        expect(invalid_interbank_location_disbursement.provider_raw_response['beneficiaryAccountNo']).to eq('*********')
        expect(invalid_interbank_location_disbursement.provider_raw_response['amount']).to eq({
                                                                                                'value'=>'10000.00',
                                                                                                'currency'=>'IDR'
                                                                                              })
      end
    end
  end
end
