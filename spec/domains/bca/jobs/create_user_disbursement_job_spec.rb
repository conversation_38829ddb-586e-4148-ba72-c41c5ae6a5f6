require './spec/shared/bca'
require './spec/shared/user_disbursements'

describe BCA::Jobs::CreateUserDisbursementJob, type: :job do
  include_context 'BCA setup'
  include_context 'user_disbursements creations'

  before do
    travel_to Time.utc(2025, 01, 31, 07, 53, 45)
    @redis_client = RedisConnector.instance.redis
    @redis_client.set(BCA::Constants::ACCESS_TOKEN_CACHE_KEY, 'yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G')
  end

  after do
    travel_back
  end

  context "with succeeded disbursement" do
    context 'when intrabank disbursement' do # BCA to BCA
      before do
        stub_succeeded_transfer_intrabank(brand)
        user_disbursement_to_bca

        # should BCA
        expect(user_disbursement_to_bca.provider).to eq('bca')
      end

      it "updates disbursement status to success and save the response" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_bca.id)
          user_disbursement_to_bca.reload
        end.to change(user_disbursement_to_bca, :status).from('inprogress').to('success')
           .and change(user_disbursement_to_bca, :provider_reference_id).from(nil).to('**************')
           .and change(user_disbursement_to_bca, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(user_disbursement_to_bca.provider_raw_response['responseCode']).to eq(BCA::Constants::INTRABANK_TRANSFER_SUCCESSFULL_CODE)
        expect(user_disbursement_to_bca.provider_raw_response['responseMessage']).to eq('Successful')
        expect(user_disbursement_to_bca.provider_raw_response['beneficiaryAccountNo']).to eq('**********')
        expect(user_disbursement_to_bca.provider_raw_response['amount']).to eq(
          {
            'value'=>'10000.00',
            'currency'=>'IDR'
          }
        )
      end
    end

    context 'when interbank disbursement' do # BCA to Non BCA
      before do
        stub_succeeded_transfer_interbank
        user_disbursement_to_non_bca

        # should BCA
        expect(user_disbursement_to_non_bca.provider).to eq('bca')
      end

      it "updates disbursement status to success and save the response" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_non_bca.id)
          user_disbursement_to_non_bca.reload
        end.to change(user_disbursement_to_non_bca, :status).from('inprogress').to('success')
           .and change(user_disbursement_to_non_bca, :provider_reference_id).from(nil).to('20250404000000046296199')
           .and change(user_disbursement_to_non_bca, :external_id).from(nil).to(anything) # this is random generated 32 digit number

        expect(user_disbursement_to_non_bca.provider_raw_response['responseCode']).to eq(BCA::Constants::INTERBANK_TRANSFER_SUCCESSFULL_CODE)
        expect(user_disbursement_to_non_bca.provider_raw_response['responseMessage']).to eq('Successful')
        expect(user_disbursement_to_non_bca.provider_raw_response['beneficiaryBankCode']).to eq('ABALIDBS')
        expect(user_disbursement_to_non_bca.provider_raw_response['beneficiaryAccountNo']).to eq('***************')
        expect(user_disbursement_to_non_bca.provider_raw_response['additionalInfo']).to eq({'bifastId'=>'20250404O000132645'})
        expect(user_disbursement_to_non_bca.provider_raw_response['amount']).to eq(
          {
            'value'=>'10000.00',
            'currency'=>'IDR'
          }
        )
      end
    end
  end

  context 'when got timeout' do
    before do
      stub_read_timeout_transfer_interbank
      user_disbursement_to_non_bca

      # should BCA
      expect(user_disbursement_to_non_bca.provider).to eq('bca')
    end

    context 'when first attempt' do
      it "should retry the job with 5 minutes delay" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_non_bca.id)

          user_disbursement_to_non_bca.reload
        end
          .to not_change { user_disbursement_to_non_bca.status }.from('inprogress')
          .and not_change { user_disbursement_to_non_bca.provider_reference_id }.from(nil)
          .and not_change { user_disbursement_to_non_bca.external_id }.from(nil)

        job = Sidekiq::Worker.jobs.find {
          |job| job['wrapped'] == 'BCA::Jobs::CreateUserDisbursementJob' &&
            job['args'].first['arguments'].first['disbursement_id'] == user_disbursement_to_non_bca.id &&
            job['args'].first['arguments'].first['retry_count'] == 2
        }
        expect(job).to be_present
        expect(job['at'] - job['created_at']).to eq(BCA::Jobs::CreateUserDisbursementJob::DELAY)
      end
    end

    context 'when second attempt' do
      before do
        stub_succeeded_check_transfer_status
      end

      it "should check transfer status based on external_id and return the job if has been successfully transferred" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_non_bca.id, retry_count: 2, original_external_id: '81710243110853040011956756626251')
          user_disbursement_to_non_bca.reload
        end
          .to change(user_disbursement_to_non_bca, :status).from('inprogress').to('success')
          .and change(user_disbursement_to_non_bca, :provider_reference_id).from(nil).to('250616095602492610')
          .and change(user_disbursement_to_non_bca, :external_id).from(nil).to('81710243110853040011956756626251') # this is random generated 32 digit number

        expect(user_disbursement_to_non_bca.provider_raw_response['responseCode']).to eq(BCA::Constants::INTERBANK_CHECK_TRANSFER_STATUS_SUCCESSFUL_CODE)
        expect(user_disbursement_to_non_bca.provider_raw_response['responseMessage']).to eq('Successful')
        expect(user_disbursement_to_non_bca.provider_raw_response['beneficiaryBankCode']).to eq('ABALIDBS')
        expect(user_disbursement_to_non_bca.provider_raw_response['beneficiaryAccountNo']).to eq('***************')
        expect(user_disbursement_to_non_bca.provider_raw_response['amount']).to eq(
          {
            'value'=>'10000.00',
            'currency'=>'IDR'
          }
        )
      end
    end

    context 'when already reach maximum retry and transaction is still not found' do
      before do
        stub_not_found_transfer_status
        allow(BCA::Client).to receive(:transfer_interbank).and_raise(Faraday::TimeoutError)
      end

      it "should check transfer status based on external_id and return the job if has been successfully transferred" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_non_bca.id, retry_count: 3, original_external_id: '81710243110853040011956756626251')
        end
          .to change { delivery_user_account.account_transactions.count }.by(1) # revert usage
          .and raise_error(Faraday::TimeoutError)

        user_disbursement_to_non_bca.reload
        expect(user_disbursement_to_non_bca.status).to eq('failed')
        expect(user_disbursement_to_non_bca.external_id).to be_present
        expect(user_disbursement_to_non_bca.provider_raw_response).to eq('timeout')

        delivery_user_account.reload
        expect(delivery_user_account.balance).to eq(10_000.0)

        account_transaction = delivery_user_account.account_transactions.last
        expect(account_transaction.amount).to eq(10_000.0)
        expect(account_transaction.user_disbursement_id).to eq(user_disbursement_to_non_bca.id)
        expect(account_transaction.notes).to eq('Refund Credit Withdrawal')
      end
    end
  end

  context 'when failed to disburse' do
    context 'when intrabank disbursement' do
      before do
        stub_failed_transfer_intrabank(brand)
        user_disbursement_to_bca

        # should BCA
        expect(user_disbursement_to_bca.provider).to eq('bca')
      end

      it "updates location disbursement status to FAILED and save the response" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_bca.id)
          user_disbursement_to_bca.reload
          delivery_user_account.reload
        end
          .to change(user_disbursement_to_bca, :status).from('inprogress').to('failed')
          .and change(user_disbursement_to_bca, :provider_reference_id).from(nil).to('**************')
          .and change(user_disbursement_to_bca, :external_id).from(nil).to(anything) # this is random generated 32 digit number
          .and change { delivery_user_account.account_transactions.count }.by(1) # revert usage
          .and change { delivery_user_account.balance }.by(10000) # revert usage

        expect(user_disbursement_to_bca.provider_raw_response['responseCode']).to eq('4031714')
        expect(user_disbursement_to_bca.provider_raw_response['responseMessage']).to eq('Insufficient funds')
        expect(user_disbursement_to_bca.provider_raw_response['beneficiaryAccountNo']).to eq('**********')
        expect(user_disbursement_to_bca.provider_raw_response['amount']).to eq(
          {
            'value'=>'1000000.00',
            'currency'=>'IDR'
          }
        )

        account_transaction = delivery_user_account.account_transactions.last
        expect(account_transaction.amount).to eq(10000.0)
        expect(account_transaction.user_disbursement_id).to eq(user_disbursement_to_bca.id)
        expect(account_transaction.notes).to eq('Refund Credit Withdrawal')
      end
    end

    context 'when interbank disbursement' do
      before do
        stub_failed_transfer_interbank
        user_disbursement_to_non_bca.update(disbursement_bank_account_number: '*********')
      end

      it "updates location disbursement status to FAILED and save the response" do
        expect do
          described_class.perform_now(disbursement_id: user_disbursement_to_non_bca.id)
          user_disbursement_to_non_bca.reload
          delivery_user_account.reload
        end
          .to change(user_disbursement_to_non_bca, :status).from('inprogress').to('failed')
          .and change(user_disbursement_to_non_bca, :provider_reference_id).from(nil).to('')
          .and change(user_disbursement_to_non_bca, :external_id).from(nil).to(anything) # this is random generated 32 digit number
          .and change { delivery_user_account.account_transactions.count }.by(1) # revert usage
          .and change { delivery_user_account.balance }.by(10_000.0)

        expect(user_disbursement_to_non_bca.provider_raw_response['responseCode']).to eq('4041811')
        expect(user_disbursement_to_non_bca.provider_raw_response['responseMessage']).to eq('Invalid Account')
        expect(user_disbursement_to_non_bca.provider_raw_response['beneficiaryAccountNo']).to eq('*********')
        expect(user_disbursement_to_non_bca.provider_raw_response['amount']).to eq(
          {
            'value'=>'10000.00',
            'currency'=>'IDR'
          }
        )

        account_transaction = delivery_user_account.account_transactions.last
        expect(account_transaction.amount).to eq(10000.0)
        expect(account_transaction.user_disbursement_id).to eq(user_disbursement_to_non_bca.id)
        expect(account_transaction.notes).to eq('Refund Credit Withdrawal')
      end
    end
  end
end
