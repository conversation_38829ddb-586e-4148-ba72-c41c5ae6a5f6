require './spec/shared/locations'
require './spec/shared/messaging/client_stubs'
require './spec/shared/swagger'

describe 'Location API', type: :request, search: true do
  include_context 'locations creations'
  include_context 'messaging client stubs'
  include_context 'swagger after response'

  before(:each) do
    @header = authentication_header(owner)
  end

  let(:brand) { owner.active_brand }
  let(:brand_uuid) { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let(:"Brand-UUID") { brand_uuid }

  let(:Authorization) { @header['Authorization'] }
  let(:tags) { 'Restaurant - Messaging Locations' }

  path '/api/messaging/locations/from_origins' do
    get 'origins list' do
      tags tags
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, required: false, type: :string
      parameter name: :page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_page' }
      parameter name: :item_per_page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_item_per_page' }
      parameter name: :destination_to_id, in: :query, required: false, type: :string
      parameter name: :destination_to_type, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_messaging_location_type' }

      response 200, 'successful' do
        before do |example|
          central_kitchen
          owned_branch_1
          franchise_branch_1

          Location.search_index.refresh
        end

        schema '$ref' => '#/components/responses/response_messaging_locations_from_origins'

        context 'when destination to params is not present' do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return outlets' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            expect(response_body['origins'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['origins'].map { |origin| origin['branch_type'] }.uniq).to match_array %w[outlet]
            expect(response_body['origins'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when one of the location is deactivated' do
          before do |example|
            owned_branch_1.update(status: 'deactivated')

            Location.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should only return activated outlets' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            location_ids = response_body['origins'].pluck('id').sort
            expect(location_ids).to match_array [franchise_branch_1.id].sort
            expect(response_body['origins'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['origins'].map { |origin| origin['branch_type'] }.uniq).to match_array %w[outlet]
            expect(response_body['origins'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when destination to params is outlet' do
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { 'Location' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should return nothing for now' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            expect(response_body['origins']).to eq ([])
            expect(response_body['paging']).to eq ({"current_page"=>0, "total_item"=>0})
          end
        end

        context 'when destination to params is central_kitchen' do
          let(:destination_to_id) { central_kitchen.id }
          let(:destination_to_type) { 'Location' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            expect(response_body['origins'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['origins'].map { |origin| origin['branch_type'] }.uniq).to eq(['outlet'])
            expect(response_body['origins'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when pagination params exist' do
          let(:page) { 2 }
          let(:item_per_page) { 1 }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            expect(response_body['paging'].keys).to match_array %w[current_page total_item]
            expect(response_body['paging']['current_page']).to eq(page)
            expect(response_body['paging']['total_item']).to eq(2)
          end
        end

        context 'when keyword params exist' do
          let(:keyword) { owned_branch_1.name }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[origins paging]
            expect(response_body['origins'].length).to eq 1
            expect(response_body['origins'][0]['id']).to eq owned_branch_1.id
            expect(response_body['origins'][0]['name']).to eq owned_branch_1.name
          end
        end
      end
    end
  end

  path '/api/messaging/locations/to_destinations' do
    get 'destination list' do
      tags tags
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, required: false, type: :string
      parameter name: :page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_page' }
      parameter name: :item_per_page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_item_per_page' }
      parameter name: :origin_from_id, in: :query, required: false, type: :string
      parameter name: :origin_from_type, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_messaging_location_type' }

      response 200, 'successful' do
        before do |example|
          central_kitchen
          central_kitchen_2
          owned_branch_1
          franchise_branch_1

          Location.search_index.refresh
        end

        schema '$ref' => '#/components/responses/response_messaging_locations_to_destinations'

        context 'when destination from params is not present' do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return central_kitchens without vendors' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            expect(response_body['destinations'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['destinations'].map { |origin| origin['branch_type'] }.uniq).to match_array %w[central_kitchen]
            expect(response_body['destinations'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when origin from params is central_kitchen' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { 'Location' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should return nothing for now' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            expect(response_body['destinations']).to eq ([])
            expect(response_body['paging']).to eq ({"current_page"=>0, "total_item"=>0})
          end
        end

        context 'when origin from params is outlet' do
          let(:origin_from_id) { owned_branch_1.id }
          let(:origin_from_type) { 'Location' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return central_kitchen' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            expect(response_body['destinations'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['destinations'].map { |origin| origin['branch_type'] }.uniq).to eq(['central_kitchen'])
            expect(response_body['destinations'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when one of the location is deactivated' do
          let(:origin_from_id) { owned_branch_1.id }
          let(:origin_from_type) { 'Location' }

          before do |example|
            central_kitchen_2.update(status: 'deactivated')

            Location.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should only return activated central_kitchen' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            location_ids = response_body['destinations'].pluck('id').sort
            expect(location_ids).to match_array [central_kitchen.id].sort
            expect(response_body['destinations'].first.keys).to match_array %w[id name initial branch_type is_franchise type brand_id]
            expect(response_body['destinations'].map { |origin| origin['branch_type'] }.uniq).to eq(['central_kitchen'])
            expect(response_body['destinations'].map { |origin| origin['type'] }.uniq).to match_array %w[Location]
          end
        end

        context 'when pagination params exist' do
          let(:page) { 2 }
          let(:item_per_page) { 1 }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return central_kitchen per page' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            expect(response_body['paging'].keys).to match_array %w[current_page total_item]
            expect(response_body['paging']['current_page']).to eq(page)
            expect(response_body['paging']['total_item']).to eq(2)
          end
        end

        context 'when keyword params exist' do
          let(:keyword) { central_kitchen.name }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should only return central_kitchen' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[destinations paging]
            expect(response_body['destinations'].length).to eq 1
            expect(response_body['destinations'][0]['id']).to eq central_kitchen.id
            expect(response_body['destinations'][0]['name']).to eq central_kitchen.name
          end
        end
      end
    end
  end

  path '/api/messaging/locations/users' do
    get 'users that exist in origins or destination' do
      tags tags
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, required: false, type: :string
      parameter name: :page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_page' }
      parameter name: :item_per_page, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_item_per_page' }
      parameter name: :origin_from_id, in: :query, required: true, type: :string
      parameter name: :origin_from_type, in: :query, required: true, type: :string, schema: { '$ref' => '#/components/parameters/parameter_messaging_location_type' }
      parameter name: :destination_to_id, in: :query, required: true, type: :string
      parameter name: :destination_to_type, in: :query, required: true, type: :string, schema: { '$ref' => '#/components/parameters/parameter_messaging_location_type' }
      parameter name: :exclude_ids, in: :query, required: false, type: :string, schema: { '$ref' => '#/components/parameters/parameter_messaging_exclude_ids' }

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_messaging_locations_users'
        before do |example|
          central_kitchen
          owned_branch_1
          franchise_branch_1

          Location.search_index.refresh
        end

        context 'when keyword params exists' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { central_kitchen.class.name }
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { owned_branch_1.class.name }
          let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id], fullname: SecureRandom.uuid) }
          let(:employee_2) { create(:confirmed_user, location_ids: [central_kitchen.id], fullname: SecureRandom.uuid) }

          before do |example|
            employee
            employee_2

            submit_request(example.metadata)
          end

          let(:keyword) { employee_2.fullname }

          it 'should return users based on pagination params' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[users paging]
            expect(response_body['users'].length).to eq 1
            expect(response_body['users'][0]).to eq({ id: employee_2.id, name: employee_2.fullname }.as_json)
          end
        end

        context 'when pagination params exists' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { central_kitchen.class.name }
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { owned_branch_1.class.name }
          let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
          let(:employee_2) { create(:confirmed_user, location_ids: [central_kitchen.id]) }

          before do |example|
            employee
            employee_2

            submit_request(example.metadata)
          end

          let(:page) { 2 }
          let(:item_per_page) { 1 }

          it 'should return users based on pagination params' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[users paging]
            expect(response_body['users'].length).to eq 1
            expect(response_body['users'][0]).to eq({ id: employee_2.id, name: employee_2.fullname }.as_json)
          end
        end

        context 'when required params are empty' do
          let(:origin_from_id) { '' }
          let(:origin_from_type) { '' }
          let(:destination_to_id) { '' }
          let(:destination_to_type) { '' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should return no user' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[users paging]
            expect(response_body['users']).to eq []
            expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
          end
        end

        context 'when user has access either origin or destination' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { central_kitchen.class.name }
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { owned_branch_1.class.name }
          let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
          let(:employee_2) { create(:confirmed_user, location_ids: [central_kitchen.id]) }
          let(:employee_3) { create(:confirmed_user, location_ids: [central_kitchen.id]) }

          before do |example|
            employee
            employee_2
            employee_3
          end

          context 'when both location is activated but one of the employee is kicked from the brand' do
            before do |example|
              employee_3.user_manage_brands.last.destroy
              submit_request(example.metadata)
            end

            it 'should exclude current user and return users that has access to either one' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array %w[users paging]
              expect(response_body['users'].length).to eq 2
              expect(response_body['users'][0].keys).to match_array %w[id name]
              expect(response_body['users'].map { |user| user['id'] }).to match_array [employee.id, employee_2.id]
              expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
            end
          end

          context 'when one of location is deactivated' do
            before do |example|
              owned_branch_1.update(status: 'deactivated')

              Location.search_index.refresh

              submit_request(example.metadata)
            end

            it 'should only return user with active locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array %w[users paging]
              expect(response_body['users'].length).to eq 2
              expect(response_body['users'][0].keys).to match_array %w[id name]
              expect(response_body['users'].map { |user| user['id'] }).to match_array [employee_2.id, employee_3.id]
              expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
            end
          end
        end

        context 'when user has access to both origin & destination' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { central_kitchen.class.name }
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { owned_branch_1.class.name }
          let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id, central_kitchen.id]) }
          let(:employee_2) { create(:confirmed_user, location_ids: [owned_branch_1.id, central_kitchen.id]) }
          let(:employee_3) { create(:confirmed_user, location_ids: [owned_branch_1.id, central_kitchen.id]) }

          before do |example|
            employee
            employee_2
            employee_3
          end

          context 'when both location is activated' do
            before do |example|
              submit_request(example.metadata)
            end

            it 'should exclude current user and return users that has access without duplicates' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array %w[users paging]
              expect(response_body['users'].length).to eq 3
              expect(response_body['users'][0].keys).to match_array %w[id name]
              expect(response_body['users'].map { |user| user['id'] }).to match_array [employee.id, employee_2.id, employee_3.id]
              expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
            end
          end

          context 'when one of location is deactivated' do
            before do |example|
              owned_branch_1.update(status: 'deactivated')

              Location.search_index.refresh

              submit_request(example.metadata)
            end

            it 'should exclude current user and still return users from one of the activated locations' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array %w[users paging]
              expect(response_body['users'].length).to eq 3
              expect(response_body['users'][0].keys).to match_array %w[id name]
              expect(response_body['users'].map { |user| user['id'] }).to match_array [employee.id, employee_2.id, employee_3.id]
              expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
            end
          end
        end


        context 'when params exclude_ids exist' do
          let(:origin_from_id) { central_kitchen.id }
          let(:origin_from_type) { central_kitchen.class.name }
          let(:destination_to_id) { owned_branch_1.id }
          let(:destination_to_type) { owned_branch_1.class.name }
          let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
          let(:employee_2) { create(:confirmed_user, location_ids: [central_kitchen.id]) }
          let(:exclude_ids) { [employee.id, employee_2.id].join(',') }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should exclude current user and return users that has access to either one' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[users paging]
            expect(response_body['users'].length).to eq 0
            expect(response_body['paging'].keys).to match_array %w[current_page total_item next_page prev_page]
          end
        end
      end
    end
  end
end