require './spec/shared/sale_transactions'
require './spec/shared/promos'
require './spec/shared/storage_section_mappings'

describe SaleDetailModifier, type: :model do
  include_context "sale transaction creations"
  include_context "promos creations"
  include_context "storage section mappings creations"

  # spreadsheets calculations https://docs.google.com/spreadsheets/d/1AmMLvkC4xDH1ILu9yUvKcp9lVFAM12RiHJUZznwWcYQ/edit#gid=1448520720
  # we will use rounding 5 here as DB is using rounding 6, and we want to ensure no precision error is found here

  let(:modifier_discount) do
    build(:sale_detail_modifier, product_id: nil, description: 'Potongan', product_unit_id: nil, quantity: 1, price: -5000, total_line_amount: -5000)
  end

  let(:modifier_surcharge) do
    build(:sale_detail_modifier, product_id: nil, description: 'Biaya Extra', product_unit_id: nil, quantity: 1, price: 20000, total_line_amount: 20000)
  end

  let(:local_sale_transaction) do
    sale_transaction.sale_detail_transactions.first.sale_detail_modifiers.first.update!(price: 4000, total_line_amount: 4000)
    sale_detail_modifier_2.price = 6000
    sale_detail_modifier_2.total_line_amount = 6000

    sale_transaction.sale_detail_transactions.first.update!(quantity: 1, price: 25000, total_line_amount: 25000,
                                                            total_amount: 25000 + sale_detail_modifier_2.total_line_amount +
                                                                          sale_transaction.sale_detail_transactions.first.sale_detail_modifiers
                                                                                          .first
                                                                                          .total_line_amount)

    sale_transaction.subtotal = sale_transaction.sale_detail_transactions.first.total_amount
    sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << sale_detail_modifier_2
    sale_transaction.save!

    sale_transaction
  end

  let(:local_promo_order_1) do
    reward = promo_all_locations.promo_reward
    reward.discount_amount = 2000
    reward.save!

    json_object = promo_all_locations.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_order_2) do
    reward = promo_all_locations_2.promo_reward
    reward.discount_amount = 3000
    reward.save!

    json_object = promo_all_locations_2.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_item_1) do
    go_food_promo.update_columns(is_select_all_location: true, location_type: nil, location_ids: [], owner_location_id: local_sale_transaction.location_id)
    reward = go_food_promo.promo_reward
    reward.discount_amount = 2000
    reward.reward_products = [
      {
        product_id: latte.id,
        quantity: nil
      }
    ]
    reward.save!
    rule = go_food_promo.promo_rule
    rule.maximum_qty_applied_to_products = [
      {
        id: latte.id,
        name: latte.name,
        maximum_purchase: nil
      }
    ]
    rule.save!

    json_object = go_food_promo.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_item_2) do
    promo_with_promo_rule_product_ids.update_columns(is_select_all_location: true, location_type: nil, location_ids: [], owner_location_id: local_sale_transaction.location_id)
    reward = promo_with_promo_rule_product_ids.promo_reward
    reward.discount_amount = 3000
    reward.save!

    json_object = promo_with_promo_rule_product_ids.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  describe '#destroy' do
    context 'when has relation with sale detail lines, sales returns and payments' do
      let(:return_payment) { build(:return_payment, payment_method_id: payment_method.id) }
      let(:sales_return_line) { build(:sales_return_line, sale_detail_transaction_id: sale_transaction_modifier.sale_detail_transactions.first.id) }
      let(:sales_return) do
        data = build(:sales_return, brand_id: sale_transaction_modifier.brand_id, location_id: sale_transaction_modifier.location_id,
          sale_transaction_id: sale_transaction_modifier.id, refund_employee_id: owner.id)
        data.sales_return_lines << sales_return_line
        data.return_payments << return_payment
        data.save!
        data
      end
      let(:payment) { build(:payment, payment_method_id: payment_method.id) }

      before do
        sale_transaction_modifier.save
        sales_return
        sale_transaction_modifier.payments << payment
        sale_transaction_modifier.save
      end

      it 'should remove dependent models' do
        expect do
          sale_transaction_modifier.destroy!
        end.to change(SalesReturn, :count).by(-1)
           .and change(SalesReturnLine, :count).by(-1)
           .and change(Payment, :count).by(-1)
           .and change(ReturnPayment, :count).by(-1)
           .and change(SaleDetailModifier, :count).by(-1)
           .and change(SaleDetailTransaction, :count).by(-1)
           .and change(SalesReturnLine, :count).by(-1)
      end
    end
  end

  context 'when have no modifier discount and surcharge' do
    it 'should return correct calculation' do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.reload.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(sale_modifier_1.total_line_amount)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(sale_modifier_2.total_line_amount)

      expect(sale_detail.prorate_discount.round(5)).to eql(0)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(0)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_line_amount)
      expect(sale_detail.total_amount_prorate_discount.round(5)).to eql(sale_detail.total_amount)
    end
  end

  context 'when have modifier discount and surcharge' do
    let(:new_local_sale_transaction) do
      SaleTransaction.find(local_sale_transaction.id)
    end

    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount) +
                                        modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_discount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_surcharge

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale_detail = new_local_sale_transaction.sale_detail_transactions.first
      sale_detail.reload.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(571.42857)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2285.71429)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(5714.28572)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(857.14286)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3428.57143)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(8571.42857)

      expect(sale_detail.prorate_discount.round(5)).to eql(3571.42857)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(14285.71429)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(35714.28572)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount_prorate_discount.round(5) + 0.00001) # rounding 0.00001

      expect(sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail.prorate_discount.round(5)).to eql(modifier_discount.total_line_amount * -1)
      expect(sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail.prorate_surcharge.round(5)).to eql(modifier_surcharge.total_line_amount + 0.00001) # 0.00001 ROUNDING
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) + sale_modifier_2.total_amount_prorate_discount.round(5) + sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount + 0.00001) # 0.00001 ROUNDING
    end
  end

  context 'when have discount fee, applied promos, surcharge fee, but has qty is 0' do
    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount))

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000

      # promo item deleted bcs no modifier related
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2]
      local_sale_transaction.save!

      sale_modifier = sale_detail.sale_detail_modifiers.second
      sale_modifier.update!(quantity: 0)
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale_detail = sale.sale_detail_transactions.first
      sale_detail.reload
      sale_detail.sale_transaction.send(:init_temp_variables)
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5).to_s).to eql('2068.96552')
      expect(sale_modifier_1.prorate_surcharge.round(5).to_s).to eql('275.86207')
      expect(sale_modifier_1.total_amount_prorate_discount.round(5).to_s).to eql('2206.89655')

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.to_s).to eql('0.0')
      expect(sale_modifier_2.prorate_surcharge.to_s).to eql('0.0')
      expect(sale_modifier_2.total_amount_prorate_discount.to_s).to eql('0.0')

      expect(sale_detail.prorate_discount.round(5)).to eql(12931.03448)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(1724.13793)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(13793.10345)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount_prorate_discount.round(5))
    end
  end

  context 'when have discount fee, applied promos, surcharge fee' do
    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount))

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000

      # promo item deleted bcs no modifier related
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale_detail = sale.sale_detail_transactions.first
      sale_detail.reload
      sale_detail.sale_transaction.send(:init_temp_variables)
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(1714.28571)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(228.57143)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(2514.28571 + 0.00001) # rounding precision

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(2571.42857)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(342.85714)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(3771.42857)

      expect(sale_detail.prorate_discount.round(5)).to eql(10714.28571)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(1428.57143)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(15714.28571 + 0.00001) # rounding precision
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount_prorate_discount.round(5) + 0.00001) # rounding precision

      discount_total = sale.discount_promo_total_order_only + sale.discount_fee
      expect(sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail.prorate_discount.round(5)).to eql(discount_total - 0.00001) # 0.00001 ROUNDING
      expect(sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail.prorate_surcharge.round(5)).to eql(sale.surcharge_fee) # 0.00001 ROUNDING
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) + sale_modifier_2.total_amount_prorate_discount.round(5) + sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount + sale.surcharge_fee - discount_total + 0.00001) # 0.00001 ROUNDING
    end
  end

  context 'when have modifiers discount and surcharge, discount fee, applied promos, surcharge fee' do
    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount) +
                                        modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_discount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_surcharge

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2, local_promo_item_1, local_promo_item_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale_detail = sale.sale_detail_transactions.first
      sale_detail.reload
      sale_detail.sale_transaction.send(:init_temp_variables)
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(2285.71429)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2514.28571 + 0.00001) # rounding precision
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(4228.57143)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(3428.57143)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3771.42857)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(6342.85714)

      expect(sale_detail.prorate_discount.round(5)).to eql(14285.71429)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(15714.28571 + 0.00001) # rounding precision
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(26428.57143)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount_prorate_discount.round(5))

      discount_total = sale.discount_promo_total_order_only + sale.discount_fee - modifier_discount.total_line_amount
      surcharge_total = sale.surcharge_fee + modifier_surcharge.total_line_amount

      expect(sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail.prorate_discount.round(5)).to eql(discount_total + 0.00001) # 0.00001 ROUNDING
      expect(sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail.prorate_surcharge.round(5)).to eql(surcharge_total + 0.00001) # 0.00001 ROUNDING
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount +
                                                                        sale.surcharge_fee -
                                                                        (sale.discount_promo_total_order_only + sale.discount_fee))
    end
  end

  context 'when have modifiers discount and surcharge, discount fee, applied promos, surcharge fee, and discount fee greater than total product cost' do
    let(:new_local_sale_transaction) do
      SaleTransaction.find(local_sale_transaction.id)
    end

    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount) +
                                        modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_discount
      local_sale_transaction.sale_detail_transactions.first.sale_detail_modifiers << modifier_surcharge

      local_sale_transaction.subtotal = local_sale_transaction.sale_detail_transactions.first.total_amount
      local_sale_transaction.discount_fee = 500_000
      local_sale_transaction.surcharge_fee = 2000
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2, local_promo_item_1, local_promo_item_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation, and ensure prorate discount = prorate surcharge + total line amount' do
      sale_detail = new_local_sale_transaction.sale_detail_transactions.first
      sale_detail.reload.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount).to eql(sale_modifier_1.prorate_surcharge + sale_modifier_1.total_line_amount)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2514.28571 + 0.00001) # rounding precision
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(0)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount).to eql(sale_modifier_2.prorate_surcharge + sale_modifier_2.total_line_amount)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3771.42857)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(0)

      expect(sale_detail.prorate_discount).to eql(sale_detail.prorate_surcharge + sale_detail.total_line_amount)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(15714.28571 + 0.00001) # rounding precision
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(0)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5) +
             sale_detail.total_line_discount_prorate.round(5)).to eql(0)
    end
  end
end
