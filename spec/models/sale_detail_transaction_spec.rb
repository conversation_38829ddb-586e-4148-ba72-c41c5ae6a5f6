require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/promos'
require './spec/shared/storage_section_mappings'

describe SaleDetailTransaction, type: :model do
  include_context "sale transaction creations"
  include_context "sales returns creations"
  include_context "promos creations"
  include_context "storage section mappings creations"

  # spreadsheets calculations https://docs.google.com/spreadsheets/d/1AmMLvkC4xDH1ILu9yUvKcp9lVFAM12RiHJUZznwWcYQ/edit#gid=1448520720
  # we will use rounding 5 here as DB is using rounding 6, and we want to ensure no precision error is found here

  let(:modifier_discount) do
    build(:sale_detail_modifier, product_id: nil, description: 'Potongan', product_unit_id: nil, quantity: 1, price: -5000, total_line_amount: -5000)
  end

  let(:modifier_surcharge) do
    build(:sale_detail_modifier, product_id: nil, description: 'Biaya Extra', product_unit_id: nil, quantity: 1, price: 20000, total_line_amount: 20000)
  end

  let(:modifier_discount_2) do
    build(:sale_detail_modifier, product_id: nil, description: 'Potongan II', product_unit_id: nil, quantity: 1, price: -5000, total_line_amount: -5000)
  end

  let(:modifier_surcharge_2) do
    build(:sale_detail_modifier, product_id: nil, description: 'Biaya Extra II', product_unit_id: nil, quantity: 1, price: 20000, total_line_amount: 20000)
  end

  let(:local_product_modifier) { create(:product, :modifier, name: 'Local Modifier I', brand: brand, product_unit: gram, sell_tax_setting: 'price_exclude_tax', tax: tax) }
  let(:local_product_modifier_2) { create(:product, :modifier, name: 'Local Modifier II', brand: brand, product_unit: gram, sell_tax_setting: 'price_exclude_tax', tax: tax) }

  let(:sale_detail_modifier_3) do
    build(
      :sale_detail_modifier,
      product_id: local_product_modifier.id,
      product_unit_id: local_product_modifier.product_unit.id,
      price: 6000,
      total_line_amount: 6000
    )
  end

  let(:sale_detail_modifier_4) do
    build(
      :sale_detail_modifier,
      product_id: local_product_modifier_2.id,
      product_unit_id: local_product_modifier_2.product_unit.id,
      price: 12000,
      total_line_amount: 12000
    )
  end

  let(:local_sale_transaction) do
    sale_detail_1 = sale_transaction_with_2_lines.sale_detail_transactions.first
    sale_detail_2 = sale_transaction_with_2_lines.sale_detail_transactions.second

    sale_detail_1.sale_detail_modifiers
                 .first.update!(price: 4000, total_line_amount: 4000)

    sale_detail_2.sale_detail_modifiers
                 .first.update!(price: 8000, total_line_amount: 8000)

    sale_detail_1.update!(quantity: 1, price: 25000, total_line_amount: 25000,
                          total_amount: 25000 + sale_detail_modifier_3.total_line_amount +
                                        sale_detail_1.sale_detail_modifiers.first.total_line_amount)

    sale_detail_2.update!(quantity: 1, price: 35000, total_line_amount: 35000,
                          total_amount: 35000 + sale_detail_modifier_4.total_line_amount +
                                        sale_detail_2.sale_detail_modifiers.first.total_line_amount)

    sale_transaction_with_2_lines.subtotal = sale_detail_1.total_amount + sale_detail_2.total_amount
    sale_detail_1.sale_detail_modifiers << sale_detail_modifier_3
    sale_detail_2.sale_detail_modifiers << sale_detail_modifier_4
    sale_transaction_with_2_lines.save!

    sale_transaction_with_2_lines
  end

  let(:local_promo_order_1) do
    reward = promo_all_locations.promo_reward
    reward.discount_amount = 2000
    reward.save!

    json_object = promo_all_locations.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_order_2) do
    reward = promo_all_locations_2.promo_reward
    reward.discount_amount = 3000
    reward.save!

    json_object = promo_all_locations_2.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_item_1) do
    go_food_promo.update_columns(is_select_all_location: true, location_type: nil, location_ids: [], owner_location_id: local_sale_transaction.location_id)
    reward = go_food_promo.promo_reward
    reward.discount_amount = 2000
    reward.reward_products = [
      {
        product_id: latte.id,
        quantity: nil
      }
    ]
    reward.save!
    rule = go_food_promo.promo_rule
    rule.maximum_qty_applied_to_products = [
      {
        id: latte.id,
        name: latte.name,
        maximum_purchase: nil
      }
    ]
    rule.save!

    json_object = go_food_promo.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_item_2) do
    promo_with_promo_rule_product_ids.update_columns(is_select_all_location: true, location_type: nil, location_ids: [], owner_location_id: local_sale_transaction.location_id)
    reward = promo_with_promo_rule_product_ids.promo_reward
    reward.discount_amount = 3000
    reward.reward_products = [
      {
        product_id: latte.id,
        quantity: nil
      }
    ]
    reward.save!
    rule = promo_with_promo_rule_product_ids.promo_rule
    rule.maximum_qty_applied_to_products = [
      {
        id: latte.id,
        name: latte.name,
        maximum_purchase: nil
      }
    ]
    rule.save!

    json_object = promo_with_promo_rule_product_ids.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  context 'when have no modifier discount and surcharge' do
    it 'should return correct calculation' do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(sale_modifier_1.total_line_amount)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(sale_modifier_2.total_line_amount)

      expect(sale_detail.prorate_discount.round(5)).to eql(0)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(0)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_line_amount)
      expect(sale_detail.total_amount_prorate_discount.round(5)).to eql(sale_detail.total_amount)

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      sale_modifier_3 = sale_detail_2.sale_detail_modifiers.first
      expect(sale_modifier_3.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_3.total_amount_prorate_discount.round(5)).to eql(sale_modifier_3.total_line_amount)

      sale_modifier_4 = sale_detail_2.sale_detail_modifiers.second
      expect(sale_modifier_4.prorate_discount.round(5)).to eql(0)
      expect(sale_modifier_4.prorate_surcharge.round(5)).to eql(0)
      expect(sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(sale_modifier_4.total_line_amount)

      expect(sale_detail_2.prorate_discount.round(5)).to eql(0)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(0)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(sale_detail_2.total_line_amount)
      expect(sale_detail_2.total_amount_prorate_discount.round(5)).to eql(sale_detail_2.total_amount)
    end
  end

  context 'when have modifier discount and surcharge' do
    let(:new_local_sale_transaction) do
      SaleTransaction.find(local_sale_transaction.id)
    end

    before do
      sale_detail_1 = local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.update!(total_amount: 25000 + sale_detail_1.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.update!(total_amount: 35000 + sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount_2.total_line_amount + modifier_surcharge_2.total_line_amount)

      local_sale_transaction.subtotal = sale_detail_1.total_amount + sale_detail_2.total_amount
      sale_detail_1.sale_detail_modifiers << modifier_discount
      sale_detail_1.sale_detail_modifiers << modifier_surcharge

      sale_detail_2.sale_detail_modifiers << modifier_discount_2
      sale_detail_2.sale_detail_modifiers << modifier_surcharge_2

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale_detail_1 = new_local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_1.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_1.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail_1.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(571.42857)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2285.71429)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(5714.28572)

      sale_modifier_2 = sale_detail_1.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(857.14286)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3428.57143)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(8571.42857)

      expect(sale_detail_1.prorate_discount.round(5)).to eql(3571.42857)
      expect(sale_detail_1.prorate_surcharge.round(5)).to eql(14285.71429)
      expect(sale_detail_1.total_line_discount_prorate.round(5)).to eql(35714.28572)
      expect(sale_detail_1.total_amount_prorate_discount.round(5)).to eql(sale_detail_1.total_amount)

      expect(sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail_1.prorate_discount.round(5)).to eql(modifier_discount.total_line_amount * -1)
      expect(sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail_1.prorate_surcharge.round(5)).to eql(modifier_surcharge.total_line_amount + 0.00001) # 0.00001 ROUNDING
      expect(sale_modifier_1.total_amount_prorate_discount.round(5) + sale_modifier_2.total_amount_prorate_discount.round(5) + sale_detail_1.total_line_discount_prorate.round(5)).to eql(sale_detail_1.total_amount + 0.00001) # 0.00001 ROUNDING

      sale_detail_2 = new_local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      sale_modifier_3 = sale_detail_2.sale_detail_modifiers.first
      expect(sale_modifier_3.prorate_discount.round(5)).to eql(727.27273)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(2909.09091)
      expect(sale_modifier_3.total_amount_prorate_discount.round(5)).to eql(10181.81818)

      sale_modifier_4 = sale_detail_2.sale_detail_modifiers.second
      expect(sale_modifier_4.prorate_discount.round(5)).to eql(1090.90909)
      expect(sale_modifier_4.prorate_surcharge.round(5)).to eql(4363.63636)
      expect(sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(15272.72727)

      expect(sale_detail_2.prorate_discount.round(5)).to eql(3181.81818)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(12727.27273)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(44545.45455)
      expect(sale_detail_2.total_amount_prorate_discount.round(5)).to eql(sale_detail_2.total_amount)

      expect(sale_modifier_3.prorate_discount.round(5) + sale_modifier_4.prorate_discount.round(5) + sale_detail_2.prorate_discount.round(5)).to eql(modifier_discount.total_line_amount * -1)
      expect(sale_modifier_3.prorate_surcharge.round(5) + sale_modifier_4.prorate_surcharge.round(5) + sale_detail_2.prorate_surcharge.round(5)).to eql(modifier_surcharge.total_line_amount)
      expect(sale_modifier_3.total_amount_prorate_discount.round(5) + sale_modifier_4.total_amount_prorate_discount.round(5) + sale_detail_2.total_line_discount_prorate.round(5)).to eql(sale_detail_2.total_amount)
    end
  end

  context 'when have discount fee, applied promos, surcharge fee' do
    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount))

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.update!(total_amount: 35000 + sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount))

      local_sale_transaction.subtotal = sale_detail.total_amount + sale_detail_2.total_amount
      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000

      # promo item deleted bcs no modifier related
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale.send(:init_temp_variables)
      sale_detail = sale.sale_detail_transactions.first
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(666.66667)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(88.88889)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(3422.22222)

      sale_modifier_2 = sale_detail.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(1000)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(133.33333)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(5133.33333)

      expect(sale_detail.prorate_discount.round(5)).to eql(4166.66667)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(555.55556)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(21388.88889)
      expect(sale_detail.total_line_discount_prorate.round(5) +
             sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(29944.44444)

      sale_detail_2 = sale.sale_detail_transactions.second
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      sale_modifier_3 = sale_detail_2.sale_detail_modifiers.first
      expect(sale_modifier_3.prorate_discount.round(5)).to eql(1333.33333)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(177.77778)
      expect(sale_modifier_3.total_amount_prorate_discount.round(5)).to eql(6844.44444 + 0.00001) # 0.00001 rounding

      sale_modifier_4 = sale_detail_2.sale_detail_modifiers.second
      expect(sale_modifier_4.prorate_discount.round(5)).to eql(2000.00000)
      expect(sale_modifier_4.prorate_surcharge.round(5)).to eql(266.66667)
      expect(sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(10266.66667)

      expect(sale_detail_2.prorate_discount.round(5)).to eql(5833.33333)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(777.77778)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(29944.44444 + 0.00001) # 0.00001 rounding precision
      expect(sale_detail_2.total_line_discount_prorate.round(5) +
             sale_modifier_3.total_amount_prorate_discount.round(5) +
             sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(47055.55556 + 0.00001) # 0.00001 rounding precision

      discount_total = sale.discount_promo_total_order_only + sale.discount_fee
      expect(sale_modifier_3.prorate_discount.round(5) + sale_modifier_4.prorate_discount.round(5) + sale_detail_2.prorate_discount.round(5) +
             sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail.prorate_discount.round(5)).to eql(discount_total)
      expect(sale_modifier_3.prorate_surcharge.round(5) + sale_modifier_4.prorate_surcharge.round(5) + sale_detail_2.prorate_surcharge.round(5) +
             sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail.prorate_surcharge.round(5)).to eql(sale.surcharge_fee + 0.00001) # rounding precision 0.00001
      expect(sale_modifier_3.total_amount_prorate_discount.round(5) + sale_modifier_4.total_amount_prorate_discount.round(5) + sale_detail_2.total_line_discount_prorate.round(5) +
             sale_modifier_1.total_amount_prorate_discount.round(5) + sale_modifier_2.total_amount_prorate_discount.round(5) + sale_detail.total_line_discount_prorate.round(5)).to eql(sale_detail.total_amount + sale_detail_2.total_amount + sale.surcharge_fee - discount_total + 0.00001) # 0.00001 ROUNDING
    end
  end

  context 'when have discount fee, applied promos, surcharge fee, and has 0 qty' do
    before do
      sale_detail = local_sale_transaction.sale_detail_transactions.first
      sale_detail.update!(total_amount: 25000 + sale_detail.sale_detail_modifiers.sum(&:total_line_amount))

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.update!(total_amount: 35000 + sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount))

      local_sale_transaction.subtotal = sale_detail.total_amount + sale_detail_2.total_amount
      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000

      # promo item deleted bcs no modifier related
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2]

      local_sale_transaction.save!

      sale_modifier = sale_detail_2.sale_detail_modifiers.second
      sale_modifier.update_columns(quantity: 0)

      sale_modifier = sale_detail.sale_detail_modifiers.second
      sale_modifier.update_columns(quantity: 0)
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale_detail = sale.sale_detail_transactions.first
      sale_detail.reload

      sale_detail.sale_transaction.send(:init_temp_variables)
      sale_detail.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(833.33333)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(111.11111)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(3277.77778)

      expect(sale_detail.prorate_discount.round(5)).to eql(5208.33333)
      expect(sale_detail.prorate_surcharge.round(5)).to eql(694.44444)
      expect(sale_detail.total_line_discount_prorate.round(5)).to eql(20486.11111)

      sale = SaleTransaction.find(local_sale_transaction.id)
      sale_detail_2 = sale.sale_detail_transactions.second
      sale_detail_2.reload

      sale_detail_2.sale_transaction.send(:init_temp_variables)
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      sale_modifier_3 = sale_detail_2.sale_detail_modifiers.first
      expect(sale_modifier_3.prorate_discount.round(5)).to eql(1666.66667)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(222.22222)
      expect(sale_modifier_3.total_amount_prorate_discount.round(5)).to eql(6555.55556)

      expect(sale_detail_2.prorate_discount.round(5)).to eql(7291.66667)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(972.22222)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(28680.55556)
    end
  end

  context 'when have modifiers discount and surcharge, discount fee, applied promos, surcharge fee' do
    before do
      sale_details = local_sale_transaction.sale_detail_transactions
      sale_detail_1 = sale_details.detect { |det| det.product_id == latte_owned_branch_1.id }
      sale_detail_1.update!(total_amount: 25000 + sale_detail_1.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      sale_detail_2 = sale_details.detect { |det| det.product_id == coffee.id }
      sale_detail_2.update!(total_amount: 35000 + sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount_2.total_line_amount + modifier_surcharge_2.total_line_amount)

      local_sale_transaction.subtotal = sale_detail_1.total_amount + sale_detail_2.total_amount
      sale_detail_1.sale_detail_modifiers << modifier_discount
      sale_detail_1.sale_detail_modifiers << modifier_surcharge

      sale_detail_2.sale_detail_modifiers << modifier_discount_2
      sale_detail_2.sale_detail_modifiers << modifier_surcharge_2

      local_sale_transaction.discount_fee = 10000
      local_sale_transaction.surcharge_fee = 2000
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2, local_promo_item_1, local_promo_item_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale = SaleTransaction.find(local_sale_transaction.id)
      sale.send(:init_temp_variables)
      sale_detail_1 = sale.sale_detail_transactions.detect { |det| det.product_id == latte_owned_branch_1.id }
      sale_detail_1.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_1.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_1.send(:calculate_total_amount_prorate_discount)

      modifiers = sale_detail_1.sale_detail_modifiers
      sale_modifier_1 = modifiers.detect { |modifier| modifier.product_id == sugar_modifier_owned_branch_1.id }
      expect(sale_modifier_1.prorate_discount.round(5)).to eql(1285.71429)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2380.95238)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(5095.2381)

      sale_modifier_2 = modifiers.detect { |modifier| modifier.product_id == local_product_modifier.id }
      expect(sale_modifier_2.prorate_discount.round(5)).to eql(1928.57143)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3571.42857)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(7642.85714)

      expect(sale_detail_1.prorate_discount.round(5)).to eql(8035.71429)
      expect(sale_detail_1.prorate_surcharge.round(5)).to eql(14880.95238)
      expect(sale_detail_1.total_line_discount_prorate.round(5)).to eql(31845.2381)
      expect(sale_detail_1.total_line_discount_prorate.round(5) +
             sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(44583.33334)

      sale_detail_2 = sale.sale_detail_transactions.detect { |det| det.product_id == coffee.id }
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      product_modifiers = sale_detail_2.sale_detail_modifiers.select { |modifier| modifier.product_id.present? }
      sale_modifier_3 = product_modifiers.detect { |modifier| modifier.product_id == milk_modifier_owned_branch_1.id }
      expect(sale_modifier_3.prorate_discount.round(5)).to eql(2000)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(3078.78788)
      expect(sale_modifier_3.total_amount_prorate_discount.round(3)).to eql(9078.788)

      sale_modifier_4 = product_modifiers.detect { |modifier| modifier.product_id == local_product_modifier_2.id }
      expect(sale_modifier_4.prorate_discount.round(5)).to eql(3000)
      expect(sale_modifier_4.prorate_surcharge.round(5)).to eql(4618.18182)
      expect(sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(13618.18182)

      expect(sale_detail_2.prorate_discount.round(4)).to eql(8750)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(13469.69697)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(39719.69697)
      expect(sale_detail_2.total_line_discount_prorate.round(5) +
             sale_modifier_3.total_amount_prorate_discount.round(5) +
             sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(62416.66667)

      discount_total = sale.discount_promo_total_order_only + sale.discount_fee +
                       (modifier_discount.total_line_amount * -1) + (modifier_discount_2.total_line_amount * -1)
      surcharge_total = sale.surcharge_fee + modifier_surcharge.total_line_amount + modifier_surcharge_2.total_line_amount
      expect(discount_total).to eql(25000)
      expect(sale_modifier_3.prorate_discount.round(5) + sale_modifier_4.prorate_discount.round(5) + sale_detail_2.prorate_discount.round(5) +
              sale_modifier_1.prorate_discount.round(5) + sale_modifier_2.prorate_discount.round(5) + sale_detail_1.prorate_discount.round(5)).to eql(discount_total + 0.00001) # rounding precision
      expect(sale_modifier_3.prorate_surcharge.round(5) + sale_modifier_4.prorate_surcharge.round(5) + sale_detail_2.prorate_surcharge.round(5) +
              sale_modifier_1.prorate_surcharge.round(5) + sale_modifier_2.prorate_surcharge.round(5) + sale_detail_1.prorate_surcharge.round(5)).to eql(surcharge_total) # rounding precision 0.00001
      expect(sale_modifier_3.total_amount_prorate_discount.round(5) + sale_modifier_4.total_amount_prorate_discount.round(5) + sale_detail_2.total_line_discount_prorate.round(5) +
              sale_modifier_1.total_amount_prorate_discount.round(5) + sale_modifier_2.total_amount_prorate_discount.round(5) + sale_detail_1.total_line_discount_prorate.round(5))
      .to eql(sale_detail_1.total_amount + sale_detail_2.total_amount + sale.surcharge_fee - sale.discount_fee - sale.discount_promo_total_order_only + 0.00001) # 0.00001 ROUNDING
    end
  end

  context 'when sale detail is 0, have modifiers discount and surcharge, discount fee, applied promos, surcharge fee' do
    let(:new_local_sale_transaction) do
      SaleTransaction.find(local_sale_transaction.id)
    end

    before do
      sale_detail_1 = local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.sale_detail_modifiers.destroy_all
      sale_detail_1.reload
      sale_detail_1.update_columns(total_line_amount: 0)

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.sale_detail_modifiers.destroy_all
      sale_detail_2.reload
      sale_detail_2.update_columns(total_line_amount: 0)

      local_sale_transaction.subtotal = sale_detail_1.total_amount + sale_detail_2.total_amount
      sale_detail_1.sale_detail_modifiers << modifier_discount
      sale_detail_1.sale_detail_modifiers << modifier_surcharge

      sale_detail_2.sale_detail_modifiers << modifier_discount_2
      sale_detail_2.sale_detail_modifiers << modifier_surcharge_2

      sale_detail_1.update_columns(total_amount: sale_detail_1.sale_detail_modifiers.sum(&:total_line_amount))
      sale_detail_2.update_columns(total_amount: sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount))

      local_sale_transaction.discount_fee = 1000000
      local_sale_transaction.surcharge_fee = 2000
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2, local_promo_item_1, local_promo_item_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale_detail_1 = new_local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_1.send(:calculate_total_amount_prorate_discount)

      # promo total order is 1mio 5k
      expect(sale_detail_1.prorate_discount.round(5)).to eql(21000)
      expect(sale_detail_1.prorate_surcharge.round(5)).to eql(21000) # 2000 modifiers + (1/2 * 20000 surcharge total)
      expect(sale_detail_1.total_line_discount_prorate.round(5)).to eql(0)
      expect(sale_detail_1.total_amount_prorate_discount.round(5)).to eql(0)

      sale_detail_2 = new_local_sale_transaction.sale_detail_transactions.second

      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)
      expect(sale_detail_2.prorate_discount.round(4)).to eql(21000)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(21000) # 2000 modifiers + (1/2 * 20000 surcharge total)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(0)
      expect(sale_detail_2.total_amount_prorate_discount.round(5)).to eql(0)
    end
  end

  context 'when have modifiers discount and surcharge, discount fee, applied promos, surcharge fee, and discount fee greater than product cost' do
    let(:new_local_sale_transaction) do
      SaleTransaction.find(local_sale_transaction.id)
    end

    before do
      sale_detail_1 = local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.update!(total_amount: 25000 + sale_detail_1.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount.total_line_amount + modifier_surcharge.total_line_amount)

      sale_detail_2 = local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.update!(total_amount: 35000 + sale_detail_2.sale_detail_modifiers.sum(&:total_line_amount) +
                                          modifier_discount_2.total_line_amount + modifier_surcharge_2.total_line_amount)

      local_sale_transaction.subtotal = sale_detail_1.total_amount + sale_detail_2.total_amount
      sale_detail_1.sale_detail_modifiers << modifier_discount
      sale_detail_1.sale_detail_modifiers << modifier_surcharge

      sale_detail_2.sale_detail_modifiers << modifier_discount_2
      sale_detail_2.sale_detail_modifiers << modifier_surcharge_2

      local_sale_transaction.discount_fee = 500_000
      local_sale_transaction.surcharge_fee = 2000
      local_sale_transaction.applied_promos = [local_promo_order_1, local_promo_order_2, local_promo_item_1, local_promo_item_2]

      local_sale_transaction.save!
    end

    it 'should return correct calculation' do
      sale_detail_1 = new_local_sale_transaction.sale_detail_transactions.first
      sale_detail_1.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_1.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_1.send(:calculate_total_amount_prorate_discount)

      sale_modifier_1 = sale_detail_1.sale_detail_modifiers.first
      expect(sale_modifier_1.prorate_discount).to eql(sale_modifier_1.prorate_surcharge + sale_modifier_1.total_line_amount)
      expect(sale_modifier_1.prorate_surcharge.round(5)).to eql(2380.95238)
      expect(sale_modifier_1.total_amount_prorate_discount.round(5)).to eql(0)

      sale_modifier_2 = sale_detail_1.sale_detail_modifiers.second
      expect(sale_modifier_2.prorate_discount).to eql(sale_modifier_2.prorate_surcharge + sale_modifier_2.total_line_amount)
      expect(sale_modifier_2.prorate_surcharge.round(5)).to eql(3571.42857)
      expect(sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(0)

      expect(sale_detail_1.prorate_discount).to eql(sale_detail_1.prorate_surcharge + sale_detail_1.total_line_amount)
      expect(sale_detail_1.prorate_surcharge.round(5)).to eql(14880.95238)
      expect(sale_detail_1.total_line_discount_prorate.round(5)).to eql(0)
      expect(sale_detail_1.total_line_discount_prorate.round(5) +
             sale_modifier_1.total_amount_prorate_discount.round(5) +
             sale_modifier_2.total_amount_prorate_discount.round(5)).to eql(0.00)

      sale_detail_2 = new_local_sale_transaction.sale_detail_transactions.second
      sale_detail_2.calculate_all_modifiers_amount_up_to_net_sales
      sale_detail_2.send(:calculate_prorate_discount_and_foc_and_surcharge)
      sale_detail_2.send(:calculate_total_amount_prorate_discount)

      product_modifiers = sale_detail_2.sale_detail_modifiers.select { |modifier| modifier.product_id.present? }
      sale_modifier_3 = product_modifiers.first
      expect(sale_modifier_3.prorate_discount).to eql(sale_modifier_3.prorate_surcharge + sale_modifier_3.total_line_amount)
      expect(sale_modifier_3.prorate_surcharge.round(5)).to eql(3078.78788)
      expect(sale_modifier_3.total_amount_prorate_discount.round(4)).to eql(0)

      sale_modifier_4 = product_modifiers.second
      expect(sale_modifier_4.prorate_discount).to eql(sale_modifier_4.prorate_surcharge + sale_modifier_4.total_line_amount)
      expect(sale_modifier_4.prorate_surcharge.round(5)).to eql(4618.18182)
      expect(sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(0)

      expect(sale_detail_2.prorate_discount).to eql(sale_detail_2.prorate_surcharge + sale_detail_2.total_line_amount)
      expect(sale_detail_2.prorate_surcharge.round(5)).to eql(13469.69697)
      expect(sale_detail_2.total_line_discount_prorate.round(5)).to eql(0)
      expect(sale_detail_2.total_line_discount_prorate.round(5) +
             sale_modifier_3.total_amount_prorate_discount.round(5) +
             sale_modifier_4.total_amount_prorate_discount.round(5)).to eql(0.00)
    end
  end

  describe '.return_qty_ratio' do
    context 'when no return' do
      it 'should be zero' do
        result = sale_detail_transaction_2.return_qty_ratio
        expect(result).to be_zero
      end
    end

    context 'when has return not item not delivered' do
      before do
        sales_return_5
      end

      it 'should be zero' do
        result = sale_transaction_8.sale_detail_transactions.second.return_qty_ratio
        expect(result).to be_zero
      end
    end

    context 'when has return item not delivered but void' do
      before do
        sales_return_5.update_columns(
          status: SalesReturn.statuses[:void],
          refund_reason: SalesReturn.refund_reasons[:item_not_delivered]
        )
      end

      it 'should be zero' do
        result = sale_transaction_8.sale_detail_transactions.second.return_qty_ratio
        expect(result).to be_zero
      end
    end

    context 'when has return item not delivered' do
      before do
        sales_return_5.update_columns(
          refund_reason: SalesReturn.refund_reasons[:item_not_delivered]
        )
      end

      it 'should be zero' do
        result = sale_transaction_8.sale_detail_transactions.second.return_qty_ratio
        expect(result).to eq(1)
      end
    end
  end

  context 'when have recipe line custom', search: true do
    context 'when have category section' do
      it 'should return product recipe with storage section' do
        espresso_product_category_mapping
        sale_detail = sale_transaction_with_recipe_line_custom.sale_detail_transactions.first
        expect(sale_detail.product_recipe).to eq(
          [{"product_id"=>espresso.id,
            "product_unit_id"=>espresso.product_unit_id,
            "product_unit_name"=>espresso.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"1.0",
            "storage_section_id"=>branch_1_ingredient_section_1.id},
          {"product_id"=>milk.id,
            "product_unit_id"=>milk.product_unit_id,
            "product_unit_name"=>milk.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"8.0",
            "storage_section_id"=>branch_1_ingredient_section_1.id},
          {"product_id"=>coffee.id,
            "product_unit_id"=>coffee.product_unit_id,
            "product_unit_name"=>coffee.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"2.0",
            "storage_section_id"=>branch_1_ingredient_section_1.id}]
        )
      end
    end

    context 'when have default storage section' do
      it 'should return product recipe with storage section' do
        espresso.update(product_category_id: nil)
        branch_1_refrigerator
        sale_detail = sale_transaction_with_recipe_line_custom.sale_detail_transactions.first
        expect(sale_detail.product_recipe).to eq(
          [{"product_id"=>espresso.id,
            "product_unit_id"=>espresso.product_unit_id,
            "product_unit_name"=>espresso.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"1.0",
            "storage_section_id"=>branch_1_refrigerator.id},
          {"product_id"=>milk.id,
            "product_unit_id"=>milk.product_unit_id,
            "product_unit_name"=>milk.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"8.0",
            "storage_section_id"=>branch_1_refrigerator.id},
          {"product_id"=>coffee.id,
            "product_unit_id"=>coffee.product_unit_id,
            "product_unit_name"=>coffee.product_unit.name,
            "product_no_stock"=>false,
            "product_unit_conversion_qty"=>"1.0",
            "quantity"=>"2.0",
            "storage_section_id"=>branch_1_refrigerator.id}]
        )
      end
    end
  end
end
