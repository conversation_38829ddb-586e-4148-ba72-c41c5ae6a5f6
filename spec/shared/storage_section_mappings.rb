require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/recipes'
require './spec/shared/storage_sections'

RSpec.shared_context 'storage section mappings creations' do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'recipes creations'
  include_context 'storage sections creations'

  let(:section_1) { ingredient_storage_section_at_central_kitchen }
  let(:section_2) { yield_storage_section_at_central_kitchen }

  let(:storage_section_mappings_for_soy_milk) do
    # product section
    create(:storage_section_mapping, location: owned_branch_1,
                                     storage_section: branch_1_ingredient_section_1,
                                     mapping_type: :out,
                                     mappable: soy_milk)

    # category section
    create(:storage_section_mapping, location: owned_branch_1,
                                     storage_section: branch_1_ingredient_section_2,
                                     mapping_type: :out,
                                     mappable: soy_milk.product_category)
  end

  let(:product_storage_section_mappings) do
    [
      cheese_burger_recipe_batches,
      spicy_burger_recipe_batches,
      latte_recipe_made_to_order,
      espresso_recipe_made_to_order
    ].flat_map do |recipe|
      [
        create(:storage_section_mapping, location: central_kitchen,
                                         storage_section: section_1,
                                         mapping_type: :out,
                                         mappable: recipe.product),
        create(:storage_section_mapping, location: central_kitchen,
                                         storage_section: section_2,
                                         mapping_type: :in,
                                         mappable: recipe.product)
      ]
    end
  end

  let(:category_storage_section_mappings) do
    [ coffee_drinks_category, burgers_category, tea_category ].flat_map do |category|
      [
        create(:storage_section_mapping, location: central_kitchen,
                                         storage_section: section_1,
                                         mapping_type: :out,
                                         mappable: category),
        create(:storage_section_mapping, location: central_kitchen,
                                         storage_section: section_2,
                                         mapping_type: :in,
                                         mappable: category)
      ]
    end
  end

  let(:main_product_category_storage_section_mapping) do
    create(:storage_section_mapping, location: owned_branch_1,
                                     storage_section: branch_1_ingredient_section_3,
                                     mapping_type: :out,
                                     mappable: drink_category)
  end

  let(:spicy_burger_recipe_made_to_order_storage_section_mappings) do
    recipe = spicy_burger_recipe_made_to_order

    ids = [
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second).id
    ]


    StorageSectionMapping.where(id: ids)
  end

  let(:espresso_recipe_made_to_order_with_substitute_ingredients_storage_section_mappings) do
    recipe = espresso_recipe_made_to_order_with_substitute_ingredients

    ids = [
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second).id,

      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_2,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first.recipe_line_substitutes.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_2,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second.recipe_line_substitutes.first).id
    ]


    StorageSectionMapping.where(id: ids)
  end

  let(:cheese_burger_recipe_batches_storage_section_mappings) do
    recipe = cheese_burger_recipe_batches

    ids = [
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_yield_section_1,
                                       mapping_type: :in,
                                       mappable: recipe).id,

      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second).id
    ]

    StorageSectionMapping.where(id: ids)
  end

  let(:latte_recipe_batches_with_substitute_ingredients_storage_section_mappings) do
    recipe = latte_recipe_batches_with_substitute_ingredients

    ids = [
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_yield_section_2,
                                       mapping_type: :in,
                                       mappable: recipe).id,

      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second).id,

      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_2,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.first.recipe_line_substitutes.first).id,
      create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_2,
                                       mapping_type: :out,
                                       mappable: recipe.recipe_lines.second.recipe_line_substitutes.first).id
    ]


    StorageSectionMapping.where(id: ids)
  end

  let(:espresso_product_category_mapping) do
    create(:storage_section_mapping, location: owned_branch_1,
                                       storage_section: branch_1_ingredient_section_1,
                                       mapping_type: :out,
                                       mappable: espresso.product_category)
  end
end
