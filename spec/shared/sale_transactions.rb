require './spec/shared/locations'
require './spec/shared/payments'
require './spec/shared/products'
require './spec/shared/loyalty'
require './spec/shared/taxes'
require './spec/shared/order_types'
require './spec/shared/dine_ins'
require './spec/shared/recipe_line_customs'

RSpec.shared_context "sale transaction creations" do
  include_context 'order_types creations'
  include_context 'locations creations'
  include_context 'payments creations'
  include_context 'products creations'
  include_context 'loyalty creations'
  include_context 'taxes creations'
  include_context 'dine ins creations'
  include_context 'recipe line customs creations'

  def recalculate_sale_transaction_using_sale_detail_amounts(sale_transaction)
    amounts = {
      gross_sales: 0,
      discount: 0,
      surcharge: 0,
      net_sales: 0,
      service_charge_fee: 0,
      tax_fee: 0,
      additional_charge_fee: 0,
      rounding: 0,
      total: 0,
      total_subsidize: 0,
      total_processing_fee: 0
    }
    sale_transaction.sale_detail_transactions.each do |sale_detail|
      amounts[:gross_sales] += sale_detail.include_modifiers_gross_sales.to_d
      amounts[:discount] += sale_detail.include_modifiers_prorate_discount_before_tax.to_d
      amounts[:surcharge] += sale_detail.include_modifiers_prorate_surcharge_before_tax.to_d
      amounts[:net_sales] += sale_detail.include_modifiers_net_sales.to_d
      amounts[:service_charge_fee] += sale_detail.include_modifiers_prorate_service_charge_before_tax.to_d
      amounts[:tax_fee] += sale_detail.include_modifiers_tax_fee.to_d
      amounts[:additional_charge_fee] += sale_detail.include_modifiers_prorate_additional_charge_fee.to_d
      amounts[:rounding] += sale_detail.include_modifiers_prorate_rounding.to_d
      amounts[:total] += sale_detail.include_modifiers_net_sales_after_tax.to_d
      amounts[:total_subsidize] += sale_detail.include_modifiers_prorate_total_subsidized.to_d
      amounts[:total_processing_fee] += sale_detail.include_modifiers_prorate_processing_fee.to_d
    end
    sale_transaction.update_columns(gross_sales: amounts[:gross_sales], total_discount_before_tax: amounts[:total_discount_before_tax],
                                    total_prorate_surcharge_before_tax: amounts[:surcharge], net_sales: amounts[:net_sales], new_net_sales: amounts[:net_sales],
                                    service_charge_fee_before_tax: amounts[:service_charge_fee_before_tax], tax_fee: amounts[:tax_fee],
                                    online_platform_fee: amounts[:additional_charge_fee], rounding: amounts[:rounding],
                                    net_sales_after_tax: amounts[:total], total_subsidize: amounts[:total_subsidize],
                                    total_processing_fee: amounts[:total_processing_fee])
  end

  # DISCLAIMER : This equation is only made for specs purpose only
  # Another cases might need different approach to calculate
  def calculate_amounts_for_sale_transaction_param(sale_param)
    sale_param[:sale_detail_transactions_attributes].each do |sale_detail_param|
      total_modifier_amount = 0
      sale_detail_param[:sale_detail_modifiers_attributes].each do |modifier_param|
        modifier_param[:total_line_amount] = modifier_param[:price].to_d * modifier_param[:quantity].to_d
        total_modifier_amount += modifier_param[:total_line_amount]
      end

      sale_detail_param[:total_line_amount] = sale_detail_param[:price].to_d * sale_detail_param[:quantity].to_d
      sale_detail_param[:total_amount] = sale_detail_param[:total_line_amount] + total_modifier_amount
    end
  end

  # DISCLAIMER : This equation is only made for specs purpose only
  # Another cases might need different approach to calculate
  def calculate_amounts_for_sale_transaction(sale_transaction)
    sale_details = sale_transaction.sale_detail_transactions
    total_promo_items = 0

    available_order_type_ids = sale_details.map(&:order_type_id).uniq
    service_charge_settings = ServiceChargeLocation.where(location_id: sale_transaction.location_id,
                                                          order_type_id: available_order_type_ids)
                                                   .index_by(&:order_type_id)

    service_charge_fee = 0
    tax_fee = 0
    sale_transaction.set_calculate_tax_after_discount
    sale_transaction.set_calculate_service_charge_after_discount
    sale_details.each do |sale_detail|
      sale_detail.order_type = sale_transaction.order_type if sale_detail.order_type.blank? && sale_transaction.order_type.present?
      sale_detail.sale_detail_modifiers.each do |modifier|
        total_promo_items += modifier.total_line_amount.to_d if modifier.product_id.nil? && modifier.total_line_amount.to_d.negative?
      end

      service_charge_setting = service_charge_settings[sale_detail.order_type_id]
      if service_charge_setting.present? && service_charge_setting.service_charge.to_d.positive?
        service_charge_fee += sale_detail.total_amount * service_charge_setting.service_charge.to_d / 100
      end
    end

    sale_transaction.service_charge_fee = service_charge_fee
    sale_transaction.calculate_report_data
    sale_details = sale_transaction.sale_detail_transactions
    sale_transaction.subtotal = sale_details.sum { |sale_detail| sale_detail.send(:total_amount_before_adjustment) }
    sale_transaction.gross_sales = sale_transaction.calculate_report_gross_sales
    sale_transaction.tax_fee = sale_details.sum(&:tax_fee)
    sale_transaction.new_net_sales = sale_transaction.gross_sales - sale_transaction.total_discount_before_tax +
                                     sale_transaction.total_prorate_surcharge_before_tax - sale_transaction.total_free_of_charge_fee_before_tax
    sale_transaction.net_sales = sale_transaction.new_net_sales

    raise ::Errors::UnprocessableEntity, I18n.t('errors.messages.negative_amount') if sale_transaction.tax_fee.negative? || sale_transaction.new_net_sales.negative?
    sale_transaction.discount_total = sale_transaction.discount_fee -
                                      sale_transaction.surcharge_fee +
                                      sale_transaction.discount_promo_total_order_only
                                      + total_promo_items
    sale_transaction.net_sales_after_tax = sale_transaction.new_net_sales + sale_transaction.tax_fee +
                                           sale_transaction.online_platform_fee + sale_transaction.service_charge_fee_before_tax +
                                           sale_transaction.rounding

    sale_transaction
  end

  def create_sale_multilines(sale_transaction, location, quantity: 1)
    sale_transaction.location_id = location.id
    sale_transaction.sale_detail_transactions << build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: quantity)
    sale_transaction.sale_detail_transactions << build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: quantity)
    sale_transaction.save!
    sale_transaction
  end

  def default_sale_transaction_attributes
    # NOTE: Can't change this method to variable let. When you change this as let variable, it will not use the time specific in travel_to
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
          sales_no: SecureRandom.uuid, order_employee_id: owner.id,
          cashier_employee_id: owner.id, order_type_id: order_type.id, net_sales: 5000)
    sale_transaction.payments << payment
    sale_transaction
  end

  let(:my_customer) { create(:customer, brand: brand, location_ids: [owned_branch_1.id]) }
  let(:payment_method) { create(:payment_method, brand: brand) }
  let(:payment_method_2) { create(:payment_method, brand: brand, fixed_fee: 15_000) }
  let(:payment_method_3) { create(:payment_method, name: "Payment Method 3", brand: brand, fixed_fee: 15_000) }
  let(:payment) { build(:payment, payment_method_id: payment_method.id) }
  let(:payment_2) { build(:payment, payment_method_id: payment_method_2.id) }
  let(:payment_with_processing_fee) { build(:payment, payment_method_id: payment_method.id, payment_processing_fee: 10_400) }
  let(:payment_3) { build(:payment, payment_method_id: payment_method_3.id) }
  let(:return_payment) { build(:return_payment, payment_method_id: payment_method.id) }
  let(:sale_detail_modifier) do
    build(
      :sale_detail_modifier,
      product_id: sugar_modifier_owned_branch_1.id,
      product_unit_id: sugar_modifier_owned_branch_1.product_unit.id
    )
  end
  let(:sale_detail_modifier_2) do
    build(
      :sale_detail_modifier,
      product_id: milk_modifier_owned_branch_1.id,
      product_unit_id: milk_modifier_owned_branch_1.product_unit.id
    )
  end

  let(:latte_as_parent_modifier) do
    build(
      :sale_detail_modifier,
      product_id: latte.id,
      product_unit_id: latte.product_unit.id,
      meta: {
        option_set_id: opsi_minuman.id,
        option_set_name: opsi_minuman.name,
        option_set_option_id: latte.option_set_options.first.id
      }
    )
  end
  let(:few_sugar_as_child_modifier) do
    build(
      :sale_detail_modifier,
      product_id: few_sugar.id,
      product_unit_id: few_sugar.product_unit.id,
      parent_id: latte_as_parent_modifier.id,
      meta: {
        option_set_id: sugar_level.id,
        option_set_name: sugar_level.name,
        option_set_option_id: few_sugar_level.id
      }
    )
  end

  let(:sale_detail_modifier_4) do
    build(
      :sale_detail_modifier,
      product_id: coffee_milk.id,
      product_unit_id: coffee_milk.product_unit.id,
      option_set_quantity: 3
    )
  end

  let(:sale_detail_modifier_5) do
    build(
      :sale_detail_modifier,
      product_id: ginger_milk.id,
      product_unit_id: ginger_milk.product_unit.id,
      option_set_quantity: 3
    )
  end
  let(:sale_detail_transaction) do
    build(
      :sale_detail_transaction,
      product_id: latte_owned_branch_1.id,
      product_unit_id: latte_owned_branch_1.product_unit_id,
      sale_detail_modifiers: [sale_detail_modifier]
    )
  end
  let(:sale_detail_redeem_transaction) do
    build(
      :sale_detail_redeem_transaction,
      loyalty_id: loyalty_product_1.loyalty.id,
      loyalty_product_id: loyalty_product_1.id,
      product_id: loyalty_product_1.product.id,
      product_unit_id: loyalty_product_1.product.product_unit_id
    )
  end
  let(:sale_detail_transaction_2) do
    build(
      :sale_detail_transaction,
      product_id: coffee.id,
      product_unit_id: coffee.product_unit_id,
      sale_detail_modifiers: [sale_detail_modifier_2]
    )
  end
  let(:sale_detail_transaction_with_nested_option_sets) do
    build(
      :sale_detail_transaction,
      product_id: paket_family.id,
      product_unit_id: family_pack.id,
      sale_detail_modifiers: [latte_as_parent_modifier, few_sugar_as_child_modifier]
    )
  end
  let(:sale_detail_transaction_paket_family_without_option_sets) do
    build(
      :sale_detail_transaction,
      product_id: paket_family.id,
      product_unit_id: family_pack.id
    )
  end

  let(:sale_transaction_with_nested_option_sets) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction_with_nested_option_sets, sale_detail_transaction_paket_family_without_option_sets],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_americano) do
    payment = build(:payment, payment_method_id: payment_method_2.id)

    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [
        build(
          :sale_detail_transaction,
          product_id: americano.id,
          product_unit_id: americano.product_unit_id,
          price: 4_500,
          total_line_amount: 4_500,
          quantity: 1,
          sale_detail_modifiers: [
              build(:sale_detail_modifier, product_id: ice.id, product_unit_id: ice.product_unit.id, meta: {"option_set_option_id" => pos_ice_id_on_america}),
              build(:sale_detail_modifier, product_id: small.id, product_unit_id: small.product_unit.id, meta: {"option_set_option_id" => pos_small_id_on_america}),
              build(:sale_detail_modifier, product_id: cake.id, product_unit_id: cake.product_unit.id, meta: {"option_set_option_id" => pos_cake_id_on_america})
            ]
        )
      ],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )


    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale

  end

  let(:sale_transaction) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_2_payments) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment, payment_2],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 8,
      metadata: { open_time: "Wed, 25 Sep 2024 03:34:08.********* UTC +00:00" }
    )
  end

  let(:sale_transaction_with_2_payments_2) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee, payment_2],
      sale_detail_transactions: [sale_detail_transaction_2],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 8,
      metadata: { open_time: "Wed, 25 Sep 2024 03:34:08.********* UTC +00:00" }
    )
  end

  let(:sale_transaction_with_payment_3) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_3],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 8,
      metadata: { open_time: "Wed, 25 Sep 2024 03:34:08.********* UTC +00:00" }
    )
  end

  let(:sale_transaction_with_redeem) do
    loyalty_metadata = {
      'loyalty': loyalty_product_1.loyalty.attributes.merge(
        {
          loyalty_products: [
            {
              id: loyalty_product_1.id,
              quantity: 2,
              product_id: loyalty_product_1.product.id,
              option_sets: [],
              point_needed: loyalty_product_1.point_needed,
              product_category_id: loyalty_product_1.product.product_category_id
            }
          ]
        }
      )
    }

    metadata = loyalty_metadata

    sale_detail_transactions = [
      sale_detail_transaction
    ]

    sale_detail_redeem_transactions = [
      sale_detail_redeem_transaction
    ]

    payments = [
      payment
    ]

    sale_transaction = create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: payments,
      sale_detail_transactions: sale_detail_transactions,
      sale_detail_redeem_transactions: sale_detail_redeem_transactions,
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      metadata: metadata,
      customer_id: my_customer.id,
      order_type_id: order_type.id
    )

    net_sales_after_tax = sale_transaction.new_net_sales + sale_transaction.tax_fee + sale_transaction.service_charge_fee
    sale_transaction.update(net_sales_after_tax: net_sales_after_tax)

    sale_transaction
  end


  let(:sale_transaction_3) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_4) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 4,
      metadata: { open_time: "Thu, 26 Sep 2024 05:34:08.********* UTC +00:00" }
    )
  end

  let(:sale_transaction_with_advanced_guest) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 7,
      number_of_male_guests: 4,
      number_of_female_guests: 3,
      number_of_senior_guests: 1,
      number_of_adult_guests: 2,
      number_of_youth_guests: 3,
      number_of_child_guests: 1,
      metadata: { open_time: "Fri, 27 Sep 2024 04:51:08.********* UTC +00:00" }
    )
  end

  let(:other_sale_transaction_with_advanced_guest) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 2,
      number_of_male_guests: 1,
      number_of_female_guests: 1,
      number_of_senior_guests: 0,
      number_of_adult_guests: 2,
      number_of_youth_guests: 0,
      number_of_child_guests: 0,
      metadata: { open_time: "Fri, 27 Sep 2024 05:51:08.********* UTC +00:00" }
    )
  end

  let(:sale_transaction_with_advanced_guest_from_other_location) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 4,
      number_of_male_guests: 3,
      number_of_female_guests: 1,
      number_of_senior_guests: 0,
      number_of_adult_guests: 2,
      number_of_youth_guests: 2,
      number_of_child_guests: 0
    )
  end

  let(:sale_transaction_with_advanced_guest_from_another_location) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_3.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      number_of_guests: 3,
      number_of_male_guests: 2,
      number_of_female_guests: 1,
      number_of_senior_guests: 0,
      number_of_adult_guests: 2,
      number_of_youth_guests: 1,
      number_of_child_guests: 0
    )
  end

  let(:sale_transaction_without_receipt_no_for_owned_branch_1) do
    sale_transaction = default_sale_transaction_attributes
    sale_transaction.location_id = owned_branch_1.id

    sale_transaction.receipt_no = nil

    sale_transaction.sale_detail_transactions << build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 1)
    sale_transaction.sale_detail_transactions << build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: 1)
    sale_transaction.save!
  end

  let(:sale_transaction_multi_lines) do
    sale_transaction = default_sale_transaction_attributes
    create_sale_multilines(sale_transaction, franchise_branch_1)
    sale_transaction
  end

  let(:sale_transaction_multi_lines_2) do
    sale_transaction = default_sale_transaction_attributes
    create_sale_multilines(sale_transaction, franchise_branch_1, quantity: 2)
    sale_transaction
  end

  let(:sale_transaction_multi_lines_3) do
    sale_transaction = default_sale_transaction_attributes
    create_sale_multilines(sale_transaction, franchise_branch_1)
    sale_transaction
  end

  let(:sale_transaction_multi_lines_4) do
    sale_transaction = default_sale_transaction_attributes
    create_sale_multilines(sale_transaction, owned_branch_1)
    sale_transaction
  end

  let(:sale_transaction_with_platform_fee) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:preorder_sale_transaction) do
    completed_customer_preorder.save!

    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: 'PO-ips-00001',
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      is_from_preorder: true,
      customer_order_id: completed_customer_preorder.id,
      net_sales_after_tax: completed_customer_preorder.total_amount
    )
  end

  let(:grab_discount_sharing_sale_transaction) do
    completed_customer_preorder.save! # dont mind the customer order instance
    completed_customer_preorder.update_columns(food_delivery_integration_id: 1)

    sale_detail_transaction.sale_detail_modifiers.each do |modifier|
      modifier.tax_setting = 'price_include_tax'
      modifier.tax_rate = 10
    end

    sale_detail_transaction.tax_rate = 10
    sale_detail_transaction.tax_setting = 'price_include_tax'

    promo.promo_reward.update!(discount_external_cost: 55, discount_in_house_cost: 45)
    applied_promotions = [
      { 'id' => promo.id, 'name' => promo.name,
        'amount' => 20_007,
        'discount_external_cost' => 55,
        'food_delivery_integration_amount' => (20_007 / 1.1).round(6) }
    ]

    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      applied_promos: applied_promotions,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: 'grab-sharing-00001',
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      is_from_preorder: true,
      customer_order_id: completed_customer_preorder.id,
      net_sales_after_tax: completed_customer_preorder.total_amount
    )

    sale.sale_detail_transactions.each do |sale_detail_transaction|
      sale_detail_transaction.sale_detail_modifiers.each(&:calculate_prorate_discount_and_surcharge)
      sale_detail_transaction.calculate_prorate_discount_and_foc_and_surcharge
    end
    sale.calculate_prorate_discount_and_surcharge_before_tax
    sale.save!
    sale
  end

  let(:grab_discount_sharing_sale_transaction_fifty_percent) do
    completed_customer_preorder.save! # dont mind the customer order instance
    completed_customer_preorder.update_columns(food_delivery_integration_id: 1)

    sale_detail_transaction.sale_detail_modifiers.each do |modifier|
      modifier.tax_setting = 'price_include_tax'
      modifier.tax_rate = 10
    end

    sale_detail_transaction.tax_rate = 10
    sale_detail_transaction.tax_setting = 'price_include_tax'

    promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
    applied_promotions = [
      { 'id' => promo.id, 'name' => promo.name,
        'amount' => 10003,
        'discount_external_cost' => 50,
        'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
    ]

    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      applied_promos: applied_promotions,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: 'grab-sharing-00002',
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      is_from_preorder: true,
      customer_order_id: completed_customer_preorder.id,
      net_sales_after_tax: completed_customer_preorder.total_amount
    )

    sale.sale_detail_transactions.each do |sale_detail_transaction|
      sale_detail_transaction.sale_detail_modifiers.each(&:calculate_prorate_discount_and_surcharge)
      sale_detail_transaction.calculate_prorate_discount_and_foc_and_surcharge
    end
    sale.calculate_prorate_discount_and_surcharge_before_tax
    sale.save!
    sale
  end

  let(:grab_discount_sharing_sale_transaction_eighty_nine_percent) do
    completed_customer_preorder.save! # dont mind the customer order instance
    completed_customer_preorder.update_columns(food_delivery_integration_id: 1)

    sale_detail_transaction.sale_detail_modifiers.each do |modifier|
      modifier.tax_setting = 'price_include_tax'
      modifier.tax_rate = 10
    end

    sale_detail_transaction.tax_rate = 10
    sale_detail_transaction.tax_setting = 'price_include_tax'

    promo.promo_reward.update!(discount_external_cost: 89, discount_in_house_cost: 11)
    applied_promotions = [
      { 'id' => promo.id, 'name' => promo.name,
        'amount' => 10003,
        'discount_external_cost' => 89,
        'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
    ]

    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      applied_promos: applied_promotions,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: 'grab-sharing-00003',
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      is_from_preorder: true,
      customer_order_id: completed_customer_preorder.id,
      net_sales_after_tax: completed_customer_preorder.total_amount
    )

    sale.sale_detail_transactions.each do |sale_detail_transaction|
      sale_detail_transaction.sale_detail_modifiers.each(&:calculate_prorate_discount_and_surcharge)
      sale_detail_transaction.calculate_prorate_discount_and_foc_and_surcharge
    end
    sale.calculate_prorate_discount_and_surcharge_before_tax
    sale.save!
    sale
  end

  let(:open_bill_sale_transaction) do
    merged_open_bill.save!

    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_order_id: merged_open_bill.id,
      number_of_guests: 3
    )
  end

  let(:online_ordering_sale_transaction) do
    delivery_order = create(
      :customer_delivery_order,
      :with_delivery_service_order,
      user: delivery_user,
      location: owned_branch_1
    )
    delivery_order.update(aasm_state: 'completed')

    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_order_id: delivery_order.id
    )
  end

  let(:back_date_sale_transaction) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      sales_time: Time.zone.now - 1.days,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:sale_transaction_with_2_lines) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction, sale_detail_transaction_2],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_3_lines) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [
        sale_detail_transaction,
        sale_detail_transaction_2,
        sale_detail_transaction_8],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_3_lines_2) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [
        sale_detail_transaction,
        sale_detail_transaction_2,
        sale_detail_transaction_9],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_2) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction_2],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_5) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:sale_transaction_6) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment_2],
      sale_detail_transactions: [sale_detail_transaction_2],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:sale_transaction_7) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment_2],
      sale_detail_transactions: [sale_detail_transaction_4],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:sale_transaction_8) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment_2],
      sale_detail_transactions: [sale_detail_transaction_4, sale_detail_transaction_5],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:payment_method_owned_branch_2) { create(:payment_method, name: 'Bank Transfer', brand_id: brand.id, fixed_fee: 1000, variable_fee: 0) }
  let(:payment_owned_branch_2) { build(:payment, payment_method_id: payment_method_owned_branch_2.id) }
  let(:sale_transaction_owned_branch_2) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_2.id,
      payments: [payment_owned_branch_2],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id
    )
  end

  let(:sale_transaction_without_category) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [
        build(:sale_detail_transaction, product_id: latte_no_category.id, product_unit_id: latte_no_category.product_unit_id)
      ],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      surcharge_fee: 2_500_000
    )

    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_without_category_2) do
    create(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [
        build(:sale_detail_transaction, product_id: latte_no_category.id, product_unit_id: latte_no_category.product_unit_id)
      ],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      surcharge_fee: 2_500_000
    )
  end

  let(:sale_transaction_with_inventory) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_inventory
    sale_transaction.save
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    sale_transaction
  end

  let(:sale_transaction_with_recipe) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_recipe
    sale_transaction.save
    sale_transaction
  end

  let(:sale_detail_transaction_with_recipe_line_custom) do
    product = recipe_line_custom.recipe.product
    product.recipe.recipe_line_customs << recipe_line_custom_with_detail
    product.recipe.save
    build(:sale_detail_transaction, product_id: product.id, product_unit_id: product.product_unit.id, order_type_id: order_type.id)
  end

  let(:sale_transaction_with_recipe_line_custom) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_recipe_line_custom
    sale_transaction.save
    sale_transaction
  end

  let(:sale_detail_transaction_with_modifier_and_recipe_line_custom) do
    product = recipe_line_custom_4.recipe.product
    product.recipe.recipe_line_customs << recipe_line_custom_with_detail_4
    product.recipe.save
    build(:sale_detail_transaction, product_id: product.id, product_unit_id: product.product_unit.id, order_type_id: order_type.id,
          sale_detail_modifiers: [sale_detail_modifier])
  end

  let(:sale_transaction_with_modifier_and_recipe_line_custom) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_modifier_and_recipe_line_custom
    sale_transaction.save
    sale_transaction
  end

  let(:sale_transaction_modfier_no_product) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_modfier_no_product
    sale_transaction.save
    sale_transaction
  end

  let(:sale_detail_transaction_with_inventory) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: green_sugar.id, product_unit_id: green_sugar.product_unit.id)
    sale_detail_transaction
  end

  let(:sale_detail_modifier) { build(:sale_detail_modifier, product_id: sugar_modifier_owned_branch_1.id, product_unit_id: sugar_modifier_owned_branch_1.product_unit.id,
    tax_setting: sugar_modifier_owned_branch_1.sell_tax_setting,
    tax_id: sugar_modifier_owned_branch_1.tax_id,
    tax_name: sugar_modifier_owned_branch_1.tax&.name,
    tax_rate: sugar_modifier_owned_branch_1.tax&.rate || 0) }
  let(:sale_detail_modifier_no_product) { build(:sale_detail_modifier, description: 'add charge') }
  let(:sale_detail_modifier_discount) { build(:sale_detail_modifier,
                                              product_id: nil,
                                              product_unit_id: nil,
                                              description: 'discount',
                                              quantity: 1,
                                              price: -10000,
                                              total_line_amount: -10000) }

  let(:sale_detail_modifier_discount_2) { build(:sale_detail_modifier,
                                              product_id: nil,
                                              product_unit_id: nil,
                                              description: 'discount',
                                              quantity: 1,
                                              price: -2000,
                                              total_line_amount: -2000) }

  let(:sale_detail_modifier_discount_3) { build(:sale_detail_modifier,
                                              product_id: nil,
                                              product_unit_id: nil,
                                              description: 'discount',
                                              quantity: 1,
                                              price: -3000,
                                              total_line_amount: -3000) }

  let(:sale_detail_modifier_surcharge) { build(:sale_detail_modifier,
                                              product_id: nil,
                                              product_unit_id: nil,
                                              description: 'surcharge',
                                              quantity: 1,
                                              price: 10000,
                                              total_line_amount: 10000) }

  let(:sale_detail_transaction_3) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier])
  end

  let(:sale_detail_transaction_4) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier_4, sale_detail_modifier_5])
  end

  let(:sale_detail_transaction_5) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id,
          sale_detail_modifiers: [])
  end

  let(:sale_detail_transaction_modfier_no_product) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier_no_product])
  end

  let(:sale_detail_transaction_with_discount) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 2,
          sale_detail_modifiers: [sale_detail_modifier, sale_detail_modifier_discount],
          order_type_id: order_type.id)
  end

  let(:sale_detail_transaction_with_discount_2) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 2,
          sale_detail_modifiers: [sale_detail_modifier_discount_2],
          order_type_id: order_type.id)
  end

  let(:sale_detail_transaction_with_discount_3) do
    build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 2,
          sale_detail_modifiers: [sale_detail_modifier_discount_3],
          order_type_id: order_type.id)
  end

  let(:sale_detail_transaction_with_surcharge) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 2,
                                    order_type_id: order_type.id)
    sale_detail_transaction.sale_detail_modifiers << [sale_detail_modifier, sale_detail_modifier_surcharge]
    sale_detail_transaction.total_amount = sale_detail_transaction.total_line_amount +
                                           sale_detail_transaction.sale_detail_modifiers.sum(&:total_line_amount)
    sale_detail_transaction
  end

  let(:void_sale_transaction_pos_queue_param) do
    {
      pos_queue: {
        uuid: '74ef8acc-1273-463c-96f0-96d7d718a4c3',
        payload: {
          user_id: owner.id,
          sales_no: sale_transaction.sales_no,
          void_reason: 'test-void-reason'
        },
        payload_type: 'void_sale_transaction',
        device_id: device.id,
        location_id: owned_branch_1.id
      }
    }
  end

  let(:void_sale_transaction_with_redeem_pos_queue_param) do
    {
      pos_queue: {
        uuid: '74ef8acc-1273-463c-96f0-96d7d718a4c3',
        payload: {
          user_id: owner.id,
          sales_no: sale_transaction_with_redeem.sales_no,
          void_reason: 'test-void-reason'
        },
        payload_type: 'void_sale_transaction',
        device_id: device.id,
        location_id: owned_branch_1.id
      }
    }
  end

  let(:sale_detail_modifier_default_param) do
    {
      product_id: sugar_modifier_owned_branch_1.id,
      product_category_id: sugar_modifier_owned_branch_1.product_category&.id,
      product_category_name: sugar_modifier_owned_branch_1.product_category&.name,
      product_unit_id: sugar_modifier_owned_branch_1.product_unit.id,
      quantity: 2,
      price: 2_500_000,
      total_line_amount: 5_000_000,
      prorate_discount: 500_000,
      tax_setting: sugar_modifier_owned_branch_1.sell_tax_setting,
      tax_id: sugar_modifier_owned_branch_1.tax_id,
      tax_name: sugar_modifier_owned_branch_1.tax&.name,
      tax_rate: sugar_modifier_owned_branch_1.tax&.rate || 0
    }
  end

  let(:sale_detail_modifier_2_default_param) do
    {
      product_id: milk_modifier_owned_branch_1.id,
      product_category_id: milk_modifier_owned_branch_1.product_category&.id,
      product_category_name: milk_modifier_owned_branch_1.product_category&.name,
      product_unit_id: milk_modifier_owned_branch_1.product_unit.id,
      quantity: 2,
      price: 2_500_000,
      total_line_amount: 5_000_000,
      prorate_discount: 500_000,
      tax_setting: milk_modifier_owned_branch_1.sell_tax_setting,
      tax_id: milk_modifier_owned_branch_1.tax_id,
      tax_name: milk_modifier_owned_branch_1.tax&.name,
      tax_rate: milk_modifier_owned_branch_1.tax&.rate || 0
    }
  end

  let(:sale_detail_modifier_tax_inclusive_param) do
    sugar_modifier_owned_branch_1.update!(sell_tax_setting: 'price_include_tax', tax: tax)

    build(:sale_detail_modifier_param,
          **sale_detail_modifier_default_param)
  end

  let(:sale_detail_modifier_tax_inclusive_2_param) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_include_tax', tax: tax)

    build(:sale_detail_modifier_param,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_modifier_tax_exclusive) do
    sugar_modifier_owned_branch_1.update!(sell_tax_setting: 'price_exclude_tax', tax: tax)

    build(:sale_detail_modifier,
          **sale_detail_modifier_default_param)
  end

  let(:sale_detail_modifier_tax_exclusive_2) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_exclude_tax', tax: tax)

    build(:sale_detail_modifier,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_modifier_no_tax) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_exclude_tax', tax: nil)

    build(:sale_detail_modifier,
          tax_rate: 0.0,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_modifier_tax_inclusive) do
    sugar_modifier_owned_branch_1.update!(sell_tax_setting: 'price_include_tax', tax: tax)

    build(:sale_detail_modifier,
          **sale_detail_modifier_default_param)
  end

  let(:sale_detail_modifier_tax_inclusive_2) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_include_tax', tax: tax)

    build(:sale_detail_modifier,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_modifier_tax_inclusive_3) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_include_tax', tax: tax_pb2)

    build(:sale_detail_modifier,
          tax_rate: 20,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_modifier_tax_exclusive_param) do
    sugar_modifier_owned_branch_1.update!(sell_tax_setting: 'price_exclude_tax', tax: tax)

    build(:sale_detail_modifier_param,
          **sale_detail_modifier_default_param)
  end

  let(:sale_detail_modifier_tax_exclusive_2_param) do
    milk_modifier_owned_branch_1.update!(sell_tax_setting: 'price_exclude_tax', tax: tax)

    build(:sale_detail_modifier_param,
          **sale_detail_modifier_2_default_param)
  end

  let(:sale_detail_default_param) do
    {
      product_id: latte.id,
      product_category_id: latte.product_category&.id,
      product_category_name: latte.product_category&.name,
      product_unit_id: latte.product_unit.id,
      quantity: 2,
      price: 2_500_000,
      total_line_amount: 5_000_000,
      prorate_discount: 500_000,
      tax_setting: latte.sell_tax_setting,
      tax_id: latte.tax_id,
      tax_rate: tax.rate
    }
  end

  let(:sale_detail_2_default_param) do
    {
      product_id: spicy_burger.id,
      product_category_id: spicy_burger.product_category&.id,
      product_category_name: spicy_burger.product_category&.name,
      product_unit_id: spicy_burger.product_unit.id,
      tax_setting: spicy_burger.sell_tax_setting,
      tax_id: spicy_burger.tax_id,
      tax_rate: tax.rate,
      quantity: 2,
      price: 2_500_000,
      total_line_amount: 5_000_000,
      prorate_discount: 500_000
    }
  end

  let(:sale_detail_transaction_tax_exclusive) do
    latte.update!(tax: tax, sell_tax_setting: 'price_exclude_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_default_param,
      sale_detail_modifiers: [sale_detail_modifier_tax_exclusive]
    )
  end

  let(:sale_detail_transaction_tax_exclusive_2) do
    spicy_burger.update!(tax: tax, sell_tax_setting: 'price_exclude_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_2_default_param,
      sale_detail_modifiers: [sale_detail_modifier_tax_exclusive_2]
    )
  end

  let(:sale_detail_transaction_tax_exclusive_3) do
    spicy_burger.update!(tax: tax_pb2, sell_tax_setting: 'price_exclude_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_2_default_param,
      sale_detail_modifiers: [sale_detail_modifier_tax_exclusive_2]
    )
  end

  let(:sale_detail_transaction_tax_exclusive_4) do
    spicy_burger.update!(tax: tax, sell_tax_setting: 'price_exclude_tax')

    build(
      :sale_detail_transaction,
      product_id: spicy_burger.id,
      product_category_id: spicy_burger.product_category&.id,
      product_category_name: spicy_burger.product_category&.name,
      product_unit_id: spicy_burger.product_unit.id,
      tax_setting: spicy_burger.sell_tax_setting,
      tax_id: spicy_burger.tax_id,
      tax_rate: tax.rate,
      quantity: 2,
      price: 3_000_000,
      total_line_amount: 6_000_000,
      sale_detail_modifiers: [
        sale_detail_modifier_tax_exclusive_2,
        sale_detail_modifier_discount_2
      ]
    )
  end

  let(:sale_detail_transaction_no_tax) do
    spicy_burger.update!(tax: nil)

    build(
      :sale_detail_transaction,
      product_id: spicy_burger.id,
      product_category_id: spicy_burger.product_category&.id,
      product_category_name: spicy_burger.product_category&.name,
      product_unit_id: spicy_burger.product_unit.id,
      tax_setting: spicy_burger.sell_tax_setting,
      tax_id: nil,
      tax_rate: 0.0,
      quantity: 2,
      price: 3_000_000,
      total_line_amount: 6_000_000,
      sale_detail_modifiers: [
        sale_detail_modifier_no_tax,
        sale_detail_modifier_discount_2
      ]
    )
  end

  let(:sale_detail_transaction_tax_inclusive) do
    latte.update!(tax: tax, sell_tax_setting: 'price_include_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_default_param,
      tax_setting: 'price_include_tax',
      sale_detail_modifiers: [sale_detail_modifier_tax_inclusive]
    )
  end

  let(:sale_detail_transaction_tax_inclusive_2) do
    spicy_burger.update!(tax: tax, sell_tax_setting: 'price_include_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_2_default_param,
      tax_setting: 'price_include_tax',
      sale_detail_modifiers: [sale_detail_modifier_tax_inclusive_2]
    )
  end

  let(:sale_detail_transaction_tax_inclusive_3) do
    spicy_burger.update!(tax: tax_pb2, sell_tax_setting: 'price_include_tax')

    build(
      :sale_detail_transaction,
      **sale_detail_2_default_param,
      tax_setting: 'price_include_tax',
      tax_rate: 20,
      sale_detail_modifiers: [
        sale_detail_modifier_tax_inclusive_3,
        sale_detail_modifier_discount_2
      ]
    )
  end

  let(:sale_detail_transaction_tax_exclusive_param) do
    latte.update!(tax: tax, sell_tax_setting: 'price_exclude_tax')

    sale_detail_param = build(:sale_detail_transaction_param,
          **sale_detail_default_param,
          sale_detail_modifiers_attributes: [sale_detail_modifier_tax_exclusive_param])

    sale_detail_param[:total_amount] = sale_detail_param[:total_line_amount] + sale_detail_modifier_tax_exclusive_param[:total_line_amount]
    sale_detail_param
  end

  let(:sale_detail_transaction_tax_exclusive_2_param) do
    spicy_burger.update!(tax: tax, sell_tax_setting: 'price_exclude_tax')

    sale_detail_param = build(:sale_detail_transaction_param,
          **sale_detail_2_default_param,
          sale_detail_modifiers_attributes: [sale_detail_modifier_tax_exclusive_2_param])

    sale_detail_param[:total_amount] = sale_detail_param[:total_line_amount] + sale_detail_modifier_tax_exclusive_2_param[:total_line_amount]
    sale_detail_param
  end

  let(:sale_detail_transaction_tax_inclusive_param) do
    latte.update!(tax: tax, sell_tax_setting: 'price_include_tax')

    sale_detail_param = build(:sale_detail_transaction_param,
          **sale_detail_default_param,
          sale_detail_modifiers_attributes: [sale_detail_modifier_tax_inclusive_param])

    sale_detail_param[:total_amount] = sale_detail_param[:total_line_amount] + sale_detail_modifier_tax_exclusive_2_param[:total_line_amount]
    sale_detail_param
  end

  let(:sale_detail_transaction_tax_inclusive_2_param) do
    spicy_burger.update!(tax: tax, sell_tax_setting: 'price_include_tax')

    sale_detail_param = build(:sale_detail_transaction_param,
          **sale_detail_2_default_param,
          sale_detail_modifiers_attributes: [sale_detail_modifier_tax_inclusive_2_param])

    sale_detail_param[:total_amount] = sale_detail_param[:total_line_amount] + sale_detail_modifier_tax_inclusive_2_param[:total_line_amount]
    sale_detail_param
  end

  let(:pos_sale_queue_tax_exclusive_detail_param) do
    sale_transaction_param = build(:sale_transaction_param, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                  cashier_employee_id: owner.id, order_type_id: order_type.id,
                                  customer_id: my_customer.id, brand_id: brand.id,
                                  discount_fee: 2_000_000, subtotal: 20_000_000,
                                  payments_attributes: [payment_param],
                                  sale_detail_transactions_attributes: [sale_detail_transaction_tax_exclusive_param, sale_detail_transaction_tax_exclusive_2_param])
    device = create(:device, device_id: SecureRandom.uuid, location_id: owned_branch_1.id, brand_id: brand.id, user_id: owner.id)

    build(:pos_sale_queue_param,
      payload_type: 'sale_transaction',
      uuid: SecureRandom.uuid,
      device_id: device.id,
      payload: sale_transaction_param)
  end

  let(:pos_sale_queue_tax_inclusive_detail_param) do
    sale_transaction_param = build(:sale_transaction_param, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                  cashier_employee_id: owner.id, order_type_id: order_type.id,
                                  customer_id: my_customer.id, brand_id: brand.id,
                                  discount_fee: 2_000_000, subtotal: 20_000_000,
                                  payments_attributes: [payment_param],
                                  sale_detail_transactions_attributes: [sale_detail_transaction_tax_inclusive_param, sale_detail_transaction_tax_inclusive_2_param])
    device = create(:device, device_id: SecureRandom.uuid, location_id: owned_branch_1.id, brand_id: brand.id, user_id: owner.id)

    build(:pos_sale_queue_param,
      payload_type: 'sale_transaction',
      uuid: SecureRandom.uuid,
      device_id: device.id,
      payload: sale_transaction_param)
  end

  let(:pos_sale_queue_tax_partial_inclusive_detail_param) do
    sale_transaction_param = build(:sale_transaction_param, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                  cashier_employee_id: owner.id, order_type_id: order_type.id,
                                  customer_id: my_customer.id, brand_id: brand.id,
                                  discount_fee: 2_000_000, subtotal: 20_000_000,
                                  payments_attributes: [payment_param],
                                  sale_detail_transactions_attributes: [sale_detail_transaction_tax_exclusive_param, sale_detail_transaction_tax_inclusive_2_param])
    device = create(:device, device_id: SecureRandom.uuid, location_id: owned_branch_1.id, brand_id: brand.id, user_id: owner.id)

    build(:pos_sale_queue_param,
      payload_type: 'sale_transaction',
      uuid: SecureRandom.uuid,
      device_id: device.id,
      payload: sale_transaction_param)
  end

  let(:sale_transaction_with_discount) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id, discount_fee: 1_500_000,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id, surcharge_fee: 2_000_000,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment_with_processing_fee
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_discount
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction.update!(total_discount_before_tax: 0)
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    sale_transaction
  end

  let(:sale_transaction_multiple_lines_with_discount) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment_with_processing_fee
    sale_transaction.sale_detail_transactions = [sale_detail_transaction_with_discount_2, sale_detail_transaction_with_discount_3]
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction.update!(total_discount_before_tax: 0)
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    sale_transaction
  end

  let(:sale_transaction_with_tax_exclusive_details) do
    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                            cashier_employee_id: owner.id, order_type_id: order_type.id,
                            customer_id: my_customer.id, brand_id: brand.id,
                            location_id: owned_branch_1.id,
                            discount_fee: 2_000_000,
                            payments: [payment],
                            sale_detail_transactions: [sale_detail_transaction_tax_exclusive,
                                                       sale_detail_transaction_tax_exclusive_2])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_with_partial_tax_exclusive_details) do
    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                             cashier_employee_id: owner.id, order_type_id: order_type.id,
                             customer_id: my_customer.id, brand_id: brand.id,
                             location_id: owned_branch_1.id,
                             discount_fee: 2_000_000,
                             payments: [payment],
                             sale_detail_transactions: [sale_detail_transaction_tax_inclusive,
                                                        sale_detail_transaction_tax_exclusive_2])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_with_partial_tax_exclusive_details_3) do
    sale_detail_transaction_tax_inclusive.sale_detail_modifiers << sale_detail_modifier_discount_3

    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                             cashier_employee_id: owner.id, order_type_id: order_type.id,
                             customer_id: my_customer.id, brand_id: brand.id,
                             location_id: owned_branch_1.id,
                             discount_fee: 2_000_000,
                             payments: [payment],
                             sale_detail_transactions: [sale_detail_transaction_tax_inclusive,
                                                        sale_detail_transaction_tax_exclusive_4])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_2_lines_tax_incl_and_no_tax) do
    sale_detail_transaction_tax_inclusive.sale_detail_modifiers << sale_detail_modifier_discount_3

    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                             cashier_employee_id: owner.id, order_type_id: order_type.id,
                             customer_id: my_customer.id, brand_id: brand.id,
                             location_id: owned_branch_1.id,
                             discount_fee: 2_000_000,
                             payments: [payment],
                             sale_detail_transactions: [sale_detail_transaction_tax_inclusive,
                                                        sale_detail_transaction_no_tax])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_with_tax_inclusive_details) do
    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                             cashier_employee_id: owner.id, order_type_id: order_type.id,
                             customer_id: my_customer.id, brand_id: brand.id,
                             location_id: owned_branch_1.id,
                             discount_fee: 2_000_000,
                             payments: [payment],
                             sale_detail_transactions: [sale_detail_transaction_tax_inclusive,
                                                        sale_detail_transaction_tax_inclusive_2])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_2_lines_diff_tax_inclusive) do
    sale_detail_transaction_tax_inclusive.sale_detail_modifiers << sale_detail_modifier_discount_3

    sale_transaction = build(:sale_transaction, sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                             cashier_employee_id: owner.id, order_type_id: order_type.id,
                             customer_id: my_customer.id, brand_id: brand.id,
                             location_id: owned_branch_1.id,
                             discount_fee: 2_000_000,
                             payments: [payment],
                             sale_detail_transactions: [sale_detail_transaction_tax_inclusive,
                                                        sale_detail_transaction_tax_inclusive_3])
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_transaction_with_partial_tax_exclusive_details_2) do
    create(
      :sale_transaction,
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
      brand_id: brand.id,
      discount_fee: 2_000_000,
      subtotal: 20_000_000,
      location_id: owned_branch_1.id,
      tax_fee: 1_909_090.91,
      payments: [
        payment
      ],
      gross_sales: 19_090_909.09,
      net_sales: 17_181_818.81,
      net_sales_after_tax: 19_090_909.09,
      sale_detail_transactions: [
        sale_detail_transaction_tax_inclusive,
        sale_detail_transaction_tax_exclusive_3
      ]
    )
  end


  let(:sale_transaction_with_customer_1) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_customer_2) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_customer_old_1) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
      sales_time: Time.zone.now - 1.week
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_customer_old_2) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
      sales_time: customer_category_long_duration.start_date.beginning_of_day
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:sale_transaction_with_customer_old_3) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: franchise_branch_1.id,
      payments: [payment],
      sale_detail_transactions: [sale_detail_transaction],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
      sales_time: Time.zone.now - 1.week
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale
  end

  let(:voided_sale_transaction_with_customer) do
    sale = build(
      :sale_transaction,
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      payments: [payment_with_processing_fee],
      sale_detail_transactions: [sale_detail_transaction_3],
      sales_no: SecureRandom.uuid,
      order_employee_id: owner.id,
      cashier_employee_id: owner.id,
      order_type_id: order_type.id,
      customer_id: my_customer.id,
    )
    sale = calculate_amounts_for_sale_transaction(sale)
    sale.save!
    sale.void
    sale
  end

  let(:product_modifier) { create(:product, :modifier, brand: brand, product_unit: latte.product_unit) }
  let(:sale_detail_modifier_3) { build(:sale_detail_modifier, product_id: product_modifier.id, product_unit_id: product_modifier.product_unit.id) }
  let(:sale_detail_transaction_modifier) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: latte.id, quantity: 10, product_unit_id: latte.product_unit.id)
    sale_detail_transaction.sale_detail_modifiers << sale_detail_modifier_3
    sale_detail_transaction
  end

  let(:sale_transaction_modifier) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: central_kitchen.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id, net_sales: 5000)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_modifier
    sale_transaction
  end

  let(:emping_sale_detail_modifier) do
    build(:sale_detail_modifier, product_id: emping_modifier.id, quantity: 4, product_unit_id: emping_modifier.product_unit.id)
  end

  let(:latte_sale_detail_transaction) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: latte.id, product_unit_id: latte.product_unit.id, quantity: 2,
                                                              total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
    sale_detail_transaction.sale_detail_modifiers << emping_sale_detail_modifier
    sale_detail_transaction
  end

  let(:sale_transaction_9) do
    sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id, surcharge_fee: 500,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << latte_sale_detail_transaction
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_detail_transaction_6) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: 2,
                                                              total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
    sale_detail_transaction
  end

  let(:sale_transaction_10) do
    sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id, surcharge_fee: 1000,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment_6
    sale_transaction.sale_detail_transactions << sale_detail_transaction_6
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_detail_transaction_7) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: 2,
                                                              total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
    sale_detail_transaction
  end

  let(:sale_transaction_11) do
    sale_transaction = build(:sale_transaction, sales_time: Time.now - 45.days, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id, surcharge_fee: 1500,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment_7
    sale_transaction.sale_detail_transactions << sale_detail_transaction_7
    sale_transaction.save!
    sale_transaction
  end

  let(:sale_detail_transaction_8) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: 2,
                                                              total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
    sale_detail_transaction
  end

  let(:sale_detail_transaction_9) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit.id, quantity: 2,
                                                              total_amount_prorate_discount: 10_000, created_at: Time.zone.now,
                                                              sale_detail_modifiers: [sale_detail_modifier_4, sale_detail_modifier_5])
    sale_detail_transaction
  end

  let(:sale_transaction_12) do
    sale_transaction = build(:sale_transaction, sales_time: Time.now - 45.days, brand_id: brand.id, location_id: franchise_branch_2.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id, surcharge_fee: 2000,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment_8
    sale_transaction.sale_detail_transactions << sale_detail_transaction_8
    sale_transaction.save!
    sale_transaction
  end
end
