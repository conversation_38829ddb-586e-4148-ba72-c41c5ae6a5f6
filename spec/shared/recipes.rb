require './spec/shared/products'
require './spec/shared/recipe_product_option_sets'

RSpec.shared_context "recipes creations" do
  include_context 'products creations'
  include_context 'recipe product option set creations'

  let(:recipe) { build(:recipe, brand: brand, product: cheese_burger, product_unit: cheese_burger.product_unit, access_list_ids: [AccessList.brand_owner_id]) }
  let(:recipe_2) { build(:recipe, brand: brand, product: spicy_burger, product_unit: spicy_burger.product_unit, access_list_ids: [AccessList.brand_owner_id]) }
  let(:recipe_3) { build(:recipe, brand: brand, product: latte, product_unit: latte.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id], production_outlet_ids: [owned_branch_1.id]) }
  let(:recipe_4) { build(:recipe, brand: brand, product: espresso, product_unit: espresso.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id]) }
  let(:recipe_5) { build(:recipe, brand: brand, product: espresso_variant_choco, product_unit: espresso_variant_choco.product_unit, access_list_ids: []) }

  let(:chili_sauce_recipe) { build(:recipe, brand: brand, product: chili_sauce, product_unit: chili_sauce.product_unit, access_list_ids: [AccessList.brand_owner_id]) }
  let(:patty_recipe) { build(:recipe, brand: brand, product: patty, product_unit: patty.product_unit, access_list_ids: [AccessList.brand_owner_id]) }
  let(:patty_sauce_recipe) { build(:recipe, brand: brand, product: patty_sauce, product_unit: patty_sauce.product_unit, access_list_ids: [AccessList.brand_owner_id]) }
  let(:omelet_recipe) { build(:recipe, brand: brand, product: omelet, recipe_type: 'batches', expected_yield: 10, product_unit: omelet.product_unit, access_list_ids: [AccessList.brand_owner_id]) }

  let(:recipe_espresso_swappable) do
    additional_milk = create(:option_set, name: 'Additional Milk', brand_id: brand.id)
    additional_milk.option_set_options.create!(product_id: latte.id, price: 1221, quantity: 3, product_unit_id: latte.product_unit_id)
    additional_milk.product_option_sets.create!(product_id: espresso.id)

    recipe_4.recipe_lines.destroy_all
    recipe_4.recipe_lines << build(:recipe_line, product: coffee_milk, product_unit: coffee_milk.product_unit)
    recipe_4.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit)
    recipe_4.save!
  end

  let(:recipe_latte_with_swap_params) do
    build(:recipe_params, product_id: latte.id, product_unit_id: latte.product_unit_id, recipe_type: 'made_to_order',
          recipe_line_swap_products_attributes: build(:recipe_line_swap_product_params, product_from_id: coffee_milk.id, product_to_id: ginger_milk.id, ratio: 80.0))
  end

  let(:recipe_latte_error_with_swap_params) do
    build(:recipe_params, product_id: latte.id, product_unit_id: latte.product_unit_id, recipe_type: 'made_to_order',
          recipe_line_swap_products_attributes: build(:recipe_line_swap_product_params, product_from_id: coffee_milk.id, product_to_id: ginger_milk.id, ratio: 80.0),
          recipe_lines_attributes: build(:recipe_line_params, quantity: -1, product_id: coffee_milk.id, product_unit_id: coffee_milk.product_unit.id))
  end

  let(:recipe_latte_with_swap) do
    create(:recipe, recipe_latte_with_swap_params.merge({ brand_id: brand.id}))
  end

  let(:recipe_latte_with_swap_update_params) do
    recipe = recipe_latte_with_swap
    recipe_line_swappable = recipe.recipe_line_swap_products.first

    {
      recipe: {
        id: recipe.id,
        instruction: 'test',
        recipe_line_swap_products_attributes: [
          {
            id: recipe_line_swappable.id,
            _destroy: true
          },
          {
            id: nil,
            product_from_id: recipe_line_swappable.product_from_id,
            product_to_id: recipe_line_swappable.product_to_id,
            ratio: 75.0
          }
        ]
      }
    }
  end

  let(:substitute_line_1) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.first,
      product: green_sugar,
      quantity: 1,
      product_unit: green_sugar.back_office_unit
    )
  end

  let(:substitute_line_soy_milk) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.first,
      product: soy_milk,
      quantity: 1,
      product_unit: soy_milk.back_office_unit,
      sequence: 2
    )
  end

  let(:recipe_with_lines) do
    recipe_3.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    recipe_3.recipe_lines << build(:recipe_line, product: milk, product_unit: gram)
    recipe_3.recipe_lines << build(:recipe_line, product: coffee, product_unit: gram)
    recipe_3
  end

  let(:recipe_latte_with_lines_and_conversion) do
    coffee_conversion_kg
    milk_conversion_mg

    coffee.reload
    milk.reload
    # latte 1 unit
    # eql to 100 mg milk, smallest unit is gram (0.1 gr)
    # eql to 0.01 kg coffee, smallest unit is gram (10 gr)

    recipe_3.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    recipe_3.recipe_lines << build(:recipe_line, product: milk, product_unit: miligram, quantity: 100)
    recipe_3.recipe_lines << build(:recipe_line, product: coffee, product_unit: kilogram, quantity: 0.01)
    recipe_3
  end

  let(:recipe_with_lines_espresso) do
    recipe_4.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    recipe_4.recipe_lines << build(:recipe_line, product: milk, product_unit: gram)
    recipe_4.recipe_lines << build(:recipe_line, product: coffee, product_unit: gram)
    recipe_4
  end

  let(:spicy_burger_recipe_with_lines) do
    recipe_2.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    recipe_2.recipe_lines << build(:recipe_line, product: bun, product_unit: piece)
    recipe_2.recipe_lines << build(:recipe_line, product: patty, product_unit: piece)
    recipe_2.recipe_lines << build(:recipe_line, product: chili_sauce, product_unit: gram)
    recipe_2
  end

  let(:recipe_with_bun) do
    recipe.recipe_lines << build(:recipe_line, product: bun, product_unit: piece)
    recipe
  end

  let(:chili_sauce_recipe_with_lines) do
    chili_sauce_recipe.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    chili_sauce_recipe.recipe_lines << build(:recipe_line, product: chili, product_unit: gram)
    chili_sauce_recipe.recipe_lines << build(:recipe_line, product: garlic, product_unit: gram)
    chili_sauce_recipe
  end

  let(:patty_recipe_with_lines) do
    patty_recipe.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    patty_recipe.recipe_lines << build(:recipe_line, product: meat, product_unit: gram)
    patty_recipe.recipe_lines << build(:recipe_line, product: garlic, product_unit: gram)
    patty_recipe.recipe_lines << build(:recipe_line, product: patty_sauce, product_unit: gram)
    patty_recipe
  end

  let(:patty_sauce_recipe_with_lines) do
    patty_sauce_recipe.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    patty_sauce_recipe.recipe_lines << build(:recipe_line, product: salt, product_unit: gram)
    patty_sauce_recipe.recipe_lines << build(:recipe_line, product: msg, product_unit: gram)
    patty_sauce_recipe
  end

  let(:omelet_recipe_with_lines) do
    omelet_recipe.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    omelet_recipe.recipe_lines << build(:recipe_line, product: raw_egg, product_unit: raw_egg.product_unit, quantity: 10)
    omelet_recipe.recipe_lines << build(:recipe_line, product: cooking_oil, product_unit: cooking_oil.product_unit, quantity: 500)
    omelet_recipe
  end

  let(:latte_recipe_batches_with_substitute_ingredients) do
    latte_substitute_line_1
    latte_substitute_line_2
    latte_recipe_batches.reload
  end

  let(:latte_recipe_batches_with_multiple_substitute_ingredients) do
    latte_substitute_line_1
    latte_substitute_line_2
    latte_substitute_line_3
    latte_substitute_line_4
    latte_recipe_batches.reload
  end

  let(:espresso_recipe_made_to_order_with_substitute_ingredients) do
    espresso_substitute_line_1
    espresso_substitute_line_2
    espresso_recipe_made_to_order
  end

  let(:espresso_substitute_line_1) do
    create(
      :recipe_line_substitute,
      recipe_line: espresso_recipe_made_to_order.recipe_lines.first,
      product: green_sugar,
      quantity: 1,
      product_unit: green_sugar.back_office_unit
    )
  end

  let(:espresso_substitute_line_2) do
    create(
      :recipe_line_substitute,
      recipe_line: espresso_recipe_made_to_order.recipe_lines.second,
      product: black_pepper,
      quantity: 1,
      product_unit: black_pepper.back_office_unit
    )
  end

  let(:latte_substitute_line_1) { substitute_line_1 }
  let(:latte_substitute_line_2) { substitute_line_2 }

  let(:substitute_line_1) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.first,
      product: green_sugar,
      quantity: 1,
      product_unit: green_sugar.back_office_unit
    )
  end

  let(:substitute_line_2) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.second,
      product: black_pepper,
      quantity: 1,
      product_unit: black_pepper.back_office_unit
    )
  end

  let(:latte_substitute_line_3) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.first,
      product: raw_egg,
      quantity: 1,
      product_unit: raw_egg.back_office_unit
    )
  end

  let(:latte_substitute_line_4) do
    create(
      :recipe_line_substitute,
      recipe_line: latte_recipe_batches.recipe_lines.second,
      product: cooking_oil,
      quantity: 1,
      product_unit: cooking_oil.back_office_unit
    )
  end

  let(:cheese_burger_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: cheese_burger, product_unit: cheese_burger.product_unit, access_list_ids: [AccessList.brand_owner_id], production_outlet_ids: [owned_branch_1.id])
    recipe.recipe_lines << build(:recipe_line, product: plastic_bag, product_unit: plastic_bag.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: black_pepper, product_unit: black_pepper.product_unit)
    recipe.save
    recipe
  end

  let(:spicy_burger_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: spicy_burger, product_unit: spicy_burger.product_unit, access_list_ids: [AccessList.brand_owner_id], production_outlet_ids: [owned_branch_1.id])
    recipe.recipe_lines << build(:recipe_line, product: plastic_bag, product_unit: plastic_bag.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: black_pepper, product_unit: black_pepper.product_unit)
    recipe.save
    recipe
  end

  let(:spicy_burger_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: spicy_burger, product_unit: spicy_burger.product_unit, access_list_ids: [AccessList.brand_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: plastic_bag, product_unit: plastic_bag.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: black_pepper, product_unit: black_pepper.product_unit)
    recipe.save
    recipe
  end

  let(:latte_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: latte, product_unit: latte.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id], production_outlet_ids: [owned_branch_1.id])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit)
    recipe.save
    recipe
  end

  let(:latte_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: latte, product_unit: latte.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit, quantity: 8)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit, quantity: 2)
    recipe.save!
    recipe
  end

  let(:coffee_milk_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: coffee_milk, product_unit: coffee_milk.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: coffee_powder, product_unit: coffee_powder.product_unit, quantity: 20)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 10)
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit, quantity: -4)
    recipe.save!
    recipe
  end

  let(:ginger_milk_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: ginger_milk, product_unit: ginger_milk.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: ginger, product_unit: ginger.product_unit, quantity: 7)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 5)
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk_conversion_1_l.product_unit, quantity: -2)
    recipe.save!
    recipe
  end

  let(:sugar_modifier_owned_branch_1_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: sugar_modifier_owned_branch_1, product_unit: sugar_modifier_owned_branch_1.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit, quantity: 8)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit, quantity: 2)
    recipe.save!
    recipe
  end

  let(:espresso_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: espresso, product_unit: espresso.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: few_caffeine, product_unit: few_caffeine.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:espresso_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: espresso, product_unit: espresso.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: few_caffeine, product_unit: few_caffeine.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:espresso_variant_choco_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: espresso_variant_choco, product_unit: espresso_variant_choco.product_unit, access_list_ids: [])
    recipe.recipe_lines << build(:recipe_line, product: few_caffeine, product_unit: few_caffeine.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:latte_no_category_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: latte_no_category, product_unit: latte_no_category.sell_unit, access_list_ids: [])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit)
    recipe.save
    recipe
  end

  let(:recipe_batches_with_nested_recipe_batches) do
    latte_recipe_batches

    recipe = build(:recipe, :batches, brand: brand, product: espresso, product_unit: espresso.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, quantity: 2, product: latte_recipe_batches.product, product_unit: latte_recipe_batches.product.product_unit)
    recipe.recipe_lines << build(:recipe_line, quantity: 3, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:recipe_made_to_order_with_double_nested_recipe_batches) do
    recipe_batches_with_nested_recipe_batches

    recipe = build(:recipe, :made_to_order, brand: brand, product: americano, product_unit: americano.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, quantity: 3, product: recipe_batches_with_nested_recipe_batches.product, product_unit: recipe_batches_with_nested_recipe_batches.product.product_unit)
    recipe.save
    recipe
  end

  # brand 2
  let(:brand_2_latte_made_to_order_recipe) { build(:recipe, :made_to_order, brand: brand_2, product: brand_2_latte, product_unit: brand_2_latte.product_unit, access_list_ids: []) }

  let(:brand_2_latte_made_to_order_recipe_with_lines) do
    brand_2_latte_made_to_order_recipe.recipe_lines.destroy_all # it doesn't have to be this way, we do this because there's an old code building associations on factories
    brand_2_latte_made_to_order_recipe.recipe_lines << build(:recipe_line, product: brand_2_milk, product_unit: brand_2_gram)
    brand_2_latte_made_to_order_recipe.recipe_lines << build(:recipe_line, product: brand_2_coffee, product_unit: brand_2_gram)
    brand_2_latte_made_to_order_recipe
  end

  let(:recipe_access_list_ids_1) do
    recipe = build(:recipe, brand: brand, product: cheese_burger_owned_branch_1, product_unit: cheese_burger_owned_branch_1.product_unit,
                   access_list_ids: [AccessList.location_owner_id], production_access_list_ids: [AccessList.brand_owner_id],
                   production_outlet_ids: [])
    recipe.recipe_lines << build(:recipe_line, product: plastic_bag, product_unit: plastic_bag.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: black_pepper, product_unit: black_pepper.product_unit)
    recipe.save
    recipe
  end

  let(:recipe_access_list_ids_2) do
    recipe = build(:recipe, brand: brand, product: latte_owned_branch_1, product_unit: latte_owned_branch_1.product_unit,
                   access_list_ids: [AccessList.brand_owner_id], production_access_list_ids: [AccessList.location_owner_id],
                   production_outlet_ids: [owned_branch_1.id])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit)
    recipe.save
    recipe
  end

  let(:recipe_access_list_ids_3) do
    recipe = build(:recipe, brand: brand, product: espresso_owned_branch_1, product_unit: espresso_owned_branch_1.product_unit,
                   access_list_ids: [AccessList.location_owner_id], production_access_list_ids: [AccessList.brand_owner_id],
                   production_outlet_ids: [])
    recipe.recipe_lines << build(:recipe_line, product: few_caffeine, product_unit: few_caffeine.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:recipe_access_list_ids_4) do
    recipe = build(:recipe, brand: brand, product: black_coffe_owned_branch_1, product_unit: black_coffe_owned_branch_1.product_unit,
                   access_list_ids: [AccessList.brand_owner_id], production_access_list_ids: [AccessList.location_owner_id],
                   production_outlet_ids: [owned_branch_1.id])
    recipe.recipe_lines << build(:recipe_line, product: few_caffeine, product_unit: few_caffeine.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: few_sugar, product_unit: few_sugar.product_unit)
    recipe.save
    recipe
  end

  let(:coffee_recipe_made_to_order) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: coffee, product_unit: coffee.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: coffee_powder, product_unit: coffee_powder.product_unit, quantity: 20)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 10)
    recipe.save!
    recipe
  end

  let(:coffee_recipe_batches) do
    recipe = build(:recipe, :batches, brand: brand, product: coffee, product_unit: coffee.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: coffee_powder, product_unit: coffee_powder.product_unit, quantity: 20)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 10)
    recipe.save!
    recipe
  end

  let(:americano_small_ice_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, recipe_product_option_set_id: americano_recipe_product_option.id, product_unit: americano.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: coffee_powder, product_unit: coffee_powder.product_unit, quantity: 20)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 10)
    recipe.save!
    recipe
  end

  let(:latte_small_ice_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, recipe_product_option_set_id: latte_product_option.id, product_unit: americano.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: coffee_powder, product_unit: coffee_powder.product_unit, quantity: 20)
    recipe.recipe_lines << build(:recipe_line, product: water, product_unit: water.product_unit, quantity: 10)
    recipe.save!
    recipe
  end

  let(:cake_swap_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: cake, product_unit: piece, access_list_ids: [AccessList.brand_owner_id])
    recipe.recipe_line_swap_products << build(:recipe_line_swap_product, product_from: coffee_powder, product_to: water)
    recipe.save!
    recipe
  end

  let(:cake_swap_empty_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: cake, product_unit: piece, access_list_ids: [AccessList.brand_owner_id])
    recipe.recipe_line_swap_products << build(:recipe_line_swap_product, product_from: coffee_powder, product_to: nil)
    recipe.save!
    recipe
  end

  let(:cake_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, product: cake, product_unit: piece, access_list_ids: [AccessList.brand_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: plastic_bag, product_unit: plastic_bag.product_unit)
    recipe.recipe_lines << build(:recipe_line, product: black_pepper, product_unit: black_pepper.product_unit)
    recipe.save!
    recipe
  end

  let(:latte_option_set_recipe) do
    recipe = build(:recipe, :made_to_order, brand: brand, recipe_product_option_set: latte_product_option, product_unit: latte.sell_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
    recipe.recipe_lines << build(:recipe_line, product: milk, product_unit: milk.product_unit, quantity: 8)
    recipe.recipe_lines << build(:recipe_line, product: coffee, product_unit: coffee.product_unit, quantity: 2)
    recipe.save!
    recipe
  end
end
