require './spec/shared/locations'

RSpec.shared_context 'BCA setup' do
  include_context 'locations creations'

  def stub_succeeded_access_token_request
    body = {
      "responseCode"=>"2007300",
      "responseMessage"=>"Successful",
      "accessToken"=>"OZO5CZMdSeRKRulsLD4ns3uYyreqZ68mDIysLe1Gy8S5QFZbXvlP00",
      "tokenType"=>"Bearer",
      "expiresIn"=>"900"
    }

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/access-token/b2b").
      and_return(status: 200, body: body.to_json, headers: {})
  end

  def stub_failed_access_token_request
    body = {
      "responseCode" => "401xx00",
      "responseMessage" => " Unauthorized. [Signature]",
      "data" => {}
    }

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/access-token/b2b").
      and_return(status: 401, body: body.to_json, headers: {})
  end

  def stub_succeeded_internal_account_inquiry_request
    body = {
      "responseCode"=>"2001500",
      "responseMessage"=>"Successful",
      "beneficiaryAccountNo"=>"**********",
      "beneficiaryAccountName"=>"Giro **********",
      "referenceNo"=>"20250131000000021981232",
      "partnerReferenceNo"=>"202010290000000001"
    }

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/account-inquiry-internal")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"beneficiaryAccountNo\":\"**********\"}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>'b4oJjxNPsdNAYm4bdgx4yG4JZK4J2a8Yc/de48exHctRodXBo0fQoUoi0YM2gzuOD8/bV67atiqm+3WdwxnREw=='
        })
      .to_return(status: 200, body: body.to_json, headers: {})
  end

  def stub_invalid_account_internal_account_inquiry
    body = {"responseCode"=>"4041511",
            "responseMessage"=>"Invalid Account",
            "beneficiaryAccountNo"=>"**********",
            "beneficiaryAccountName"=>"",
            "referenceNo"=>"",
            "partnerReferenceNo"=>"202010290000000001"}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/account-inquiry-internal")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"beneficiaryAccountNo\":\"**********\"}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>'lJ3SUp5V/T1rzOxoNLzB8ueOkpDpxB+jAppRD+dVAJf1GH4qSZ5c/awnZN2km0vTvRkNrJeWAcRPgFy2YjU78A=='
        })
      .to_return(status: 404, body: body.to_json, headers: {})
  end

  def stub_succeeded_transfer_intrabank(outlet)
    response_body = {"responseCode"=>"2001700",
                     "responseMessage"=>"Successful",
                     "referenceNo"=>"**************",
                     "partnerReferenceNo"=>"202010290000000001",
                     "amount"=>{"value"=>"10000.00", "currency"=>"IDR"},
                     "beneficiaryAccountNo"=>"**********",
                     "customerReference"=>"",
                     "currency"=>"",
                     "sourceAccountNo"=>"**********",
                     "transactionDate"=>"2025-03-06T17:36:50+07:00",
                     "additionalInfo"=>{"economicActivity"=>"", "transactionPurpose"=>"02"}}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/transfer-intrabank")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"sourceAccountNo\":\"**********\",\"beneficiaryEmail\":null,\"beneficiaryAccountNo\":\"**********\",\"amount\":{\"value\":\"10000.00\",\"currency\":\"IDR\"},\"transactionDate\":\"2025-01-31T14:53:45+07:00\",\"remark\":\"Disb #{outlet.name.gsub(/[^a-zA-Z0-9 ]/, '').squeeze(' ').strip}\",\"additionalInfo\":{\"economicActivity\":\"\",\"transactionPurpose\":\"02\"}}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'Channel-Id'=>'95051',
          'User-Agent'=>'Faraday v1.10.4',
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'X-External-Id'=>/.*/,
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>/.*/
        })
      .to_return(status: 200, body: response_body.to_json, headers: {})
  end

  def stub_succeeded_transfer_interbank
    response_body = {"responseCode"=>"2001800",
                     "responseMessage"=>"Successful",
                     "beneficiaryAccountNo"=>"***************",
                     "amount"=>{"value"=>"10000.00", "currency"=>"IDR"},
                     "referenceNo"=>"20250404000000046296199",
                     "partnerReferenceNo"=>"202010290000000001",
                     "beneficiaryBankCode"=>"ABALIDBS",
                     "sourceAccountNo"=>"**********",
                     "additionalInfo"=>{"bifastId"=>"20250404O000132645"}}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v2.0/transfer-interbank")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"sourceAccountNo\":\"**********\",\"beneficiaryEmail\":\"\",\"beneficiaryBankCode\":\"ABALIDBS\",\"beneficiaryAccountNo\":\"***************\",\"beneficiaryAccountName\":\"Yories Yolanda\",\"amount\":{\"value\":\"10000.00\",\"currency\":\"IDR\"},\"transactionDate\":\"2025-01-31T14:53:45+07:00\",\"additionalInfo\":{\"transferType\":\"2\",\"purposeCode\":\"02\"}}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'Channel-Id'=>'95051',
          'User-Agent'=>'Faraday v1.10.4',
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'X-External-Id'=>/.*/,
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>/.*/
        })
      .to_return(status: 200, body: response_body.to_json, headers: {})
  end

  def stub_read_timeout_transfer_interbank
    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v2.0/transfer-interbank")
      .to_raise(Net::ReadTimeout)
  end

  def stub_succeeded_check_transfer_status
    response_body = {"responseCode"=>"2003600",
                     "responseMessage"=>"Successful",
                     "beneficiaryAccountNo"=>"***************",
                     "amount"=>{"value"=>"10000.00", "currency"=>"IDR"},
                     "originalReferenceNo"=>"250616095602492610",
                     "originalExternalId"=>/.*/,
                     "serviceCode"=>"18",
                     "referenceNumber"=>"250616153270366214",
                     "latestTransactionStatus"=>"00",
                     "originalPartnerReferenceNo"=>"202010290000000001",
                     "transactionStatusDesc"=>"Transaction Success",
                     "beneficiaryBankCode"=>"ABALIDBS",
                     "transactionDate"=>"2025-06-16T00:00:00+07:00",
                     "sourceAccountNo"=>BCA::Constants::SOURCE_ACCOUNT_NO}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/transfer/status")
      .to_return(status: 200, body: response_body.to_json, headers: {})
  end

  def stub_not_found_transfer_status
    response_body = {"responseCode"=>"4043601",
                     "responseMessage"=>"Transaction Not Found",
                     "beneficiaryAccountNo"=>"***************",
                     "amount"=>{"value"=>"10000.00", "currency"=>"IDR"},
                     "originalReferenceNo"=>"",
                     "originalExternalId"=>/.*/,
                     "serviceCode"=>"18",
                     "referenceNumber"=>"250616153270366214",
                     "originalPartnerReferenceNo"=>"202010290000000001",
                     "transactionStatusDesc"=>"",
                     "beneficiaryBankCode"=>"",
                     "transactionDate"=>"2025-06-16T00:00:00+07:00",
                     "sourceAccountNo"=>BCA::Constants::SOURCE_ACCOUNT_NO}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/transfer/status")
      .to_return(status: 400, body: response_body.to_json, headers: {})
  end

  def stub_failed_transfer_intrabank(outlet)
    response_body = {
      "responseCode"=>"4031714",
      "responseMessage"=>"Insufficient funds",
      "referenceNo"=>"**************",
      "partnerReferenceNo"=>"202010290000000001",
      "amount"=>{"value"=>"1000000.00","currency"=>"IDR"},
      "beneficiaryAccountNo"=>"**********",
      "customerReference"=>"",
      "currency"=>"",
      "sourceAccountNo"=>"**********",
      "transactionDate"=>"2025-03-06T17:36:50+07:00",
      "additionalInfo"=>{"economicActivity"=>"","transactionPurpose"=>"02"}}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v1.0/transfer-intrabank")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"sourceAccountNo\":\"**********\",\"beneficiaryEmail\":null,\"beneficiaryAccountNo\":\"**********\",\"amount\":{\"value\":\"10000.00\",\"currency\":\"IDR\"},\"transactionDate\":\"2025-01-31T14:53:45+07:00\",\"remark\":\"Disb #{outlet.name.gsub(/[^a-zA-Z0-9 ]/, '').squeeze(' ').strip}\",\"additionalInfo\":{\"economicActivity\":\"\",\"transactionPurpose\":\"02\"}}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'Channel-Id'=>'95051',
          'User-Agent'=>'Faraday v1.10.4',
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'X-External-Id'=>/.*/,
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>/.*/
        })
      .to_return(status: 403, body: response_body.to_json, headers: {})
  end

  def stub_failed_transfer_interbank
    response_body = {"responseCode"=>"4041811",
                     "responseMessage"=>"Invalid Account",
                     "beneficiaryAccountNo"=>"*********",
                     "amount"=>{"value"=>"10000.00", "currency"=>"IDR"},
                     "referenceNo"=>"",
                     "partnerReferenceNo"=>"202010290000000001",
                     "beneficiaryBankCode"=>"ABALIDBS",
                     "sourceAccountNo"=>"**********",
                     "additionalInfo"=>{"bifastId"=>""}}

    stub_request(:post, "#{BCA::Constants::BASE_URL}/openapi/v2.0/transfer-interbank")
      .with(
        body: "{\"partnerReferenceNo\":\"202010290000000001\",\"sourceAccountNo\":\"**********\",\"beneficiaryEmail\":\"\",\"beneficiaryBankCode\":\"ABALIDBS\",\"beneficiaryAccountNo\":\"*********\",\"beneficiaryAccountName\":\"Yories Yolanda\",\"amount\":{\"value\":\"10000.00\",\"currency\":\"IDR\"},\"transactionDate\":\"2025-01-31T14:53:45+07:00\",\"additionalInfo\":{\"transferType\":\"2\",\"purposeCode\":\"02\"}}",
        headers: {
          'Authorization'=>'Bearer yh9K9mE733xqfPt2iy33jV0ZVCkWIWGOi2TL1A7UHBAmBAqomHQI9G',
          'Content-Type'=>'application/json',
          'Channel-Id'=>'95051',
          'User-Agent'=>'Faraday v1.10.4',
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'X-External-Id'=>/.*/,
          'X-PARTNER-Id'=>'UATCORP001',
          'X-Timestamp'=>'2025-01-31T14:53:45+07:00',
          'X-Signature'=>/.*/
        })
      .to_return(status: 404, body: response_body.to_json, headers: {})
  end
end