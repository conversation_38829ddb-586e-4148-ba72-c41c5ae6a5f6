require './spec/shared/recipes'
require './spec/shared/order_types'


RSpec.shared_context "recipe line customs creations" do
  include_context 'recipes creations'
  include_context 'order_types creations'

  let(:recipe_line_custom) do
    create(
      :recipe_line_custom,
      recipe: latte_recipe_made_to_order,
      order_type_ids: [order_type.id,order_type_2.id],
    )
  end
  let(:recipe_line_custom_2) do
    create(
      :recipe_line_custom,
      recipe: cheese_burger_recipe_batches,
      order_type_ids: [order_type_2.id]
    )
  end
  let(:recipe_line_custom_3) do
    create(
      :recipe_line_custom,
      recipe: spicy_burger_recipe_made_to_order,
      order_type_ids: [order_type.id,order_type_2.id]
    )
  end
  let(:recipe_line_custom_4) do
    create(
      :recipe_line_custom,
      recipe: sugar_modifier_owned_branch_1_recipe_made_to_order,
      order_type_ids: [order_type.id,order_type_2.id]
    )
  end

  let(:recipe_line_custom_with_detail) do
    recipe_line_custom.recipe_line_custom_details << build(:recipe_line_custom_detail, product: espresso, product_unit: espresso.product_unit )
    recipe_line_custom
  end

  let(:recipe_line_custom_with_detail_2) do
    recipe_line_custom_2.recipe_line_custom_details << build(:recipe_line_custom_detail, product: few_sugar, product_unit: few_sugar.product_unit )
    recipe_line_custom_2
  end

  let(:recipe_line_custom_with_detail_3) do
    recipe_line_custom_3.recipe_line_custom_details << build(:recipe_line_custom_detail, product: few_sugar, product_unit: few_sugar.product_unit )
    recipe_line_custom_3
  end

  let(:recipe_line_custom_with_detail_4) do
    recipe_line_custom_4.recipe_line_custom_details << build(:recipe_line_custom_detail, product: espresso, product_unit: espresso.product_unit )
    recipe_line_custom_4
  end
end
