require './spec/shared/access_lists'

# @deprecated: Do not add more spec to this file.
# Add to this file orders_index_swagger_spec.rb instead!
describe 'API orders', type: :request, search: true do
  include_context 'access lists creations'

  let(:owner) { create(:hq_owner) }
  let(:central_kitchen) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location ' + SecureRandom.hex,
                                      initial: 'Initial ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'central_kitchen',
                                      central_kitchen_ids: []
                                    }))
  end
  let(:main_branch) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location ' + SecureRandom.hex,
                                      initial: 'Initial ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:brand) { owner.active_brand }
  let(:owner_manage_brand) { owner.user_manage_brands.find_by(brand_id: brand.id) }

  let(:sub_branch_1) do
    owner.create_new_location(build(:location_params, {
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:sub_branch_2) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location ' + SecureRandom.hex,
                                      initial: 'Initial ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:sub_branch_franchise) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location franchise ' + SecureRandom.hex,
                                      initial: 'Initial franchise ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id],
                                      is_franchise: true
                                    }))
  end
  let(:employee) { create(:confirmed_user, location_ids: [sub_branch_1.id, sub_branch_franchise.id]) }
  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product_unit_1) { create(:product_unit, brand: brand) }
  let(:product) { create(:product, brand: brand, product_unit: product_unit) }
  let(:order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_buy_price: product.internal_price(nil, product_unit.id),
                                            product_unit_id: product_unit.id)
    build(:order_params, location_from_id: sub_branch_1.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: order_lines)
  end
  let(:order_request_params_with_discount) do
    order_lines = build(:order_line_params, product_id: product.id, product_buy_price: product.internal_price(nil, product_unit.id),
                        product_unit_id: product_unit.id, discount: '1')
    build(:order_params, order_transaction_lines_attributes: order_lines)
  end
  let(:vendor) { create(:vendor, :active, location_ids: [central_kitchen.id], brand: brand) }
  let!(:outgoing_order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: vendor) }
  let!(:order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: central_kitchen) }
  let(:other_order) { create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: sub_branch_1, location_to: central_kitchen) }
  let(:order_sub_branch_2) do
    create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: sub_branch_2, location_to: central_kitchen)
  end

  let(:owner_sub_branch_1_access_list) do
    location_user = LocationsUser.find_by(location: sub_branch_1, user: owner)
    location_user.access_list
  end

  let(:owner_branch_sub_branch_1_access_list) do
    location_user = UserManageBrand.find_by(brand: sub_branch_1.brand, user: owner)
    location_user.access_list
  end

  let(:vendor) { create(:vendor, :active, location_ids: [sub_branch_1.id], brand: brand) }
  let(:vendor_all_loc) { create(:vendor, :active, location_ids: [], brand: brand, owner_location: central_kitchen) }
  let(:external_order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: vendor) }
  let(:external_order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_unit_id: product_unit.id)
    build(:order_external_params, location_to_id: vendor.id, location_to_type: 'Vendor',
                                  location_from_id: main_branch.id, location_from_type: 'Location',
                                  order_transaction_lines_attributes: order_lines)
  end

  context 'default preset for user in HQ', bullet: :skip do
    before do
      @header = authentication_header(owner)
    end

    it 'should be able to access orders', search: true do
      other_order
      order_sub_branch_2
      OrderTransaction.search_index.refresh

      get '/api/orders', params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['orders', 'paging']

      order_ids = response_body['orders'].map { |order| order['id'] }
      expect(order_ids).to include(other_order.id, order_sub_branch_2.id)
    end

    it 'should be able to access order detail' do
      get "/api/orders/#{order.id}", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
      expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "order_date", "user_from_id",
           "location_from_id", "location_to_id", "shipping_fee", "autovoid_unpaid_scheduled_at", "autovoided_at", "notes", "void_notes", "total_amount", "created_at", "updated_at", "deleted",
           "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id", "user_from_fullname",
           "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
           "location_from_postal_code", "location_from_contact_number", "location_to_name", "location_to_shipping_address", "location_to_city",
           "location_to_province", "location_to_country", "location_to_postal_code", "location_to_contact_number", "total_tax", "applied_promos", "discount_total",
           'can_reorder', "enable_approve", "invoices", "shipping_fee_paid", "subtotal_amount", "transaction_fee",
           "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes", "procurement_payment_status",
           "paid_at", "online_payment_display", "online_shipping_fee_payment_display", "online_shipping_fee_added_display", "customer_order_id",
           "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at", "is_bulk_order", "parent_order_transaction_id",
           "fulfillment_location", "status_name", "approval_date", "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
           "location_from", "location_to", "user_from", "total_products", "can_create_delivery", "can_void", 'can_print_order_form', 'is_duplicate', 'is_multibrand', 'multibrand_master_order_id',
           "can_create_fulfillment", "can_manage_price_and_discount", "can_approve", "is_closed_period", "request_delivery_date",
           "taxes_with_total_tax_amount","order_attachments", "can_update", "only_price_update", "metadata", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
      expect(response_body['order_detail']['location_from'].keys).to match_array ['id', 'name', 'city', 'contact_number', 'brand_id', 'franchise_pic_name', 'tax_identification_no', 'tax_company_registration_no',
                                                                                  'contact_number_country_code', 'contact_number_country', 'public_contact_number', 'public_contact_number_country_code', 'country', 'postal_code', 'province', 'shipping_address', 'type', 'branch_type', 'is_franchise']
      expect(response_body['order_detail']['location_to'].keys).to match_array ['id', 'name', 'city', 'contact_number','brand_id', 'franchise_pic_name', 'tax_identification_no', 'tax_company_registration_no',
                                                                                'contact_number_country_code', 'contact_number_country', 'public_contact_number', 'public_contact_number_country_code', 'country', 'postal_code', 'province', 'shipping_address', 'type', 'branch_type', 'is_franchise']
      expect(response_body['order_detail_lines'][0].keys).to match_array ['id', "parent_order_line_id", 'product_buy_price', 'total_amount', 'product_unit_conversion_qty', 'multibrand_master_order_line_id', 'metadata',
                                                                          'product_qty', 'discount', 'discount_amount', 'order_transaction_id', 'product_id', 'product_unit_id', 'product_unit_conversion_id', 'tax_id', 'tax_name', 'tax_rate', 'product_upc', 'product_description', 'product_name', 'product_sku', 'product_unit_name', 'created_at', 'updated_at', 'deleted', 'product', 'product_unit', 'open_qty',
                                                                          "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo", "can_edit_price_by_franchisor"]
      expect(response_body['order_detail_lines'][0]['product'].keys).to match_array ['name', 'id', 'sku', 'description',  'upc', 'image_url', 'stock_availability']
      expect(response_body['order_detail_lines'][0]['product_unit'].keys).to match_array ['name', 'id', 'converted_qty']

      expect(response_body['order_detail']['id']).to eq order.id
      expect(response_body['order_detail']['total_amount']).to be_present
      expect(response_body['order_detail']['can_manage_price_and_discount']).to eq true

      response_order_line = response_body['order_detail_lines'][0]
      expect(response_order_line['discount']).to eq nil
      expect(response_order_line['product_buy_price']).to eq nil
      expect(response_order_line['total_amount']).to be_present
    end

    it 'should be able to access order detail grouped by category' do
      product_with_category = create(:product, brand: brand, product_unit_conversions: [
        build(:product_unit_conversion, product_unit: product_unit_1)
      ])
      product_with_category_1 = create(:product, brand: brand, product_unit: product_unit_1)
      order_same_product = build(:order_transaction, brand: brand, user_from_id: employee.id, location_from: sub_branch_1,
                                                     location_to: central_kitchen)
      order_line_1 = build(:order_transaction_line, product: product_with_category,
                                                    product_buy_price: product_with_category.internal_price(nil, product_with_category.id),
                                                    product_unit: product_with_category.product_unit)
      order_line_2 = build(:order_transaction_line, product: product_with_category,
                                                    product_buy_price: product_with_category.internal_price(nil, product_with_category.id),
                                                    product_unit: product_with_category.product_unit_conversions.first.product_unit)
      product_with_category.procurement_units.create(product_unit_id: product_with_category.product_unit_conversions.first.product_unit.id)
      order_same_product.order_transaction_lines << order_line_1
      order_same_product.order_transaction_lines << order_line_2
      order_same_product.save!

      get "/api/orders/#{order_same_product.id}", params: { format: 'json', group_by: 'category' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines', 'total_products']

      expect(response_body['order_detail']['id']).to eq order_same_product.id
      expect(response_body['order_detail']['total_amount']).to be_present
      expect(response_body['order_detail']['can_manage_price_and_discount']).to eq true
      response_order_line = response_body['order_detail_lines'][0]
      expect(response_order_line['category']).to be_present
      expect(response_order_line['order_detail_lines']).to be_present
      expect(response_order_line['order_detail_lines'][0]['product_id']).to eq product_with_category.id
      expect(response_order_line['order_detail_lines'][1]['product_id']).to eq product_with_category.id
    end

    it 'should NOT be able to see prices in order detail' do
      owner_sub_branch_1_access_list.location_permission['order']['price_show_internal'] = false
      owner_sub_branch_1_access_list.save!

      get "/api/orders/#{order.id}", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['order_detail']['id']).to eq order.id
      expect(response_body['order_detail']['total_amount']).to_not be_present
      expect(response_body['order_detail']['can_manage_price_and_discount']).to eq false

      response_order_line = response_body['order_detail_lines'][0]
      expect(response_order_line['discount']).to_not be_present
      expect(response_order_line['product_buy_price']).to_not be_present
      expect(response_order_line['total_amount']).to_not be_present
    end

    it 'should be able to create order detail' do
      expect do
        post '/api/orders', params: { format: 'json', order_transaction: order_request_params }, headers: @header
        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:created)
      end.to change { OrderTransaction.count }.by 1
    end

    it 'should be able to update order detail' do
      expect do
        patch "/api/orders/#{order.id}", params: { format: 'json', order_transaction: { shipping_fee: order.shipping_fee + 100 } }, headers: @header
        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        order.reload
      end.to change { order.shipping_fee }
    end

    it 'should not be able to see order from location not allowed' do
      other_order
      order_sub_branch_2
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all

      auth = authentication_header(employee)
      get '/api/orders', params: { format: 'json', location_from_ids: [sub_branch_2.id] }, headers: auth
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['orders'].map { |order| order['id'] }.include?(order_sub_branch_2.id)).to eq false
    end

    it 'should be able to void order' do
      patch "/api/orders/#{order.id}/void", params: { format: 'json', order_transaction: { void_notes: 'Void Notes' } }, headers: @header
      expect(response).to have_http_status(:no_content)
      expect(order.reload.void?).to eq true
      expect(order.reload.audits.last.custom_action).to eq 'void'
    end

    it 'should be able to close order' do
      order.approve
      patch "/api/orders/#{order.id}/close", params: { format: 'json', order_transaction: { closed_notes: 'closed Notes' } }, headers: @header
      expect(response).to have_http_status(:ok)
      expect(order.reload.closed?).to eq true
      expect(order.reload.audits.last.custom_action).to eq 'close_order'
    end

    it 'should be able to approve order that is created from other location' do
      patch "/api/orders/#{order.id}/approve", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:no_content)
      expect(order.reload.processing?).to eq true
      expect(order.reload.audits.last.custom_action).to eq 'approve'
    end

    it 'should be able to update payment status of order' do
      expect do
        patch "/api/orders/#{order.id}/payment_status", params: { format: 'json', order_transaction: { payment_status: 'paid' } }, headers: @header
        expect(response).to have_http_status(:no_content)
        expect(order.reload.audits.last.custom_action).to eq 'record_payment'
      end.to change { order.reload.payment_status }
    end

    it 'should be able to export order detail' do
      get "/api/orders/#{order.id}.pdf", headers: @header
      expect(response.media_type).to eq('application/pdf')
    end

    it 'should be able to export outgoing order detail as link' do
      FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
      YourlsClient::YourlsWrapper.stub(:url_shortener) { 'https://short.url' }
      get "/api/orders/#{outgoing_order.id}.pdf", params: { as_link: true, format: 'pdf' }, headers: @header
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      expect(response_body['url']).to eq('https://short.url')
    end

    it 'should be able to query order lines with open quantity' do
      order.approve
      get "/api/orders/#{order.id}/open_quantity_order_lines", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      response_first_order_line = response_body['order_detail_lines'].first
      first_order_line = order.order_transaction_lines.first

      expect(response_body.keys).to match_array ['order_detail_lines']
      expect(response_first_order_line.keys).to match_array ['product_name', 'quantity', 'product_unit_name']
      expect(response_first_order_line['product_name']).to eq first_order_line.product.name
      expect(response_first_order_line['quantity']).to eq(first_order_line.open_qty.to_s)
      expect(response_first_order_line['product_unit_name']).to eq(first_order_line.product_unit.name)
    end

    it 'should be able to export order invoice' do
      get "/api/orders/#{order.id}/invoice.pdf", params: { format: 'pdf' }, headers: @header
      expect(response).to have_http_status(:ok)
    end

    it 'should be able to update shipping_fee once approve' do
      order
      order.approve(owner)

      patch "/api/orders/#{order.id}/update_shipping_fee", params: { order_transaction: { shipping_fee: 300 } }, headers: @header
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      expect(order.reload.shipping_fee).to eq 300
    end

    it 'should be able to show order history' do
      get "/api/orders/#{order.id}/history", headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['audits']
    end

    it 'should be able to show order with delivery information' do
      get "/api/orders/#{order.id}/show_with_delivery", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
    end

    it 'should be able to show delivery list from an order' do
      get "/api/orders/#{order.id}/related_transactions", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['related_transactions']
    end

    it 'should be able to return location to for order' do
      get '/api/orders/location_to', params: { format: 'json', location_from_id: central_kitchen.id, location_from_type: 'Location' },
                                     headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['data', 'paging']
    end

    it 'should be able to return location on all vendor', search: true do
      vendor_all_loc

      Vendor.search_index.refresh
      Location.search_index.refresh

      get '/api/orders/location_to', params: { format: 'json', location_from_id: central_kitchen.id, location_from_type: 'Location' },
                                     headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['data'].size).to eql(1)
    end

    it 'should be able to return location from for order' do
      get '/api/orders/location_from', params: { format: 'json', location_to_id: central_kitchen.id, location_to_type: 'Location' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['data', 'paging', 'has_all_locations_access']
      expect(response_body['has_all_locations_access']).to be_in([true, false])
    end
  end

  context 'custom preset for user in sub branch', bullet: :skip do
    let(:employee_access_list) do
      location_user = LocationsUser.find_by(location: sub_branch_1, user: employee)
      location_user.access_list
    end

    let(:employee_access_list_2) do
      location_user = LocationsUser.find_by(location: sub_branch_2, user: employee)
      location_user.access_list
    end

    before do
      location_user = LocationsUser.find_by(user_id: employee.id)
      location_user.access_list = sub_branch_permission
      location_user.save

      @header = authentication_header(employee)
    end

    it 'should be able to get available location for deliveires', search: true do
      other_order
      order_sub_branch_2
      other_order.approve(employee)
      order_sub_branch_2.approve(employee)
      employee_access_list.location_permission['order']['index'] = true
      employee_access_list.save!

      OrderTransaction.search_index.refresh

      get '/api/orders/available_delivery_location_to',
          params: { format: 'json', location_from_id: central_kitchen.id, location_from_type: 'Location' }, headers: @header

      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)

      location_ids = response_body['data'].map { |order| order['id'] }
      expect(location_ids).to include(sub_branch_1.id)
      expect(location_ids).to include(sub_branch_2.id)
    end

    it 'should NOT be able to access orders' do
      LocationsUser.where(user: employee).map do |location_user|
        access_list = location_user.access_list
        access_list.location_permission['order']['index'] = false
        access_list.save!
      end

      get '/api/orders', params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should be able to access order detail' do
      employee_access_list.location_permission['order']['show'] = true
      employee_access_list.save!

      get "/api/orders/#{other_order.id}", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(response_body['order_detail']['id']).to eq other_order.id
    end

    it 'should NOT be able to access order detail' do
      LocationsUser.where(user: employee).map do |location_user|
        access_list = location_user.access_list
        access_list.location_permission['order']['show'] = false
        access_list.save!
      end

      get "/api/orders/#{other_order.id}", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should NOT be able to access order detail on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['show'] = true
      employee_access_list.save!
      get "/api/orders/#{order_sub_branch_2.id}", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:not_found)
    end

    it 'should NOT be able to create order detail' do
      employee_access_list.location_permission['order']['create'] = false
      employee_access_list.save!

      expect do
        post '/api/orders', params: { format: 'json', order_transaction: order_request_params }, headers: @header
        expect(response).to have_http_status(:forbidden)
      end.to change { OrderTransaction.count }.by 0
    end

    it 'should NOT be able to create order detail on other location' do
      employee_access_list.location_permission['order']['create'] = true
      employee_access_list.save!

      expect do
        order_request_params['location_from_id'] = sub_branch_2
        post '/api/orders', params: { format: 'json', order_transaction: order_request_params }, headers: @header
        expect(response).to have_http_status(:forbidden)
      end.to change { OrderTransaction.count }.by 0
    end

    it 'should NOT be able to update order detail' do
      LocationsUser.where(user: employee).map do |location_user|
        access_list = location_user.access_list
        access_list.location_permission['order']['update'] = false
        access_list.save!
      end

      expect do
        patch "/api/orders/#{order.id}", params: { format: 'json', order_transaction: { shipping_fee: order.shipping_fee + 100 } }, headers: @header
        expect(response).to have_http_status(:forbidden)
        order.reload
      end.to_not change { order.shipping_fee }
    end

    it 'should NOT be able to update order detail on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['update'] = true
      employee_access_list.save!

      expect do
        patch "/api/orders/#{order_sub_branch_2.id}", params: { format: 'json', order_transaction: { shipping_fee: order.shipping_fee + 100 } },
                                                      headers: @header
        expect(response).to have_http_status(:not_found)
        order.reload
      end.to_not change { order_sub_branch_2.shipping_fee }
    end

    it 'should NOT be able to void order' do
      employee_access_list.location_permission['order']['void_internal'] = false
      employee_access_list.save!

      patch "/api/orders/#{order.id}/void", params: { format: 'json', order_transaction: { void_notes: 'Void Notes' } }, headers: @header
      expect(response).to have_http_status(:forbidden)
      expect(order.reload.void?).to eq false
    end

    it 'should NOT be able to void order on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['void_internal'] = true
      employee_access_list.save!

      patch "/api/orders/#{order_sub_branch_2.id}/void", params: { format: 'json', order_transaction: { void_notes: 'Void Notes' } }, headers: @header
      expect(response).to have_http_status(:not_found)
      expect(order_sub_branch_2.reload.void?).to eq false
    end

    it 'should NOT be able to approve order that is created from other location' do
      employee_access_list.location_permission['order']['approve_internal'] = false
      employee_access_list.save!

      patch "/api/orders/#{other_order.id}/approve", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
      expect(other_order.reload.processing?).to eq false
    end

    it 'should NOT be able to approve order for other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['accept'] = true
      employee_access_list.save!

      patch "/api/orders/#{order_sub_branch_2.id}/approve", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:not_found)
      expect(order_sub_branch_2.reload.processing?).to eq false
    end

    it 'should NOT be able to update payment status of order' do
      LocationsUser.where(user: employee).map do |location_user|
        access_list = location_user.access_list
        access_list.location_permission['order']['payment_status'] = false
        access_list.save!
      end

      expect do
        patch "/api/orders/#{order.id}/payment_status", params: { format: 'json', order_transaction: { payment_status: 'paid' } }, headers: @header
        expect(response).to have_http_status(:forbidden)
      end.to_not change { order.reload.payment_status }
    end

    it 'should NOT be able to update payment status of order on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['payment_status'] = true
      employee_access_list.save!

      expect do
        patch "/api/orders/#{order_sub_branch_2.id}/payment_status", params: { format: 'json', order_transaction: { payment_status: 'paid' } },
                                                                     headers: @header
        expect(response).to have_http_status(:not_found)
      end.to_not change { order_sub_branch_2.reload.payment_status }
    end

    it 'should NOT be able to export order detail' do
      employee_access_list.location_permission['order']['export'] = false
      employee_access_list.save!

      get "/api/orders/#{order.id}.pdf", headers: @header
      expect(response.media_type).to eq('application/pdf')
      expect(response).to have_http_status(:forbidden)
    end

    it 'should NOT be able to export order detail on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['export'] = true
      employee_access_list.save!

      get "/api/orders/#{order_sub_branch_2.id}.pdf", headers: @header
      expect(response).to have_http_status(:not_found)
    end

    it 'should NOT be able to export order invoice' do
      employee_access_list.location_permission['order']['export'] = false
      employee_access_list.save!

      get "/api/orders/#{order.id}/invoice.pdf", params: { format: 'pdf' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should NOT be able to export order invoice on other location' do
      LocationsUser.where(location_id: order_sub_branch_2.location_from_id, user: employee).destroy_all
      employee_access_list.location_permission['order']['export'] = true
      employee_access_list.save!

      get "/api/orders/#{order_sub_branch_2.id}/invoice.pdf", params: { format: 'pdf' }, headers: @header
      expect(response).to have_http_status(:not_found)
    end

    it 'should NOT be able to show order history' do
      employee_access_list.location_permission['order']['history'] = false
      employee_access_list.save!

      get "/api/orders/#{order.id}/history", headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should NOT be able to show order with delivery information' do
      employee_access_list.location_permission['order']['show'] = false
      employee_access_list.location_permission['delivery']['index'] = false
      employee_access_list.save!

      get "/api/orders/#{order.id}/show_with_delivery", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should NOT be able to show delivery list from an order' do
      employee_access_list.location_permission['order']['show'] = false
      employee_access_list.location_permission['delivery']['index'] = false
      employee_access_list.save!

      get "/api/orders/#{order.id}/related_transactions", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end
  end
end
