require './spec/shared/reports'
require './spec/shared/locations'
require './spec/shared/bulk_sale_returns'
require './spec/shared/order_types'
require './spec/shared/swagger'
require './spec/shared/product_groups'
require './spec/shared/devices'
require './spec/shared/payment_methods'
require './spec/shared/sale_transactions'
require './spec/shared/procurements'
require './spec/shared/recipes'

RSpec.describe 'api/report/sales_by', type: :request, clickhouse: true do
  include_context 'locations creations'
  include_context 'swagger after response'
  include_context 'bulk sale returns creations'
  include_context 'order_types creations'
  include_context 'product group creations'
  include_context 'devices creations'
  include_context 'payment methods creations'
  include_context 'sale transaction creations'
  include_context 'procurements creations'
  include_context 'recipes creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 24, 11, 0)
    @header = authentication_header(owner)

    Flipper.enable(:sales_by_modifier_query_use_filtered_sale_detail_transactions)
  end

  after(:each) do
    travel_back
    Flipper.disable(:sales_by_modifier_query_use_filtered_sale_detail_transactions)
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid
  end
  let(:Authorization) { @header['Authorization'] }

  def web_report_headers
    ["Product Name", "SKU", "Category Name", "Sold Qty", "Refund Qty",
     "% Sold Qty", "Gross Sales", "Refund Amount", "Discount", "Surcharge",
     "Net Amount", "% Of Sales", "Cost", "Gross Profit", "% Margin"]
  end

  path '/api/report/sales_by', search: true do
    get 'Sales By report' do
      tags 'Restaurant - Sales By Report'
      security [bearerAuth: []]
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :page, in: :query, type: :integer
      parameter name: :group_by, in: :query, schema: {
        type: :string, enum: %i[product category category_group]
      }
      parameter name: :sort_by, in: :query, type: :string, required: false
      parameter name: :item_per_page, in: :query, type: :integer
      parameter name: :start_date, in: :query, type: :string
      parameter name: :end_date, in: :query, type: :string

      parameter name: :product_ids, in: :query, type: :string, required: false
      parameter name: :exclude_product_ids, in: :query, type: :string, required: false
      parameter name: :is_select_all_product, in: :query, type: :string, required: false

      parameter name: :product_group_ids, in: :query, type: :string, required: false
      parameter name: :exclude_product_group_ids, in: :query, type: :string, required: false
      parameter name: :is_select_all_product_group, in: :query, type: :string, required: false

      parameter name: :daily_sale_id, in: :query, type: :string, required: false
      parameter name: :location_ids, in: :query, type: :string, required: false
      parameter name: :location_group_ids, in: :query, type: :string, required: false
      parameter name: :is_mobile_report, in: :query, type: :string, required: false
      let(:page) { 1 }
      let(:item_per_page) { 5 }
      let(:start_date) { (Time.zone.now - 30.days).strftime('%d/%m/%Y') }
      let(:end_date) { Time.zone.now.strftime('%d/%m/%Y') }
      let(:location_ids) { [franchise_branch_1.id].join(',') }
      let(:location_group_ids) { "" }

      response '200', 'get sales by' do
        let(:group_by) { 'product' }

        before do
          franchise_branch_1
          sale_transaction_9
          sale_transaction_10
          sale_transaction_11

          Location.search_index.refresh
          SaleTransaction.search_index.refresh
        end

        context 'when use estimate cost' do
          let(:sort_by) { 'qty' }

          before do
            setup_report_setting = brand.setup_report_setting
            setup_report_setting.update!(use_estimate_cost: true)
          end

          context 'when has costing, some products has no real costs, and estimate cost is from delivery' do
            let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

            before do |example|
              costing = Costing.create!(brand_id: brand.id, location_id: franchise_branch_1.id, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              sale_costing = costing.sale_costings.create!(location_id: franchise_branch_1.id,
                                brand_id: brand.id,
                                start_period: Time.zone.today - 2.months,
                                end_period: Time.zone.today - 1.day,
                                producer_index: 1)

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.product_id).update_all(price_unit: 1000)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.sale_detail_modifiers.pluck(:product_id)).update_all(price_unit: 100)

              costing.calculate_cost_of_products

              latte_sale_detail_transaction.reload
              sale_detail_transaction_6.reload
              emping_sale_detail_modifier.reload

              # Simulate removing a cost from sale detail and modifier.
              # Shouldn't be N/A in cost columns because we need to acquire the estimate cost.
              meta = latte_sale_detail_transaction.meta
              meta.delete('cost')
              latte_sale_detail_transaction.update_columns(meta: meta)

              meta = emping_sale_detail_modifier.meta
              meta.delete('cost')
              emping_sale_detail_modifier.update_columns(meta: meta)

              sale_transaction_12.update_columns(local_sales_time: (Time.zone.now - 15.days))
              sale_transaction_12.sale_detail_transactions.first.update_columns(local_sales_time: (Time.zone.now - 15.days))

              Flipper.enable(:inventory_v2)
              # Delivery to franchise branch 1
              order_transaction_location_from_is_franchise_delivery
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              acceptance_param = {
                pic_id: owner.id,
                delivery_transaction_lines_attributes: order_transaction_location_from_is_franchise_delivery.delivery_transaction_lines.map do |delivery_transaction_line|
                  { id: delivery_transaction_line.id, received_quantity: 2 }
                end,
                delivery_acceptance_notes_attributes: [
                  { message: 'completed', note_type: 'completed' }
                ]
              }
              Restaurant::Services::Procurement::DeliveryReceiver
                .new(order_transaction_location_from_is_franchise_delivery, owner, acceptance_param)
                .call

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              # Delivery to franchise branch 2
              order_transaction_location_from_is_franchise_distant_delivery_3
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              acceptance_param = {
                pic_id: owner.id,
                delivery_transaction_lines_attributes: order_transaction_location_from_is_franchise_distant_delivery_3.delivery_transaction_lines.map do |delivery_transaction_line|
                  { id: delivery_transaction_line.id, received_quantity: 2 }
                end,
                delivery_acceptance_notes_attributes: [
                  { message: 'completed', note_type: 'completed' }
                ]
              }
              Restaurant::Services::Procurement::DeliveryReceiver
                .new(order_transaction_location_from_is_franchise_distant_delivery_3, owner, acceptance_param)
                .call

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              InventoryPurchaseCard.update_all(stock_date: '9/2/2023'.to_date)

              submit_request(example.metadata)
            end

            after do
              Flipper.disable(:inventory_v2)
            end

            it 'should return valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                {"reports"=>
                [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                  {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                  {"text"=>"Food", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"5.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"5.166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"13,42%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"400",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"4.766,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"92,26%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"20.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"23.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"59,74%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"12.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"54,35%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                  {"text"=>"2",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"20%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"10.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"26,84%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"2.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"8.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"80,65%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                  {"text"=>nil, "weight"=>500, "colspan"=>1},
                  {"text"=>"", "weight"=>500, "colspan"=>1},
                  {"text"=>"10",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"35.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"38.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"12.900",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"25.600",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"66,49%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}]],
                "paging"=>
                {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                "report_headers"=>
                [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                  {"text"=>"SKU", "size"=>12, "colspan"=>1},
                  {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                  {"text"=>"Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Sales",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Discount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Surcharge",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Net Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Of Sales",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Cost",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Profit",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Margin",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1}]}
              )
            end
          end

          context 'when has costing, some products has no real costs, and estimate cost is from ck delivery' do
            let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

            before do |example|
              costing = Costing.create!(brand_id: brand.id, location_id: franchise_branch_1.id, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              sale_costing = costing.sale_costings.create!(location_id: franchise_branch_1.id,
                                brand_id: brand.id,
                                start_period: Time.zone.today - 2.months,
                                end_period: Time.zone.today - 1.day,
                                producer_index: 1)

              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.product_id).update_all(price_unit: 1000)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.sale_detail_modifiers.pluck(:product_id)).update_all(price_unit: 100)

              costing.calculate_cost_of_products

              latte_sale_detail_transaction.reload
              sale_detail_transaction_6.reload
              emping_sale_detail_modifier.reload

              # Simulate removing a cost from sale detail and modifier.
              # Shouldn't be N/A in cost columns because we need to acquire the estimate cost.
              meta = latte_sale_detail_transaction.meta
              meta.delete('cost')
              latte_sale_detail_transaction.update_columns(meta: meta)

              meta = emping_sale_detail_modifier.meta
              meta.delete('cost')
              emping_sale_detail_modifier.update_columns(meta: meta)

              sale_transaction_12.update_columns(local_sales_time: (Time.zone.now - 15.days))
              sale_transaction_12.sale_detail_transactions.first.update_columns(local_sales_time: (Time.zone.now - 15.days))

              Flipper.enable(:inventory_v2)
              # Delivery from vendor to CK
              franchise_branch_1.central_kitchen_ids = [central_kitchen.id, central_kitchen_2.id]
              franchise_branch_1.save!
              latte_order_line = order_transaction_from_ck_to_vendor_2.order_transaction_lines.find_by(product: latte)
              latte_order_line.update_columns(product_buy_price: 2222, total_amount: 4444)
              external_delivery_to_ck_2
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              acceptance_param = {
                pic_id: owner.id,
                delivery_transaction_lines_attributes: external_delivery_to_ck_2.delivery_transaction_lines.map do |delivery_transaction_line|
                  { id: delivery_transaction_line.id, received_quantity: 2 }
                end,
                delivery_acceptance_notes_attributes: [
                  { message: 'completed', note_type: 'completed' }
                ]
              }
              Restaurant::Services::Procurement::DeliveryReceiver
                .new(external_delivery_to_ck_2, owner, acceptance_param)
                .call

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              # Delivery to franchise branch 2
              order_transaction_location_from_is_franchise_distant_delivery_3
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              acceptance_param = {
                pic_id: owner.id,
                delivery_transaction_lines_attributes: order_transaction_location_from_is_franchise_distant_delivery_3.delivery_transaction_lines.map do |delivery_transaction_line|
                  { id: delivery_transaction_line.id, received_quantity: 2 }
                end,
                delivery_acceptance_notes_attributes: [
                  { message: 'completed', note_type: 'completed' }
                ]
              }
              Restaurant::Services::Procurement::DeliveryReceiver
                .new(order_transaction_location_from_is_franchise_distant_delivery_3, owner, acceptance_param)
                .call

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              InventoryPurchaseCard.update_all(stock_date: '9/2/2023'.to_date)

              franchise_branch_1.update_columns(is_franchise: false)
              franchise_branch_2.update_columns(is_franchise: false)
              submit_request(example.metadata)
            end

            after do
              Flipper.disable(:inventory_v2)
            end

            it 'should return valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                {"reports"=>
                [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                  {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                  {"text"=>"Food", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"5.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"5.166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"13,42%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"400",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"4.766,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"92,26%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"20.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"23.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"59,74%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"12.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"54,35%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                  {"text"=>"2",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"20%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"10.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"26,84%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"2.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"8.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"80,65%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                  {"text"=>nil, "weight"=>500, "colspan"=>1},
                  {"text"=>"", "weight"=>500, "colspan"=>1},
                  {"text"=>"10",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"35.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"38.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"12.900",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"25.600",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"66,49%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}]],
                "paging"=>
                {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                "report_headers"=>
                [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                  {"text"=>"SKU", "size"=>12, "colspan"=>1},
                  {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                  {"text"=>"Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Sales",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Discount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Surcharge",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Net Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Of Sales",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Cost",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Profit",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Margin",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1}]}
              )
            end
          end

          context 'when has costing, some products has no real costs, and estimate cost is from other internal outlet deliveries' do
            let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

            before do |example|
              costing = Costing.create!(brand_id: brand.id, location_id: franchise_branch_1.id, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              sale_costing = costing.sale_costings.create!(location_id: franchise_branch_1.id,
                                brand_id: brand.id,
                                start_period: Time.zone.today - 2.months,
                                end_period: Time.zone.today - 1.day,
                                producer_index: 1)

              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.product_id).update_all(price_unit: 1000)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.sale_detail_modifiers.pluck(:product_id)).update_all(price_unit: 100)

              costing.calculate_cost_of_products

              latte_sale_detail_transaction.reload
              sale_detail_transaction_6.reload
              emping_sale_detail_modifier.reload

              # Simulate removing a cost from sale detail and modifier.
              # Shouldn't be N/A in cost columns because we need to acquire the estimate cost.
              meta = latte_sale_detail_transaction.meta
              meta.delete('cost')
              latte_sale_detail_transaction.update_columns(meta: meta)

              meta = emping_sale_detail_modifier.meta
              meta.delete('cost')
              emping_sale_detail_modifier.update_columns(meta: meta)

              sale_transaction_12.update_columns(local_sales_time: (Time.zone.now - 15.days))
              sale_transaction_12.sale_detail_transactions.first.update_columns(local_sales_time: (Time.zone.now - 15.days))

              Flipper.enable(:inventory_v2)
              inventory_latte = create(:inventory, location_id: owned_branch_2.id, product_id: latte.id, resource: latte, resource_line: latte)
              card_latte = create(:inventory_purchase_card, location_id: owned_branch_2.id, product_id: latte.id, inventory_id: inventory_latte.id, price: 3335)

              # Delivery to franchise branch 2
              order_transaction_location_from_is_franchise_distant_delivery_3
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              acceptance_param = {
                pic_id: owner.id,
                delivery_transaction_lines_attributes: order_transaction_location_from_is_franchise_distant_delivery_3.delivery_transaction_lines.map do |delivery_transaction_line|
                  { id: delivery_transaction_line.id, received_quantity: 2 }
                end,
                delivery_acceptance_notes_attributes: [
                  { message: 'completed', note_type: 'completed' }
                ]
              }
              Restaurant::Services::Procurement::DeliveryReceiver
                .new(order_transaction_location_from_is_franchise_distant_delivery_3, owner, acceptance_param)
                .call

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

              InventoryPurchaseCard.update_all(stock_date: '9/2/2023'.to_date)

              franchise_branch_1.update_columns(is_franchise: false)
              franchise_branch_2.update_columns(is_franchise: false)
              submit_request(example.metadata)
            end

            after do
              Flipper.disable(:inventory_v2)
            end

            it 'should return valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                {"reports"=>
                [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                  {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                  {"text"=>"Food", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"5.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"5.166,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"13,42%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"400",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"4.766,67",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"92,26%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                  {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                  {"text"=>"4",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"40%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"20.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"23.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"59,74%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"12.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"54,35%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"latte", "weight"=>500, "colspan"=>1},
                  {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                  {"text"=>"2",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"20%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"10.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"10.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"26,84%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"2.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"8.333,33",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"80,65%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}],
                  [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                  {"text"=>nil, "weight"=>500, "colspan"=>1},
                  {"text"=>"", "weight"=>500, "colspan"=>1},
                  {"text"=>"10",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"0",
                    "alignment"=>"center",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"integer"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"35.000",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"0",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"3.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"38.500",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"100%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"},
                  {"text"=>"12.900",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"25.600",
                    "alignment"=>"right",
                    "weight"=>500,
                    "colspan"=>1,
                    "cell_format"=>"money"},
                  {"text"=>"66,49%",
                    "alignment"=>"right",
                    "opacity"=>50,
                    "weight"=>400,
                    "colspan"=>1,
                    "cell_format"=>"percentage"}]],
                "paging"=>
                {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                "report_headers"=>
                [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                  {"text"=>"SKU", "size"=>12, "colspan"=>1},
                  {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                  {"text"=>"Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Sold Qty",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Sales",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Refund Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Discount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Surcharge",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Net Amount",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Of Sales",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Cost",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"Gross Profit",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"% Margin",
                  "opacity"=>50,
                  "weight"=>400,
                  "size"=>12,
                  "colspan"=>1}]}
              )
            end
          end

          context 'when has costing, some products has no real costs, and estimate cost is from latest order' do
            let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

            before do |example|
              costing = Costing.create!(brand_id: brand.id, location_id: franchise_branch_1.id, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              sale_costing = costing.sale_costings.create!(location_id: franchise_branch_1.id,
                                brand_id: brand.id,
                                start_period: Time.zone.today - 2.months,
                                end_period: Time.zone.today - 1.day,
                                producer_index: 1)

              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.product_id).update_all(price_unit: 1000)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.sale_detail_modifiers.pluck(:product_id)).update_all(price_unit: 100)

              costing.calculate_cost_of_products

              latte_sale_detail_transaction.reload
              sale_detail_transaction_6.reload
              emping_sale_detail_modifier.reload

              # Simulate removing a cost from sale detail and modifier.
              # Shouldn't be N/A in cost columns because we need to acquire the estimate cost.
              meta = latte_sale_detail_transaction.meta
              meta.delete('cost')
              latte_sale_detail_transaction.update_columns(meta: meta)

              meta = emping_sale_detail_modifier.meta
              meta.delete('cost')
              emping_sale_detail_modifier.update_columns(meta: meta)

              sale_transaction_12.update_columns(local_sales_time: (Time.zone.now - 15.days))
              sale_transaction_12.sale_detail_transactions.first.update_columns(local_sales_time: (Time.zone.now - 15.days))

              # Create order as the last resort for estimate costs.
              order_transaction_location_from_is_franchise.update_columns(order_date: '9/2/2023'.to_date)
              order_transaction_location_from_is_franchise.order_transaction_lines.each { |order_line| order_line.update_columns(product_buy_price: 1414) }
            end

            after do
              Flipper.disable(:inventory_v2)
            end

            context 'when per 50 locations' do
              it 'should return N/A for product that dont have real cost in one of selected locations' do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result).to eq(
                  {"reports"=>
                    [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                      {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                      {"text"=>"Food", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"13,42%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"400",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"4.766,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"92,26%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"20.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"23.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"59,74%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"20%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,84%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"2.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"8.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"80,65%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                      {"text"=>nil, "weight"=>500, "colspan"=>1},
                      {"text"=>"", "weight"=>500, "colspan"=>1},
                      {"text"=>"10",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"35.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"38.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                    {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                  "report_headers"=>
                    [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Sales",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Discount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Surcharge",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Net Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Of Sales",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Cost",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Profit",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Margin",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1}]}
                )
              end
            end

            context 'when per 1 locations' do
              before do
                allow(Restaurant::Services::Report::SalesBy::EstimateCostQuerySalesByProducts).to receive(:location_partition_count).and_return(1)
              end

              it 'should return N/A for product that dont have real cost in one of selected locations' do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result).to eq(
                  {"reports"=>
                    [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                      {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                      {"text"=>"Food", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"13,42%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"400",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"4.766,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"92,26%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"20.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"23.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"59,74%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"20%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,84%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"2.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"8.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"80,65%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                      {"text"=>nil, "weight"=>500, "colspan"=>1},
                      {"text"=>"", "weight"=>500, "colspan"=>1},
                      {"text"=>"10",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"35.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"38.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                    {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                  "report_headers"=>
                    [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Sales",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Discount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Surcharge",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Net Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Of Sales",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Cost",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Profit",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Margin",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1}]}
                )
              end
            end
          end

          context 'when has costing, some products has no real costs, and estimate cost is from recipe purchase card' do
            let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

            before do |example|
              costing = Costing.create!(brand_id: brand.id, location_id: franchise_branch_1.id, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              sale_costing = costing.sale_costings.create!(location_id: franchise_branch_1.id,
                                brand_id: brand.id,
                                start_period: Time.zone.today - 2.months,
                                end_period: Time.zone.today - 1.day,
                                producer_index: 1)

              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.product_id).update_all(price_unit: 1000)
              costing.cost_per_products.where(product_id: latte_sale_detail_transaction.sale_detail_modifiers.pluck(:product_id)).update_all(price_unit: 100)

              costing.calculate_cost_of_products

              latte_sale_detail_transaction.reload
              sale_detail_transaction_6.reload
              emping_sale_detail_modifier.reload

              # Simulate removing a cost from sale detail and modifier.
              # Shouldn't be N/A in cost columns because we need to acquire the estimate cost.
              meta = latte_sale_detail_transaction.meta
              meta.delete('cost')
              latte_sale_detail_transaction.update_columns(meta: meta)

              meta = emping_sale_detail_modifier.meta
              meta.delete('cost')
              emping_sale_detail_modifier.update_columns(meta: meta)

              sale_transaction_12.update_columns(local_sales_time: (Time.zone.now - 15.days))
              sale_transaction_12.sale_detail_transactions.first.update_columns(local_sales_time: (Time.zone.now - 15.days))

              # simulate that latte has recipe
              coffee.update!(sell_unit: coffee.product_unit)
              coffee_recipe_batches
              recipe_with_lines.save!

              inventory_milk = create(:inventory, location_id: franchise_branch_1.id, product_id: milk.id, resource: milk, resource_line: milk)
              card_milk = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: milk.id, inventory_id: inventory_milk.id, price: 2424)

              inventory_milk = create(:inventory, location_id: franchise_branch_2.id, product_id: milk.id, resource: milk, resource_line: milk)
              card_milk = create(:inventory_purchase_card, location_id: franchise_branch_2.id, product_id: milk.id, inventory_id: inventory_milk.id, price: 3131)

              inventory_coffee_powder = create(:inventory, location_id: franchise_branch_1.id, product_id: coffee_powder.id, resource: coffee_powder, resource_line: coffee_powder)
              card_coffee_powder = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: coffee_powder.id, inventory_id: inventory_coffee_powder.id, price: 4111)

              inventory_water = create(:inventory, location_id: franchise_branch_1.id, product_id: water.id, resource: water, resource_line: water)
              card_water = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: water.id, inventory_id: inventory_water.id, price: 1000)

              inventory_coffee_powder = create(:inventory, location_id: franchise_branch_2.id, product_id: coffee_powder.id, resource: coffee_powder, resource_line: coffee_powder)
              card_coffee_powder = create(:inventory_purchase_card, location_id: franchise_branch_2.id, product_id: coffee_powder.id, inventory_id: inventory_coffee_powder.id, price: 4222)

              inventory_water = create(:inventory, location_id: franchise_branch_2.id, product_id: water.id, resource: water, resource_line: water)
              card_water = create(:inventory_purchase_card, location_id: franchise_branch_2.id, product_id: water.id, inventory_id: inventory_water.id, price: 777)

              InventoryPurchaseCard.update_all(stock_date: '9/2/2023'.to_date)

              submit_request(example.metadata)
            end

            after do
              Flipper.disable(:inventory_v2)
            end

            context 'when per 50 locations' do
              it 'should return N/A for product that dont have real cost in one of selected locations' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result).to eq(
                  {"reports"=>
                    [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                      {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                      {"text"=>"Food", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"13,42%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"400",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"4.766,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"92,26%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"20.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"23.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"59,74%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"20%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,84%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"2.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"8.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"80,65%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                      {"text"=>nil, "weight"=>500, "colspan"=>1},
                      {"text"=>"", "weight"=>500, "colspan"=>1},
                      {"text"=>"10",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"35.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"38.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                    {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                  "report_headers"=>
                    [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Sales",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Discount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Surcharge",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Net Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Of Sales",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Cost",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Profit",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Margin",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1}]}
                )
              end
            end

            context 'when per 1 locations' do
              before do
                allow(Restaurant::Services::Report::SalesBy::EstimateCostQuerySalesByProducts).to receive(:location_partition_count).and_return(1)
              end

              it 'should return N/A for product that dont have real cost in one of selected locations' do |example|
                result = JSON.parse(response.body)
                expect(result).to eq(
                  {"reports"=>
                    [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                      {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                      {"text"=>"Food", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"13,42%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"400",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"4.766,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"92,26%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"20.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"23.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"59,74%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"20%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,84%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"2.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"8.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"80,65%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                      {"text"=>nil, "weight"=>500, "colspan"=>1},
                      {"text"=>"", "weight"=>500, "colspan"=>1},
                      {"text"=>"10",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"35.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"3.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"38.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                    {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                  "report_headers"=>
                    [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Sales",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Discount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Surcharge",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Net Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Of Sales",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Cost",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Profit",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Margin",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1}]}
                )
              end
            end
          end

          context 'when option sets used in sales' do
            let(:option_set_option_raw_egg) do
              build(:option_set_option, product: raw_egg, product_unit: raw_egg.product_unit)
            end

            let(:option_set_option_cooking_oil) do
              build(:option_set_option, product: cooking_oil, product_unit: cooking_oil.product_unit)
            end

            let(:option_set_raw_egg) do
              create(:option_set, name: 'Raw Egg Option Set', brand: brand, rule_cost_included_in_parent: true, option_set_options: [option_set_option_raw_egg])
            end

            let(:meta_option_set_raw_egg) do
              {
                is_option_set: true,
                option_set_id: option_set_raw_egg.id,
                option_set_option_id: option_set_option_raw_egg.id,
              }
            end

            let(:sale_detail_modifier_raw_egg_1) do
              build(:sale_detail_modifier, product_id: raw_egg.id, quantity: 4, product_unit_id: raw_egg.product_unit.id, rule_cost_included_in_parent: true, meta: meta_option_set_raw_egg)
            end

            let(:sale_detail_modifier_raw_egg_2) do
              build(:sale_detail_modifier, product_id: raw_egg.id, quantity: 4, product_unit_id: raw_egg.product_unit.id, rule_cost_included_in_parent: true, meta: meta_option_set_raw_egg)
            end

            let(:sale_detail_transaction_fried_rice) do
              sale_detail_transaction = build(:sale_detail_transaction, product_id: fried_rice.id, product_unit_id: fried_rice.product_unit.id, quantity: 2,
                                                                        total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
              sale_detail_transaction.sale_detail_modifiers << sale_detail_modifier_raw_egg_1
              sale_detail_transaction
            end

            let(:sale_detail_transaction_omelet) do
              sale_detail_transaction = build(:sale_detail_transaction, product_id: omelet.id, product_unit_id: omelet.product_unit.id, quantity: 2,
                                                                        total_amount_prorate_discount: 10_000, created_at: Time.zone.now)
              sale_detail_transaction.sale_detail_modifiers << sale_detail_modifier_raw_egg_2
              sale_detail_transaction
            end

            let(:sale_transaction_with_raw_egg) do
              sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                          sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                          cashier_employee_id: owner.id, order_type_id: order_type.id)
              sale_transaction.payments << payment
              sale_transaction.sale_detail_transactions = [sale_detail_transaction_omelet, sale_detail_transaction_fried_rice]

              sale_transaction.save
              sale_transaction
            end

            context 'when sale using same product as sale modifier with rule_cost_included_in_parent false in 2 different sale detail' do
              before do |example|
                Location.search_index.refresh

                option_set_raw_egg
                sale_transaction_with_raw_egg
                SaleTransaction.search_index.refresh

                inventory_raw_egg = create(:inventory, location_id: franchise_branch_1.id, product_id: raw_egg.id, resource: raw_egg, resource_line: raw_egg)
                card_raw_egg = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: raw_egg.id, inventory_id: inventory_raw_egg.id, price: 2400)

                inventory_fried_rice = create(:inventory, location_id: franchise_branch_1.id, product_id: fried_rice.id, resource: fried_rice, resource_line: fried_rice)
                card_fried_rice = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: fried_rice.id, inventory_id: inventory_fried_rice.id, price: 14000)

                brand.report_setting.update!(use_estimate_cost: true)

                sale_detail_modifier_raw_egg_1.update_columns(rule_cost_included_in_parent: false)
                sale_detail_modifier_raw_egg_2.update_columns(rule_cost_included_in_parent: false)
                submit_request(example.metadata)
              end

              it 'should return modifiers' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result).to eq(
                  {"reports"=>
                  [[{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                    {"text"=>raw_egg.sku, "weight"=>500, "colspan"=>1},
                    {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                    {"text"=>"8",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"40%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"19.200",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"-19.200",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                    {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                    {"text"=>"Food", "weight"=>500, "colspan"=>1},
                    {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"20%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"9,14%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Fried Rice", "weight"=>500, "colspan"=>1},
                    {"text"=>"fried_rice", "weight"=>500, "colspan"=>1},
                    {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                    {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"10%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"26,55%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"28.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"-13.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"-86,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                    {"text"=>"latte", "weight"=>500, "colspan"=>1},
                    {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                    {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"10%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"18,29%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                    {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                    {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                    {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"10%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"26,55%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                    {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                    {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                    {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"10%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"1.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"11.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"19,47%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                    {"text"=>nil, "weight"=>500, "colspan"=>1},
                    {"text"=>"", "weight"=>500, "colspan"=>1},
                    {"text"=>"20",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                    {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"55.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"1.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"56.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                    {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                  {"current_page"=>1,
                    "total_item"=>6,
                    "next_page"=>
                    "http://www.example.com/api/report/sales_by?end_date=24%2F02%2F2023&group_by=product&item_per_page=5&location_group_ids=&location_ids=2&page=2&sort_by=qty&start_date=25%2F01%2F2023",
                    "prev_page"=>nil},
                  "report_headers"=>
                  [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Refund Qty",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"% Sold Qty",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Gross Sales",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Refund Amount",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Discount",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Surcharge",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Net Amount",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"% Of Sales",
                    "opacity"=>50,
                    "weight"=>400,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Cost",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"Gross Profit",
                    "alignment"=>"right",
                    "opacity"=>0.8,
                    "size"=>12,
                    "colspan"=>1},
                    {"text"=>"% Margin",
                    "opacity"=>50,
                    "weight"=>400,
                    "size"=>12,
                    "colspan"=>1}]}
                )
              end
            end

            context 'when sale using same product as sale modifier with rule_cost_included_in_parent false in 2 different sale detail' do
              before do |example|
                Location.search_index.refresh

                option_set_raw_egg
                sale_transaction_with_raw_egg
                SaleTransaction.search_index.refresh

                inventory_raw_egg = create(:inventory, location_id: franchise_branch_1.id, product_id: raw_egg.id, resource: raw_egg, resource_line: raw_egg)
                card_raw_egg = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: raw_egg.id, inventory_id: inventory_raw_egg.id, price: 2400)

                inventory_fried_rice = create(:inventory, location_id: franchise_branch_1.id, product_id: fried_rice.id, resource: fried_rice, resource_line: fried_rice)
                card_fried_rice = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: fried_rice.id, inventory_id: inventory_fried_rice.id, price: 14000)

                inventory_omelet = create(:inventory, location_id: franchise_branch_1.id, product_id: omelet.id, resource: omelet, resource_line: omelet)
                card_omelet = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: omelet.id, inventory_id: inventory_omelet.id, price: 7000)

                brand.report_setting.update!(use_estimate_cost: true)

                sale_detail_modifier_raw_egg_1.update_columns(rule_cost_included_in_parent: true)
                sale_detail_modifier_raw_egg_2.update_columns(rule_cost_included_in_parent: true)
                submit_request(example.metadata)
              end

              it 'should return modifiers but exclude modifiers follow parent from the grand total' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                # NOTE: Explanation about expected results
                # sale_transaction_with_raw_egg: 2 Omelet and 2 Fried Rice
                #   2 Omelet has total ingredients of 4 Raw Egg = (2x7000) + (4x2400) = 23600
                #   2 Fried Rice has total ingredients of 4 Raw Egg = (2x14000) + (4x2400) = 37600
                # sale_transaction_9: 2 Latte with modifiers (not follow parent) of 4 Emping = (2xN/A) + (4xN/A) = N/A
                # sale_transaction_10: 2 Spicy Burger = (2xN/A) = N/A
                # sale_transaction_11: 2 Spicy Burger = (2xN/A) = N/A, but this sale won't be included since the date range is outside filtered
                expect(result).to eq(
                  {"reports"=>
                    [[{"text"=>"Emping Modifier", "weight"=>500, "colspan"=>1},
                      {"text"=>"sku_emping", "weight"=>500, "colspan"=>1},
                      {"text"=>"Food", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"33,33%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"5.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"5.166,67",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"9,14%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Fried Rice", "weight"=>500, "colspan"=>1},
                      {"text"=>"fried_rice", "weight"=>500, "colspan"=>1},
                      {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"16,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,55%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"37.600",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"-22.600",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"-150,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                      {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                      {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"latte", "weight"=>500, "colspan"=>1},
                      {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"16,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"10.333,33",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"18,29%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                      {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                      {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"16,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"15.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"26,55%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"23.600",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"-8.600",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"-57,33%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                      {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                      {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                      {"text"=>"4",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"Spicy Burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"spicy_burger", "weight"=>500, "colspan"=>1},
                      {"text"=>"Burgers", "weight"=>500, "colspan"=>1},
                      {"text"=>"2",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"16,67%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"10.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"1.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"11.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"19,47%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}],
                    [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                      {"text"=>nil, "weight"=>500, "colspan"=>1},
                      {"text"=>"", "weight"=>500, "colspan"=>1},
                      {"text"=>"12",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"0",
                      "alignment"=>"center",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"integer"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"55.000",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"0",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"1.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"56.500",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"100%",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "weight"=>500,
                      "colspan"=>1,
                      "cell_format"=>"money"},
                      {"text"=>"N/A",
                      "alignment"=>"right",
                      "opacity"=>50,
                      "weight"=>400,
                      "colspan"=>1,
                      "cell_format"=>"percentage"}]],
                  "paging"=>
                    {"current_page"=>1,
                    "total_item"=>7,
                    "next_page"=>
                      "http://www.example.com/api/report/sales_by?end_date=24%2F02%2F2023&group_by=product&item_per_page=5&location_group_ids=&location_ids=2&page=2&sort_by=qty&start_date=25%2F01%2F2023",
                    "prev_page"=>nil},
                  "report_headers"=>
                    [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                    {"text"=>"SKU", "size"=>12, "colspan"=>1},
                    {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                    {"text"=>"Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Sold Qty",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Sales",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Refund Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Discount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Surcharge",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Net Amount",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Of Sales",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Cost",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"Gross Profit",
                      "alignment"=>"right",
                      "opacity"=>0.8,
                      "size"=>12,
                      "colspan"=>1},
                    {"text"=>"% Margin",
                      "opacity"=>50,
                      "weight"=>400,
                      "size"=>12,
                      "colspan"=>1}]}
                )
              end
            end

            context 'when has no costing' do
              let(:option_set_raw_egg_and_cooking_oil) do
                create(:option_set, name: 'Raw Egg and Cooking oil Option Set', brand: brand, rule_cost_included_in_parent: true, option_set_options: [option_set_option_raw_egg, option_set_option_cooking_oil])
              end

              let(:product_option_set_omelet) do
                create(:product_option_set, product: omelet, option_set_id: option_set_raw_egg_and_cooking_oil.id)
              end

              def sale_detail_modifier_raw_egg_from_option_set_omelet
                build(:sale_detail_modifier, product_id: raw_egg.id, quantity: 4, product_unit_id: raw_egg.product_unit.id, rule_cost_included_in_parent: true, meta: {
                  is_option_set: true,
                  option_set_id: option_set_raw_egg_and_cooking_oil.id,
                  option_set_option_id: option_set_option_raw_egg.id,
                })
              end

              def sale_detail_modifier_cooking_oil_from_option_set_omelet
                # POS can set custom option_set_quantity
                build(:sale_detail_modifier, product_id: cooking_oil.id, quantity: 1, option_set_quantity: 4, product_unit_id: cooking_oil.product_unit.id, rule_cost_included_in_parent: true, meta: {
                  is_option_set: true,
                  option_set_id: option_set_raw_egg_and_cooking_oil.id,
                  option_set_option_id: option_set_option_cooking_oil.id,
                })
              end

              let(:sale_transaction_with_raw_egg_and_cooking_oil) do
                sale_detail_transaction = build(:sale_detail_transaction, product_id: omelet.id, product_unit_id: omelet.product_unit.id, quantity: 2)
                sale_detail_transaction.sale_detail_modifiers << [sale_detail_modifier_raw_egg_from_option_set_omelet, sale_detail_modifier_cooking_oil_from_option_set_omelet]

                sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                            sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                            cashier_employee_id: owner.id, order_type_id: order_type.id)
                sale_transaction.payments << payment
                sale_transaction.sale_detail_transactions = [sale_detail_transaction]

                sale_transaction.save
                sale_transaction
              end

              let(:sale_transaction_with_raw_egg_and_cooking_oil_2) do
                sale_detail_transaction = build(:sale_detail_transaction, product_id: omelet.id, product_unit_id: omelet.product_unit.id, quantity: 2)
                sale_detail_transaction.sale_detail_modifiers << [sale_detail_modifier_raw_egg_from_option_set_omelet, sale_detail_modifier_cooking_oil_from_option_set_omelet]

                sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                            sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                            cashier_employee_id: owner.id, order_type_id: order_type.id)
                sale_transaction.payments << payment
                sale_transaction.sale_detail_transactions = [sale_detail_transaction]

                sale_transaction.save
                sale_transaction
              end

              let(:sale_transaction_with_raw_egg_and_cooking_oil_directly) do
                sale_detail_transaction_raw_egg = build(:sale_detail_transaction, product_id: raw_egg.id, product_unit_id: raw_egg.product_unit.id, quantity: 2)
                sale_detail_transaction_cooking_oil = build(:sale_detail_transaction, product_id: cooking_oil.id, product_unit_id: cooking_oil.product_unit.id, quantity: 1)
                sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_1.id,
                                                            sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                            cashier_employee_id: owner.id, order_type_id: order_type.id)
                sale_transaction.payments << payment
                sale_transaction.sale_detail_transactions = [sale_detail_transaction_raw_egg, sale_detail_transaction_cooking_oil]

                sale_transaction.save
                sale_transaction
              end

              let(:sale_transaction_with_raw_egg_and_cooking_oil_directly_franchise_branch_2) do
                sale_detail_transaction_raw_egg = build(:sale_detail_transaction, product_id: raw_egg.id, product_unit_id: raw_egg.product_unit.id, quantity: 2)
                sale_detail_transaction_cooking_oil = build(:sale_detail_transaction, product_id: cooking_oil.id, product_unit_id: cooking_oil.product_unit.id, quantity: 1)
                sale_transaction = build(:sale_transaction, sales_time: Time.now - 1.day, brand_id: brand.id, location_id: franchise_branch_2.id,
                                                            sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                            cashier_employee_id: owner.id, order_type_id: order_type.id)
                sale_transaction.payments << payment
                sale_transaction.sale_detail_transactions = [sale_detail_transaction_raw_egg, sale_detail_transaction_cooking_oil]

                sale_transaction.save
                sale_transaction
              end

              let(:all_inventory_purchase_cards_franchise_branch_1) do
                inventory_raw_egg = create(:inventory, location_id: franchise_branch_1.id, product_id: raw_egg.id, resource: raw_egg, resource_line: raw_egg)
                card_raw_egg = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: raw_egg.id, inventory_id: inventory_raw_egg.id, price: 240)

                inventory_cooking_oil = create(:inventory, location_id: franchise_branch_1.id, product_id: cooking_oil.id, resource: cooking_oil, resource_line: cooking_oil)
                card_cooking_oil = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: cooking_oil.id, inventory_id: inventory_cooking_oil.id, price: 300)

                inventory_fried_rice = create(:inventory, location_id: franchise_branch_1.id, product_id: fried_rice.id, resource: fried_rice, resource_line: fried_rice)
                card_fried_rice = create(:inventory_purchase_card, location_id: franchise_branch_1.id, product_id: fried_rice.id, inventory_id: inventory_fried_rice.id, price: 1400)
              end

              let(:all_inventory_purchase_cards_franchise_branch_2) do
                inventory_raw_egg = create(:inventory, location_id: franchise_branch_2.id, product_id: raw_egg.id, resource: raw_egg, resource_line: raw_egg)
                card_raw_egg = create(:inventory_purchase_card, location_id: franchise_branch_2.id, product_id: raw_egg.id, inventory_id: inventory_raw_egg.id, price: 240)

                inventory_cooking_oil = create(:inventory, location_id: franchise_branch_2.id, product_id: cooking_oil.id, resource: cooking_oil, resource_line: cooking_oil)
                card_cooking_oil = create(:inventory_purchase_card, location_id: franchise_branch_2.id, product_id: cooking_oil.id, inventory_id: inventory_cooking_oil.id, price: 300)
              end

              context 'when option set rule_cost_included_in_parent is truthy, then create sale using product with option set' do
                let(:is_select_all_product) { 'false' }
                let(:product_ids) { omelet.id.to_s }
                before do |example|
                  Location.search_index.refresh

                  product_option_set_omelet
                  sale_transaction_with_raw_egg_and_cooking_oil
                  SaleTransaction.search_index.refresh

                  all_inventory_purchase_cards_franchise_branch_1
                  brand.report_setting.update!(use_estimate_cost: true)

                  submit_request(example.metadata)
                end

                context 'when group_by product' do
                  let(:group_by) { 'product' }

                  it 'should be able to show estimate cost from sale modifiers' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)
                    # NOTE: Explanation about expected results
                    # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                    expect(result).to eq(
                      {"reports"=>
                        [[{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                          {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"2",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"2.160",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"17.840",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"89,2%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                          {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"4",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"4",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                          {"text"=>nil, "weight"=>500, "colspan"=>1},
                          {"text"=>"", "weight"=>500, "colspan"=>1},
                          {"text"=>"2",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"2.160",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"17.840",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"89,2%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}]],
                      "paging"=>
                        {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                      "report_headers"=>
                        [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                        {"text"=>"SKU", "size"=>12, "colspan"=>1},
                        {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                        {"text"=>"Sold Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Refund Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Sold Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Gross Sales",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Refund Amount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Discount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Surcharge",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Net Amount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Of Sales",
                          "opacity"=>50,
                          "weight"=>400,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Cost",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Gross Profit",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Margin",
                          "opacity"=>50,
                          "weight"=>400,
                          "size"=>12,
                          "colspan"=>1}]}
                    )
                  end
                end

                context 'when group_by product category' do
                  let(:group_by) { 'product_category' }

                  it 'should be able to show estimate cost from sale modifiers and hide modifiers follow parent from reports' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)
                    # NOTE: 2 omelet has total ingredients: (4 raw egg @240 + 4 cooking oil @300) = 2160
                    expect(result).to eq(
                      {"reports"=>
                      [[{"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                        {"text"=>"2",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"0",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"2.160",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"17.840",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"89,2%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"}],
                      [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                        {"text"=>"2",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"0",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"2.160",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"17.840",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"89,2%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"}]],
                    "paging"=>
                      {"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil},
                    "report_headers"=>
                      [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                      {"text"=>"Sold Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Refund Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Sold Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Gross Sales",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Refund Amount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Discount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Surcharge",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Net Amount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Of Sales",
                        "opacity"=>50,
                        "weight"=>400,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Cost",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Gross Profit",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Margin",
                        "opacity"=>50,
                        "weight"=>400,
                        "size"=>12,
                        "colspan"=>1}]}
                    )
                  end
                end

                context 'when group_by product category group' do
                  let(:group_by) { 'category_group' }

                  it 'should be able to show estimate cost from sale modifiers and hide modifiers follow parent from reports' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)
                    # NOTE: 2 omelet has total ingredients: (4 raw egg @240 + 4 cooking oil @300) = 2160
                    expect(result).to eq(
                      {"reports"=>
                      [[{"text"=>"No Category Group", "weight"=>500, "colspan"=>1},
                        {"text"=>"2",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"0",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"2.160",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"17.840",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"89,2%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"}],
                      [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                        {"text"=>"2",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"0",
                        "alignment"=>"center",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"integer"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"0",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"20.000",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"100%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"},
                        {"text"=>"2.160",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"17.840",
                        "alignment"=>"right",
                        "weight"=>500,
                        "colspan"=>1,
                        "cell_format"=>"money"},
                        {"text"=>"89,2%",
                        "alignment"=>"right",
                        "opacity"=>50,
                        "weight"=>400,
                        "colspan"=>1,
                        "cell_format"=>"percentage"}]],
                    "paging"=>
                      {"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil},
                    "report_headers"=>
                      [{"text"=>"Product Category Group Name", "size"=>12, "colspan"=>1},
                      {"text"=>"Sold Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Refund Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Sold Qty",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Gross Sales",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Refund Amount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Discount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Surcharge",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Net Amount",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Of Sales",
                        "opacity"=>50,
                        "weight"=>400,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Cost",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"Gross Profit",
                        "alignment"=>"right",
                        "opacity"=>0.8,
                        "size"=>12,
                        "colspan"=>1},
                      {"text"=>"% Margin",
                        "opacity"=>50,
                        "weight"=>400,
                        "size"=>12,
                        "colspan"=>1}]}
                    )
                  end
                end
              end

              context 'when option set rule_cost_included_in_parent is falsey then create sale then change rule_cost_included_in_parent to truthy then create another sale' do
                let(:is_select_all_product) { 'false' }
                let(:product_ids) { [omelet.id, raw_egg.id, cooking_oil.id] }

                before do
                  replicate_data_to_clickhouse!
                  option_set_raw_egg_and_cooking_oil.update(rule_cost_included_in_parent: false)
                  Location.search_index.refresh

                  product_option_set_omelet
                  sale_transaction_with_raw_egg_and_cooking_oil_directly
                  sale_transaction_with_raw_egg_and_cooking_oil
                  replicate_data_to_clickhouse!
                  SaleTransaction.search_index.refresh

                  option_set_raw_egg_and_cooking_oil.update(rule_cost_included_in_parent: true)

                  all_inventory_purchase_cards_franchise_branch_1
                  brand.report_setting.update!(use_estimate_cost: true)
                end

                context 'when group_by product' do
                  let(:group_by) { 'product' }

                  context 'when has no product groups' do
                    context 'when single location' do
                      before do |example|
                        submit_request(example.metadata)
                      end

                      it 'should be able to show estimate cost from sale modifiers and sale details separately, but will not count modifiers follow parent to the total count' do |example|
                        assert_response_matches_metadata(example.metadata)
                        result = JSON.parse(response.body)
                        # NOTE: To make sure no more regression in sale detail modifiers when user toggle option set rule_cost_included_in_parent
                        expect(sale_transaction_with_raw_egg_and_cooking_oil.sale_detail_modifiers.map{|x| [x.product.name, x.sale_product_ids, x.product_id, x.should_rule_cost_included_in_parent?]}).to eq(
                          [[raw_egg.name, [raw_egg.id, omelet.id], raw_egg.id, true], [cooking_oil.name, [cooking_oil.id, omelet.id], cooking_oil.id, true]]
                        )
                        expect(sale_transaction_with_raw_egg_and_cooking_oil_directly.sale_detail_transactions.map{|x| [x.product.name, x.sale_product_ids, x.product_id]}).to eq(
                          [[raw_egg.name, [raw_egg.id], raw_egg.id], [cooking_oil.name, [cooking_oil.id], cooking_oil.id]]
                        )

                        # NOTE: Explanation about expected results
                        # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                        # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                        # Total cost of both sales should be 2160 + 480 + 300 = 2940
                        expect(result).to eq(
                          {"reports"=>
                            [[{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                              {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"2",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"40%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"50%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"2.160",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"17.840",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"89,2%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                              {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"4",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"4",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"2",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"40%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"10.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"10.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"25%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"480",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"9.520",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"95,2%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"Cooking Oil", "weight"=>500, "colspan"=>1},
                              {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"1",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"20%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"10.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"10.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"25%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"300",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"9.700",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"97%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                              {"text"=>nil, "weight"=>500, "colspan"=>1},
                              {"text"=>"", "weight"=>500, "colspan"=>1},
                              {"text"=>"5",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"100%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"40.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"40.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"100%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"2.940",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"37.060",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"92,65%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}]],
                          "paging"=>
                            {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                          "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"SKU", "size"=>12, "colspan"=>1},
                          {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                        )
                      end
                    end

                    context 'when multiple locations' do
                      let(:location_ids) { [franchise_branch_1.id, franchise_branch_2.id].join(',') }

                      before do |example|
                        all_inventory_purchase_cards_franchise_branch_2
                        sale_transaction_with_raw_egg_and_cooking_oil_directly_franchise_branch_2
                        Location.search_index.refresh
                        replicate_data_to_clickhouse!
                        submit_request(example.metadata)
                      end

                      it 'should be able to show estimate cost from sale modifiers and sale details separately, but will not count modifiers follow parent to the total count' do |example|
                        assert_response_matches_metadata(example.metadata)
                        result = JSON.parse(response.body)
                        # NOTE: To make sure no more regression in sale detail modifiers when user toggle option set rule_cost_included_in_parent
                        expect(sale_transaction_with_raw_egg_and_cooking_oil.sale_detail_modifiers.map{|x| [x.product.name, x.sale_product_ids, x.product_id, x.should_rule_cost_included_in_parent?]}).to eq(
                          [[raw_egg.name, [raw_egg.id, omelet.id], raw_egg.id, true], [cooking_oil.name, [cooking_oil.id, omelet.id], cooking_oil.id, true]]
                        )
                        expect(sale_transaction_with_raw_egg_and_cooking_oil_directly.sale_detail_transactions.map{|x| [x.product.name, x.sale_product_ids, x.product_id]}).to eq(
                          [[raw_egg.name, [raw_egg.id], raw_egg.id], [cooking_oil.name, [cooking_oil.id], cooking_oil.id]]
                        )
                        expect(sale_transaction_with_raw_egg_and_cooking_oil_directly_franchise_branch_2.sale_detail_transactions.map{|x| [x.product.name, x.sale_product_ids, x.product_id]}).to eq(
                          [[raw_egg.name, [raw_egg.id], raw_egg.id], [cooking_oil.name, [cooking_oil.id], cooking_oil.id]]
                        )

                        # NOTE: Explanation about expected results
                        # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                        # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                        # sale_transaction_with_raw_egg_and_cooking_oil_directly_franchise_branch_2: Exactly the same as sale_transaction_with_raw_egg_and_cooking_oil_directly
                        # Total cost of both sales should be 2160 + (480 + 300) + (480 + 300) = 3720
                        # List of products should be:
                        # 2 Omelet (independent) and modifiers: 4 Raw Egg + 4 Cooking Oil
                        # 4 Raw Egg (independent), 2 Cooking Oil (independent)
                        expect(result).to eq(
                          {"reports"=>
                            [[{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"4",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"50%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"33,33%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"960",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"19.040",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"95,2%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"Cooking Oil", "weight"=>500, "colspan"=>1},
                              {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"2",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"25%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"33,33%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"600",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"19.400",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"97%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                              {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"2",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"25%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"20.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"33,33%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"2.160",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"17.840",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"89,2%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                              {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"4",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                              {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                              {"text"=>"4",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}],
                            [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                              {"text"=>nil, "weight"=>500, "colspan"=>1},
                              {"text"=>"", "weight"=>500, "colspan"=>1},
                              {"text"=>"8",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"0",
                              "alignment"=>"center",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"integer"},
                              {"text"=>"100%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"60.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"0",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"60.000",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"100%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"},
                              {"text"=>"3.720",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"56.280",
                              "alignment"=>"right",
                              "weight"=>500,
                              "colspan"=>1,
                              "cell_format"=>"money"},
                              {"text"=>"93,8%",
                              "alignment"=>"right",
                              "opacity"=>50,
                              "weight"=>400,
                              "colspan"=>1,
                              "cell_format"=>"percentage"}]],
                          "paging"=>
                            {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                          "report_headers"=>
                            [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                            {"text"=>"SKU", "size"=>12, "colspan"=>1},
                            {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                            {"text"=>"Sold Qty",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Refund Qty",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"% Sold Qty",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Gross Sales",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Refund Amount",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Discount",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Surcharge",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Net Amount",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"% Of Sales",
                              "opacity"=>50,
                              "weight"=>400,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Cost",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"Gross Profit",
                              "alignment"=>"right",
                              "opacity"=>0.8,
                              "size"=>12,
                              "colspan"=>1},
                            {"text"=>"% Margin",
                              "opacity"=>50,
                              "weight"=>400,
                              "size"=>12,
                              "colspan"=>1}]}
                        )
                      end
                    end
                  end

                  context 'when has product groups' do
                    let(:is_select_all_product_group) { 'false' }
                    let(:product_group_ids) { [dinner_product_group.id, sweet_product_group.id].join(',') }

                    before do |example|
                      sweet_product_group.products << raw_egg
                      dinner_product_group.products << omelet
                      sweet_product_group.save!
                      dinner_product_group.save!

                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately, but will not count modifiers follow parent to the total count' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)
                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Dinner",
                            "weight"=>500,
                            "colspan"=>15,
                            "component_class"=>"TableHeaderGroup"}],
                          [{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.160",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.840",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                            {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"4",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"4",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Total Dinner", "weight"=>500, "colspan"=>1},
                            {"text"=>nil, "weight"=>500, "colspan"=>1},
                            {"text"=>"", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.160",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.840",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Sweet",
                            "weight"=>500,
                            "colspan"=>15,
                            "component_class"=>"TableHeaderGroup"}],
                          [{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Total Sweet", "weight"=>500, "colspan"=>1},
                            {"text"=>nil, "weight"=>500, "colspan"=>1},
                            {"text"=>"", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL",
                            "weight"=>500,
                            "colspan"=>1,
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>nil,
                            "weight"=>500,
                            "colspan"=>1,
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"",
                            "weight"=>500,
                            "colspan"=>1,
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money",
                            "background_color"=>"contentNeutralSecondary"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage",
                            "background_color"=>"contentNeutralSecondary"}],
                          [{"text"=>
                              "A product may appear multiple times if it belongs to more than one product group.",
                            "colspan"=>15}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"SKU", "size"=>12, "colspan"=>1},
                          {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when modifiers product dont have stock and price set thus has no inventory purchase cards' do
                    before do |example|
                      InventoryPurchaseCard.where(product_id: raw_egg.id).destroy_all
                      InventoryPurchaseCard.where(product_id: cooking_oil.id).destroy_all
                      raw_egg.update!(no_stock: true)
                      cooking_oil.update!(no_stock: true)
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately, but will not count modifiers follow parent to the total count' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)
                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (0*4 Raw Egg)+(0*4 Cooking Oil) = 0
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2xN/A) = N/A and Cooking Oil (independent) as (1xN/A) = N/A
                      # Total cost of both sales should be 0 + N/A + N/A = N/A
                      expect(result).to eq(
                        {"reports"=>
                        [[{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                          {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"2",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"40%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"50%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"20.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                          {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"4",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"4",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"2",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"40%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"10.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"10.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"25%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"Cooking Oil", "weight"=>500, "colspan"=>1},
                          {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                          {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                          {"text"=>"1",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"20%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"10.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"10.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"25%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}],
                        [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                          {"text"=>nil, "weight"=>500, "colspan"=>1},
                          {"text"=>"", "weight"=>500, "colspan"=>1},
                          {"text"=>"5",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"0",
                          "alignment"=>"center",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"integer"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"40.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"0",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"40.000",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"100%",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "weight"=>500,
                          "colspan"=>1,
                          "cell_format"=>"money"},
                          {"text"=>"N/A",
                          "alignment"=>"right",
                          "opacity"=>50,
                          "weight"=>400,
                          "colspan"=>1,
                          "cell_format"=>"percentage"}]],
                      "paging"=>
                        {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                      "report_headers"=>
                        [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                        {"text"=>"SKU", "size"=>12, "colspan"=>1},
                        {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                        {"text"=>"Sold Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Refund Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Sold Qty",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Gross Sales",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Refund Amount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Discount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Surcharge",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Net Amount",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Of Sales",
                          "opacity"=>50,
                          "weight"=>400,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Cost",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"Gross Profit",
                          "alignment"=>"right",
                          "opacity"=>0.8,
                          "size"=>12,
                          "colspan"=>1},
                        {"text"=>"% Margin",
                          "opacity"=>50,
                          "weight"=>400,
                          "size"=>12,
                          "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when parent product has stock in with price 0' do
                    let(:stock_in_omelet_price_zero) do
                      stock_io = build(
                        :stock_in,
                        location: franchise_branch_1,
                        brand: brand,
                        stock_type: 'stock_in',
                        stock_date: sale_transaction_with_raw_egg_and_cooking_oil.sales_time.to_date
                      )

                      stock_io.stock_in_or_out_lines << build(:stock_in_or_out_line, :stock_out, product: omelet, product_unit: omelet.product_unit, cost_per_unit: 100)
                      stock_io.save!
                      InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                      stock_io
                    end

                    before do |example|
                      stock_in_omelet_price_zero
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers combined with parent cost per unit' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)
                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160 + (100x2 Omelet from stock in cost unit) = 2360
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2360 + 480 + 300 = 3140
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.360",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.640",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"88,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                            {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"4",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"4",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Cooking Oil", "weight"=>500, "colspan"=>1},
                            {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"1",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"20%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"300",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.700",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"97%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>nil, "weight"=>500, "colspan"=>1},
                            {"text"=>"", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"3.140",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"36.860",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,15%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"SKU", "size"=>12, "colspan"=>1},
                          {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when buy the same product with modifiers' do
                    before do |example|
                      sale_transaction_with_raw_egg_and_cooking_oil
                      sale_transaction_with_raw_egg_and_cooking_oil_2

                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it '' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 4 Omelet with total ingredients (240*8 Raw Egg)+(300*8 Cooking Oil) = 4320
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 4320 + 480 + 300 = 5100
                      # List of products should be:
                      # 4 Omelet (independent) and modifiers: 8 Raw Egg + 8 Cooking Oil
                      # 2 Raw Egg (independent), 1 Cooking Oil (independent)
                      expect(
                        {"reports"=>
                          [[{"text"=>"Omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"omelet", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"4",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"57,14%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"66,67%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"4.320",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"35.680",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Cooking Oil", "weight"=>500, "colspan"=>1},
                            {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"8",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"    Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"8",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Raw Egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"raw_egg", "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"28,57%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"16,67%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Cooking Oil", "weight"=>500, "colspan"=>1},
                            {"text"=>cooking_oil.sku, "weight"=>500, "colspan"=>1},
                            {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"1",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"14,29%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"16,67%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"300",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.700",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"97%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>nil, "weight"=>500, "colspan"=>1},
                            {"text"=>"", "weight"=>500, "colspan"=>1},
                            {"text"=>"7",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"60.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"60.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"5.100",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"54.900",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"91,5%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>5, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"SKU", "size"=>12, "colspan"=>1},
                          {"text"=>"Category Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end
                end

                context 'when group_by product category' do
                  let(:group_by) { 'product_category' }

                  context 'when all product category are "Uncategorized"' do
                    before do |example|
                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when all product category are different' do
                    before do |example|
                      raw_egg.update_columns(product_category_id: rice_category.id)
                      cooking_oil.update_columns(product_category_id: modifier_category.id)
                      omelet.update_columns(product_category_id: food_category.id)

                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Food", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.160",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.840",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Rice", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Modifier", "weight"=>500, "colspan"=>1},
                            {"text"=>"1",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"20%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"300",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.700",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"97%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when all product category are different and has "Uncategorized"' do
                    before do |example|
                      raw_egg.update_columns(product_category_id: nil)
                      cooking_oil.update_columns(product_category_id: modifier_category.id)
                      omelet.update_columns(product_category_id: food_category.id)

                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Uncategorized", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Food", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.160",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.840",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Modifier", "weight"=>500, "colspan"=>1},
                            {"text"=>"1",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"20%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"300",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.700",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"97%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end
                end

                context 'when group_by product category group' do
                  let(:group_by) { 'category_group' }

                  before do
                    raw_egg.update_columns(product_category_id: rice_category.id)
                    cooking_oil.update_columns(product_category_id: modifier_category.id)
                    omelet.update_columns(product_category_id: food_category.id)
                  end

                  context 'when all product category belongs to "No Category Group"' do
                    before do |example|
                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"No Category Group", "weight"=>500, "colspan"=>1},
                            {"text"=>"3",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"60%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"30.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"30.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"75%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.460",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"27.540",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"91,8%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Lunch", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>2, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Category Group Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end

                  context 'when all product category are different and has "No Category Group"' do
                    before do |example|
                      rice_category.update(product_category_group: category_group_breakfast)
                      modifier_category.update(product_category_group: nil)
                      food_category.update(product_category_group: category_group_lunch)

                      replicate_data_to_clickhouse!
                      submit_request(example.metadata)
                    end

                    it 'should be able to show estimate cost from sale modifiers and sale details separately' do |example|
                      assert_response_matches_metadata(example.metadata)
                      result = JSON.parse(response.body)

                      # NOTE: Explanation about expected results
                      # sale_transaction_with_raw_egg_and_cooking_oil: 2 Omelet with total ingredients (240*4 Raw Egg)+(300*4 Cooking Oil) = 2160
                      # sale_transaction_with_raw_egg_and_cooking_oil_directly: Raw Egg (independent) as (2x240) = 480 and Cooking Oil (independent) as (1x300) = 300
                      # Total cost of both sales should be 2160 + 480 + 300 = 2940
                      expect(result).to eq(
                        {"reports"=>
                          [[{"text"=>"Breakfast", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"480",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.520",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"95,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"Lunch", "weight"=>500, "colspan"=>1},
                            {"text"=>"2",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"40%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"20.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"50%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.160",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"17.840",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"89,2%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"No Category Group", "weight"=>500, "colspan"=>1},
                            {"text"=>"1",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"20%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"10.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"25%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"300",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"9.700",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"97%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}],
                          [{"text"=>"TOTAL", "weight"=>500, "colspan"=>1},
                            {"text"=>"5",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"0",
                            "alignment"=>"center",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"integer"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"0",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"40.000",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"100%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"},
                            {"text"=>"2.940",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"37.060",
                            "alignment"=>"right",
                            "weight"=>500,
                            "colspan"=>1,
                            "cell_format"=>"money"},
                            {"text"=>"92,65%",
                            "alignment"=>"right",
                            "opacity"=>50,
                            "weight"=>400,
                            "colspan"=>1,
                            "cell_format"=>"percentage"}]],
                        "paging"=>
                          {"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil},
                        "report_headers"=>
                          [{"text"=>"Product Category Group Name", "size"=>12, "colspan"=>1},
                          {"text"=>"Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Sold Qty",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Sales",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Refund Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Discount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Surcharge",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Net Amount",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Of Sales",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Cost",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"Gross Profit",
                            "alignment"=>"right",
                            "opacity"=>0.8,
                            "size"=>12,
                            "colspan"=>1},
                          {"text"=>"% Margin",
                            "opacity"=>50,
                            "weight"=>400,
                            "size"=>12,
                            "colspan"=>1}]}
                      )
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  path '/api/report/sales_by.csv' do
    get 'Sales By report' do
      tags 'Restaurant - Sales By Report'
      security [bearerAuth: []]
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :page, in: :query, type: :integer
      parameter name: :group_by, in: :query, type: :string
      parameter name: :item_per_page, in: :query, type: :integer
      parameter name: :start_date, in: :query, type: :string
      parameter name: :end_date, in: :query, type: :string
      parameter name: :daily_sale_id, in: :query, type: :string, required: false
      parameter name: :export_mode, in: :query, type: :string, required: false, enum: ['combined', 'separated']
      parameter name: :separate_export_per_location_group, in: :query, type: :string, required: false
      parameter name: :location_ids, in: :query, type: :string, required: false
      parameter name: :location_group_ids, in: :query, type: :string, required: false

      let(:page) { 1 }
      let(:group_by) { 'product' }
      let(:item_per_page) { 2 }
      let(:start_date) { '18/07/2022' }
      let(:end_date) { '19/07/2022' }
      let(:location_ids) { [owned_branch_3.id].join(',') }
      let(:location_group_ids) { [owned_branch_location_groups.id].join(',') }

      before do
        sale_transaction_9
      end

      context 'when export 1 file' do
        context 'when valid email and confirmed' do
          response '200', 'get sales by' do
            before do |example|
              expect(Restaurant::Jobs::Report::SalesByProductReportJob)
                .to receive(:perform_later)
                .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'csv', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                      'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1' })
              submit_request(example.metadata)
            end

            it 'returns valid report response' do |_example|
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
            end
          end
        end
      end

      context 'when export multiple file' do
        let(:export_mode) { 'separated' }

        context 'when separate per location group' do
          let(:separate_export_per_location_group) { 'true' }
          let(:is_select_all_location) { 'true' }

          context 'when valid email and confirmed' do
            response '200', 'get sales by' do
              before do |example|
                expect(Restaurant::Jobs::Report::SalesByProductReportExportModeSeparatedJob)
                  .to receive(:perform_later)
                  .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'csv', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                        'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1', 'export_mode' => 'separated', 'separate_export_per_location_group' => 'true' })
                submit_request(example.metadata)
              end

              it 'returns valid report response' do |_example|
                response_body = JSON.parse(response.body)
                expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
              end
            end
          end
        end

        context 'when separate per location' do
          context 'when valid email and confirmed' do
            response '200', 'get sales by' do
              before do |example|
                expect(Restaurant::Jobs::Report::SalesByProductReportExportModeSeparatedJob)
                  .to receive(:perform_later)
                  .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'csv', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                        'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1', 'export_mode' => 'separated' })
                submit_request(example.metadata)
              end

              it 'returns valid report response' do |_example|
                response_body = JSON.parse(response.body)
                expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
              end
            end
          end
        end
      end

      include_examples 'unconfirmed email'
    end
  end

  path '/api/report/sales_by.xlsx' do
    get 'Sales By report' do
      tags 'Restaurant - Sales By Report'
      security [bearerAuth: []]
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :page, in: :query, type: :integer
      parameter name: :group_by, in: :query, type: :string
      parameter name: :item_per_page, in: :query, type: :integer
      parameter name: :start_date, in: :query, type: :string
      parameter name: :end_date, in: :query, type: :string
      parameter name: :daily_sale_id, in: :query, type: :string, required: false
      parameter name: :export_mode, in: :query, type: :string, required: false, enum: ['combined', 'separated']
      parameter name: :separate_export_per_location_group, in: :query, type: :string, required: false
      parameter name: :location_ids, in: :query, type: :string, required: false
      parameter name: :location_group_ids, in: :query, type: :string, required: false

      let(:page) { 1 }
      let(:group_by) { 'product' }
      let(:item_per_page) { 2 }
      let(:start_date) { '18/07/2022' }
      let(:end_date) { '19/07/2022' }
      let(:location_ids) { [owned_branch_3.id].join(',') }
      let(:location_group_ids) { [owned_branch_location_groups.id].join(',') }

      context 'when export single file' do
        context 'when valid email and confirmed' do
          response '200', 'get sales by' do
            before do |example|
              expect(Restaurant::Jobs::Report::SalesByProductReportJob)
                .to receive(:perform_later)
                .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'excel', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                        'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1' })
              submit_request(example.metadata)
            end

            it 'returns valid report response' do |_example|
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
            end
          end
        end
      end

      context 'when export multiple file' do
        let(:export_mode) { 'separated' }

        context 'when separate per location group' do
          context 'when valid email and confirmed' do
            let(:separate_export_per_location_group) { 'true' }

            response '200', 'get sales by' do
              before do |example|
                expect(Restaurant::Jobs::Report::SalesByProductReportExportModeSeparatedJob)
                  .to receive(:perform_later)
                  .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'excel', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                          'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1', 'export_mode' => 'separated', 'separate_export_per_location_group' => 'true' })
                submit_request(example.metadata)
              end

              it 'returns valid report response' do |_example|
                response_body = JSON.parse(response.body)
                expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
              end
            end
          end
        end

        context 'when separate per location' do
          context 'when valid email and confirmed' do
            response '200', 'get sales by' do
              before do |example|
                expect(Restaurant::Jobs::Report::SalesByProductReportExportModeSeparatedJob)
                  .to receive(:perform_later)
                  .with(brand_id: brand.id, user_id: owner.id, progress_id: 1, report_format: 'excel', sales_by_params: { 'page' => '1', 'item_per_page' => '2',
                                                                                                          'group_by' => 'product', 'start_date' => '18/07/2022', 'end_date' => '19/07/2022', 'location_ids' => owned_branch_3.id.to_s, 'location_group_ids' => '1', 'export_mode' => 'separated' })
                submit_request(example.metadata)
              end

              it 'returns valid report response' do |_example|
                response_body = JSON.parse(response.body)
                expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
              end
            end
          end
        end
      end

      include_examples 'unconfirmed email'
    end
  end
end
