require './spec/shared/locations'
require './spec/shared/bulk_sale_transactions'
require './spec/shared/reports'
require './spec/shared/swagger'

RSpec.describe 'api/report/sales_payment_methods', type: :request, bullet: :skip, clickhouse: true do
  include_context 'locations creations'
  include_context 'swagger after response'
  include_context 'bulk sale transactions creations'

  before(:all) do
    Location.reindex
  end

  before(:each) do
    @header = authentication_header(owner, app_type: 'restaurant')
    Flipper.enable(:enable_clickhouse)
    Flipper.enable(:enable_clickhouse_report)
  end

  after do
    Flipper.disable(:sale_by_payment_method_use_snapshots)
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:payment_methods_gopay) do
    methods = []

    (0..4).each do |index|
      methods << create(:payment_method, brand: brand, name: "Gopay #{index}")
    end

    methods
  end

  let(:local_bulk_recent_transactions_owned_online_branch_1) do
    sales = bulk_recent_transactions_owned_online_branch_1

    payments = sales.map(&:payments).flatten
    Payment.where(id: payments.map(&:id)).destroy_all

    sales
  end

  let(:local_bulk_recent_transactions_franchise_branch_1) do
    sales = bulk_recent_transactions_franchise_branch_1

    payments = sales.map(&:payments).flatten
    Payment.where(id: payments.map(&:id)).destroy_all

    sales
  end

  path '/api/report/sales_payment_methods', search: true do
    parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
    parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
    parameter name: :location_ids, in: :query, type: :string, required: false
    parameter name: :is_select_all_location, in: :query, type: :string, required: false
    parameter name: :exclude_location_ids, in: :query, type: :string, required: false
    parameter name: :exclude_location_group_ids, in: :query, type: :string, required: false
    parameter name: :location_group_ids, in: :query, type: :string, required: false
    parameter name: :location_group_ids, in: :query, type: :string, required: false
    parameter name: :daily_sale_id, in: :query, type: :string, required: false
    parameter name: :group_by, in: :query, type: :string, required: false, enum: ['location', 'payment_method', 'location_per_day', 'date']
    parameter name: :page, in: :query, type: :string, required: false
    parameter name: :item_per_page, in: :query, type: :string, required: false

    get('list payment methods') do
      tags 'Restaurant - Report - Sales Payment Methods'
      security [bearerAuth: []]
      consumes 'application/json'
      produces 'application/json'
      parameter name: 'Brand-UUID', in: :header, type: :string

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_reports'

        before do
          owned_online_branch_1

          Location.search_index.refresh
        end

        context 'when data is empty' do
          let(:group_by) { 'location' }
          let(:start_date) { 1.months.ago.strftime('%d/%m/%Y') }
          let(:end_date) { Time.now.strftime('%d/%m/%Y') }
          let(:location_ids) { owned_online_branch_1.id }

          before do |example|
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'returns a valid report response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['report_headers'])
              .to eq(
                [[{"text"=>"Location", "weight"=>500, "colspan"=>1, "rowspan"=>2},
                  {"text"=>"Total Amount Received",
                   "alignment"=>"right",
                   "weight"=>500,
                   "colspan"=>1,
                   "rowspan"=>2}]]
              )
            expect(response_body['reports']).to eql([
              [
                {"text"=>"TOTAL", "alignment"=>"left", "weight"=>500, "size"=>14, "colspan"=>1},
                {"text"=>"0", "alignment"=>"right", "weight"=>500, "size"=>14, "colspan"=>1, "cell_format"=>"money"}
              ]
            ])
          end
        end

        context 'when has data and select all location' do
          let(:group_by) { 'location' }
          let(:is_select_all_location) { 'true' }
          let(:location_ids) { "#{owned_online_branch_1.id},#{franchise_branch_1.id}" }
          let(:start_date) { 1.months.ago.strftime('%d/%m/%Y') }
          let(:end_date) { Time.now.strftime('%d/%m/%Y') }

          before do |example|
            Location.reindex

            travel_to Time.utc(2023, 7, 29, 11, 0)
            @header = authentication_header(owner)

            payment_methods_gopay
            local_bulk_recent_transactions_owned_online_branch_1
            local_bulk_recent_transactions_owned_online_branch_1.each_with_index do |sale_transaction, index|
              payment_method = payment_methods_gopay.detect { |pay| pay.name == "Gopay #{index}" && pay.brand = brand }
              create(:payment, payment_method: payment_method, sale_transaction: sale_transaction)
            end

            bulk_recent_transactions_franchise_branch_1.each_with_index do |sale_transaction, index|
              payment_method = payment_methods_gopay.detect { |pay| pay.name == "Gopay #{index}" && pay.brand = brand }
              create(:payment, payment_method: payment_method, sale_transaction: sale_transaction)
              sale_transaction.payments.first.destroy
            end
            payment = bulk_recent_transactions_franchise_branch_1.last.payments.reload.first
            payment.update_columns(is_cash: true)
            from_sale_transaction_create_sales_return(SaleTransaction.last)
          end

          after do
            travel_back
          end


          def expected_headers
            [[{"text"=>"Location", "weight"=>500, "colspan"=>1, "rowspan"=>2},
              {"text"=>"Gopay 0", "alignment"=>"center", "weight"=>500, "colspan"=>5},
              {"text"=>"Gopay 1", "alignment"=>"center", "weight"=>500, "colspan"=>5},
              {"text"=>"Gopay 2", "alignment"=>"center", "weight"=>500, "colspan"=>5},
              {"text"=>"Gopay 3", "alignment"=>"center", "weight"=>500, "colspan"=>5},
              {"text"=>"Gopay 4", "alignment"=>"center", "weight"=>500, "colspan"=>5},
              {"text"=>"Total Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1,
                "rowspan"=>2}],
              [{"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
              {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
              {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1}]]
          end

          def expected_reports(location_name)
            [[{"text"=>location_name,
                "alignment"=>"left",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"-5.240,4",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-15.640,4",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"34.759,6",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"}],
              [{"text"=>"Owned Location Balaraja",
                "alignment"=>"left",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"10.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-10.400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-400",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"50.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"}],
              [{"text"=>"TOTAL",
                "alignment"=>"left",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1},
                {"text"=>"2",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"20.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-20.800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"2",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"20.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-20.800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"2",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"20.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-20.800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"2",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"0",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"20.000",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-20.800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"2",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"1",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"integer"},
                {"text"=>"4.759,6",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-20.800",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"-16.040,4",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"},
                {"text"=>"84.759,6",
                "alignment"=>"right",
                "weight"=>500,
                "size"=>14,
                "colspan"=>1,
                "cell_format"=>"money"}]]
          end

          context "when location name doesn't change" do
            before do |example|
              Location.reindex
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              expect(response_body['report_headers']).to eq(expected_headers)
              expect(response_body['reports']).to eq(expected_reports(franchise_branch_1.name))
            end
          end

          context "when location name changed" do
            before do |example|
              franchise_branch_1.update_columns(name: 'ABC')
              Location.reindex
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              expect(response_body['report_headers'])
                .to eq(expected_headers)
              expect(response_body['reports'])
                .to eq(expected_reports('ABC'))
            end
          end
        end

        context 'when has data and exclude location' do
          let(:group_by) { 'location' }
          let(:is_select_all_location) { 'true' }
          let(:exclude_location_ids) { "#{franchise_branch_1.id}" }
          let(:start_date) { 1.months.ago.strftime('%d/%m/%Y') }
          let(:end_date) { Time.now.strftime('%d/%m/%Y') }

          before do |example|
            Location.reindex

            travel_to Time.utc(2023, 7, 29, 11, 0)
            @header = authentication_header(owner)

            payment_methods_gopay
            local_bulk_recent_transactions_owned_online_branch_1
            local_bulk_recent_transactions_owned_online_branch_1.each_with_index do |sale_transaction, index|
              payment_method = payment_methods_gopay.detect { |pay| pay.name == "Gopay #{index}" && pay.brand = brand }
              create(:payment, payment_method: payment_method, sale_transaction: sale_transaction)
            end

            bulk_recent_transactions_franchise_branch_1.each_with_index do |sale_transaction, index|
              payment_method = payment_methods_gopay.detect { |pay| pay.name == "Gopay #{index}" && pay.brand = brand }
              create(:payment, payment_method: payment_method, sale_transaction: sale_transaction)
              sale_transaction.payments.first.destroy
            end
            payment = bulk_recent_transactions_franchise_branch_1.last.payments.reload.first
            payment.update_columns(is_cash: true)
            from_sale_transaction_create_sales_return(SaleTransaction.last)

            Location.reindex

            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          after do
            travel_back
          end

          it 'returns a valid report response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            response_body = JSON.parse(response.body)
            expect(response_body['report_headers'])
              .to eq(
                [
                  [
                    {"text"=>"Location", "weight"=>500, "colspan"=>1, "rowspan"=>2},
                    {"text"=>"Gopay 0", "alignment"=>"center", "weight"=>500, "colspan"=>5},
                    {"text"=>"Gopay 1", "alignment"=>"center", "weight"=>500, "colspan"=>5},
                    {"text"=>"Gopay 2", "alignment"=>"center", "weight"=>500, "colspan"=>5},
                    {"text"=>"Gopay 3", "alignment"=>"center", "weight"=>500, "colspan"=>5},
                    {"text"=>"Gopay 4", "alignment"=>"center", "weight"=>500, "colspan"=>5},
                    {"text"=>"Total Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1,"rowspan"=>2}
                  ],
                  [
                    {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Subsidized/Fee","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Subsidized/Fee","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Subsidized/Fee","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Subsidized/Fee","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                    {"text"=>"Amount Received","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Subsidized/Fee","alignment"=>"right","weight"=>500,"colspan"=>1},
                    {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1}
                  ]
                ]
              )
            expect(response_body['reports'])
              .to eq(
                [
                  [
                    {"text"=>"Owned Location Balaraja","alignment"=>"left","weight"=>500,"size"=>14,"colspan"=>1},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"50.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"}
                  ],
                  [
                    {"text"=>"TOTAL","alignment"=>"left","weight"=>500,"size"=>14,"colspan"=>1},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"1","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"0","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"integer"},
                    {"text"=>"10.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-10.400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"-400","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"},
                    {"text"=>"50.000","alignment"=>"right","weight"=>500,"size"=>14,"colspan"=>1,"cell_format"=>"money"}
                  ]
                ]
              )
          end
        end

        context 'when report by cut off date' do
          let(:group_by) { 'location' }
          let(:is_select_all_location) { 'false' }
          let(:location_ids) { "#{sale_transaction_5.location_id}" }
          let(:start_date) { Time.now.strftime('%d/%m/%Y') }
          let(:end_date) { Time.now.strftime('%d/%m/%Y') }

          before do |example|
            Location.reindex

            brand.report_setting.update_columns(cut_off_time: '05:00', cut_off_mode: 'per_closing')
            travel_to Time.utc(2023, 7, 29, 11, 0)
            @header = authentication_header(owner)
            sale_transaction_5.update_columns(local_sales_time: '2023-07-29 06:00:00')
            sale_transaction_6.update_columns(local_sales_time: '2023-07-29 04:00:00')

            Location.reindex

            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          after do
            travel_back
          end

          it 'should only return sales within cut off time (sale_transaction_5)' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['report_headers']).to eql(
              [[{"text"=>"Location", "weight"=>500, "colspan"=>1, "rowspan"=>2},
                {"text"=>sale_transaction_5.payment_method_names,
                "alignment"=>"center",
                "weight"=>500,
                "colspan"=>5},
                {"text"=>"Total Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1,
                "rowspan"=>2}],
              [{"text"=>"No of Sales", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                {"text"=>"No of Refund", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                {"text"=>"Amount Received",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
                {"text"=>"Subsidized/Fee",
                "alignment"=>"right",
                "weight"=>500,
                "colspan"=>1},
                {"text"=>"Net Received", "alignment"=>"right", "weight"=>500, "colspan"=>1}]]
            )
            expect(response_body['reports'])
              .to eq([[{"text"=>"Owned Location Sudirman",
              "alignment"=>"left",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1},
             {"text"=>"1",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"integer"},
             {"text"=>"0",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"integer"},
             {"text"=>"10.000",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"-10.400",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"-400",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"10.000",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"}],
            [{"text"=>"TOTAL",
              "alignment"=>"left",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1},
             {"text"=>"1",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"integer"},
             {"text"=>"0",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"integer"},
             {"text"=>"10.000",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"-10.400",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"-400",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"},
             {"text"=>"10.000",
              "alignment"=>"right",
              "weight"=>500,
              "size"=>14,
              "colspan"=>1,
              "cell_format"=>"money"}]])
          end
        end
      end
    end
  end
end
