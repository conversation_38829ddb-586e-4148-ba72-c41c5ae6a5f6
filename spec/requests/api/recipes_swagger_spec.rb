require './spec/shared/audits'
require './spec/shared/swagger'
require './spec/shared/recipes'
require './spec/shared/recipe_line_customs'
require './spec/shared/productions'
require './spec/shared/inventories'
require './spec/shared/vendor_products'
require './spec/shared/access_lists'
require './spec/shared/storage_section_mappings'
require './spec/shared/recipe_line_customs'

describe 'api/recipes', type: :request, search: true do
  include_context 'recipes creations'
  include_context 'swagger after response'
  include_context 'recipe line customs creations'
  include_context 'productions creations'
  include_context 'inventories creations'
  include_context 'vendor products creations'
  include_context 'access lists creations'
  include_context 'storage section mappings creations'
  include_context 'recipe line customs creations'

  # cleaning up
  before(:all) do
    Product.reindex
  end

  before(:each) do
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:owner) { create(:hq_owner) }
  let(:central_kitchen) do
    owner.create_new_location(build(:location_params, {
                                      name: "Location #{SecureRandom.hex}",
                                      initial: "Initial #{SecureRandom.hex}",
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'central_kitchen',
                                      central_kitchen_ids: []
                                    }))
  end
  let!(:main_branch) do
    owner.create_new_location(build(:location_params, {
                                      name: "Location #{SecureRandom.hex}",
                                      initial: "Initial #{SecureRandom.hex}",
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:brand) { owner.active_brand }

  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product_non_internal_recipe) { create(:product, brand: brand, product_unit: product_unit, internal_distribution_type: false) }
  let(:product_without_recipe) { create(:product, brand: brand, product_unit: product_unit) }
  let(:other_product_unit) { create(:product_unit, brand: brand, name: 'Kg') }
  let(:product_unit_conversion) { create(:product_unit_conversion, product: product_without_recipe, product_unit: other_product_unit) }

  let(:product_for_recipe_with_unit_conversions) { create(:product, brand: brand, product_unit: product_unit, product_unit_conversions: [
    build(:product_unit_conversion, product_unit: other_product_unit)
  ]) }

  let(:recipe_with_unit_conversion) { create(:recipe, auto_create: true, brand: brand, product: product_for_recipe_with_unit_conversions, product_unit_id: other_product_unit.id ) }

  let(:recipe_request_params) do
    recipe_line_custom_details = build(:recipe_line_custom_details_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_lines = build(:recipe_line_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_line_customs = build(:recipe_line_customs_params, order_type_ids:['1','2'], recipe_line_custom_details_attributes: recipe_line_custom_details)
    build(:recipe_params, product_id: latte.id, product_unit_id: latte.product_unit.id,
                          recipe_lines_attributes: recipe_lines,recipe_line_customs_attributes: recipe_line_customs)
  end

  let(:recipe_request_duplicate_params) do
    recipe_line_custom_details = build(:recipe_line_custom_details_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_lines = build(:recipe_line_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_line_customs = build(:recipe_line_customs_params, order_type_ids:['1','2'], recipe_line_custom_details_attributes: recipe_line_custom_details)
    recipe_line_customs_2 = build(:recipe_line_customs_params, order_type_ids:['2','3'], recipe_line_custom_details_attributes: recipe_line_custom_details)
    build(:recipe_params, product_id: latte.id, product_unit_id: latte.product_unit.id,
          recipe_lines_attributes: recipe_lines,recipe_line_customs_attributes: [recipe_line_customs,recipe_line_customs_2])
  end

  let(:recipe_line_custom_params) do
    recipe_line_custom_details = build(:recipe_line_custom_details_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    build(:recipe_line_customs_params, order_type_ids:['1','2'], recipe_line_custom_details_attributes: recipe_line_custom_details)
  end

  let(:spicy_burger_recipe_batches_request_params) do
    recipe_line_custom_details = build(:recipe_line_custom_details_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_lines = build(:recipe_line_params, product_id: coffee.id, product_unit_id: coffee.product_unit.id)
    recipe_line_customs = build(:recipe_line_customs_params, order_type_ids:['1','2'], recipe_line_custom_details_attributes: recipe_line_custom_details)
    build(:recipe_params, product_id: latte_owned_branch_1.id, product_unit_id: latte_owned_branch_1.product_unit.id,
          recipe_lines_attributes: recipe_lines,recipe_line_customs_attributes: recipe_line_customs)
  end

  let(:recipe_with_conversion_request_params) do
    recipe_lines = build(:recipe_line_params, product_id: product_without_recipe.id, product_unit_id: product_unit_conversion.product_unit.id)
    build(:recipe_params, product_id: latte.id, product_unit_id: product_unit_conversion.product_unit.id,
                          recipe_lines_attributes: recipe_lines)
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes/{id}/history' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('history recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      context 'when has single recipe line', bullet: :skip do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_history_audit'
          let(:location_id) { central_kitchen.id }
          let(:product_id) { latte.id }
          let(:id) { cheese_burger_recipe_batches.id }

          before do |example|
            submit_request(example.metadata)
          end

          it_should_behave_like 'audit history response'
        end
      end

      context 'when has multiple recipe line, and recipe line was deleted', bullet: :skip do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_history_audit'

          let(:location_id) { central_kitchen.id }
          let(:product_id) { latte_recipe_batches.product.id }
          let(:id) { latte_recipe_batches.id }

          before do |example|
            last_recipe_line = latte_recipe_batches.recipe_lines.last
            last_recipe_line.update!(quantity: 15)
            last_recipe_line.destroy
            submit_request(example.metadata)
          end

          it_should_behave_like 'audit history response'
        end
      end

      context 'when has multiple recipe line, and recipe line and product was deleted', bullet: :skip do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_history_audit'

          let(:location_id) { central_kitchen.id }
          let(:product_id) { latte_recipe_batches.product.id }
          let(:id) { latte_recipe_batches.id }

          before do |example|
            last_recipe_line = latte_recipe_batches.recipe_lines.last
            product = last_recipe_line.product
            last_recipe_line.update!(quantity: 15)
            last_recipe_line.destroy
            product.update_columns(deleted: true)
            submit_request(example.metadata)
          end

          it_should_behave_like 'audit history response'
        end
      end
    end
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes/{id}/deactivate' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'
    parameter name: 'id', in: :path, type: :string, description: 'id'

    patch('deactivate recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      response(204, 'successful') do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { latte.id }
        let(:id) { cheese_burger_recipe_batches.id }
        before do |example|
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response' do |example|
          assert_response_matches_metadata(example.metadata)
        end
      end
    end
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes/{id}/reactivate' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'
    parameter name: 'id', in: :path, type: :string, description: 'id'

    patch('reactivate recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      response(204, 'successful') do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { latte.id }
        let(:id) { cheese_burger_recipe_batches.id }
        before do |example|
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response' do |example|
          assert_response_matches_metadata(example.metadata)
        end
      end
    end
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes/{id}' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'
    parameter name: 'id', in: :path, type: :string, description: 'id'

    delete('Delete recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      response(204, 'successful') do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { latte.id }
        let(:id) { cheese_burger_recipe_batches.id }
        before do |example|
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response' do |example|
          assert_response_matches_metadata(example.metadata)
          expect(cheese_burger_recipe_batches.reload.deleted).to be_truthy
        end
      end

      response(204, 'successful') do
        context 'when it is an option set recipe' do
          let(:location_id) { central_kitchen.id }
          let(:product_id) { latte.id }
          let(:id) { latte_option_set_recipe.id }

          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            access_list.save!
            SyncRecipeToProductJob.perform_now(latte.id)
            Product.search_index.refresh
            submit_request(example.metadata)
          end

          it 'returns a valid 204 response, with recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(latte_option_set_recipe.reload.deleted).to be_truthy
          end
        end
      end
    end
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'

    post('create recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_recipe'
      }

      response(201, 'successful') do
        schema '$ref' => '#/components/responses/response_create_recipe'
        let(:location_id) { central_kitchen.id }

        context 'when create recipe with recipe swap attributes' do
          let(:param) { { recipe: recipe_latte_with_swap_params }}
          let(:product_id) { latte.id }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to create recipe with recipe swap' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['line_swap_products']).to match_array(
              [
                {"id"=>Recipe.last.recipe_line_swap_products.last.id,
                  "product_from"=>
                    {"id"=>coffee_milk.id, "name"=>"Coffee Milk", "sku"=>"coffee_milk", "image_url"=>""},
                  "product_to"=>
                    {"id"=>ginger_milk.id,  "name"=>"Ginger Milk", "sku"=>"ginger_milk", "image_url"=>""},
                    "ratio"=>"80.0",}]
            )
          end
        end

        context 'when selected 1 access_list_ids is Brand Owner' do
          let(:param) { { recipe: recipe_request_params.merge({ access_list_ids: [main_branch_permission.id] }) } }
          let(:product_id) { latte.id }

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, access_list_ids: [main_branch_permission.id]).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when selected 1 access_list_ids is Not Brand Owner' do
          let(:param) { { recipe: recipe_request_params.merge({ access_list_ids: [sub_branch_permission.id] }) } }
          let(:product_id) { latte.id }

          it 'returns a Recipe with default Brand Owner added' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, access_list_ids: [main_branch_permission.id, sub_branch_permission.id]).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when all access_list_ids' do
          let(:param) { { recipe: recipe_request_params.merge({ access_list_ids: [] }) } }
          let(:product_id) { latte.id }

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, access_list_ids: []).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when batches' do
          let(:product_id) { latte.id }

          context 'when provide shelf life and shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'batches'
              recipe_request_params
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe).to be_batches
              expect(recipe.shelf_life).to eq(7)
              expect(recipe.shelf_life_type).to eq('weeks')

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when does not provide shelf life but provide shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = nil
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'batches'
              recipe_request_params
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe).to be_batches
              expect(recipe.shelf_life).to be_nil
              expect(recipe.shelf_life_type).to eq('weeks')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when made_to_order' do
          let(:product_id) { latte.id }

          context 'when provide shelf life and shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe).to be_made_to_order
              expect(recipe.shelf_life).to eq(7)
              expect(recipe.shelf_life_type).to eq('weeks')

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when provide shelf life but not shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = nil
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe).to be_made_to_order
              expect(recipe.shelf_life).to eq(7)
              expect(recipe.shelf_life_type).to be_nil

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when create recipe with custom order' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            it 'should be able to create recipe with custom order' do |example|
              expect do
                submit_request(example.metadata)
              end
                .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe.recipe_line_customs.count).to eq(1)
              expect(recipe.recipe_line_customs[0].recipe_line_custom_details.count).to eq(1)
              expect(recipe.recipe_line_customs[0].recipe_line_custom_details[0].product_id).to eq(coffee.id)

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when create multiple recipe with custom order and same product' do
            let(:param){ spicy_burger_recipe_batches_request_params }
            before do
              recipe_line_custom_with_detail
            end

            it 'should be able to create recipe with custom order' do |example|
              expect do
                submit_request(example.metadata)
              end
                .to change { latte_owned_branch_1.reload.recipe.present? }.from(false).to(true)

              recipe = latte_owned_branch_1.recipe
              expect(recipe.recipe_line_customs.count).to eq(1)
              expect(recipe.recipe_line_customs[0].recipe_line_custom_details.count).to eq(1)
              expect(recipe.recipe_line_customs[0].recipe_line_custom_details[0].product_id).to eq(coffee.id)

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when does not provide shelf life but provide shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = nil
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)

              recipe = latte.recipe
              expect(recipe).to be_made_to_order
              expect(recipe.shelf_life).to be_nil
              expect(recipe.shelf_life_type).to eq('weeks')

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when has vendor product' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            before do
              vendor_product_all_locations_latte
            end

            it 'returns a 201 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { latte.reload.recipe.present? }.from(false).to(true)
              .and change { latte.reload.vendor_products.count }.from(1).to(0)

              recipe = latte.recipe
              expect(recipe).to be_made_to_order
              expect(recipe.shelf_life).to eq(7)
              expect(recipe.shelf_life_type).to eq('weeks')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when selected 1 production_access_list_ids is Brand Owner' do
          let(:param) { { recipe: recipe_request_params.merge({ production_access_list_ids: [main_branch_permission.id] }) } }
          let(:product_id) { latte.id }

          it 'should create recipe successfully' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, production_access_list_ids: [main_branch_permission.id]).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when selected 1 access_list_ids is Not Brand Owner' do
          let(:param) { { recipe: recipe_request_params.merge({ production_access_list_ids: [sub_branch_permission.id] }) } }
          let(:product_id) { latte.id }

          it 'returns a Recipe with default Brand Owner added' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, production_access_list_ids: [main_branch_permission.id, sub_branch_permission.id]).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when selected 1 production_outlet_ids' do
          let(:param) { { recipe: recipe_request_params.merge({ production_outlet_ids: [owned_branch_1.id] }) } }
          let(:product_id) { latte.id }

          it 'should create recipe successfully' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, production_outlet_ids: [owned_branch_1.id]).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when select all outlet' do
          let(:param) {{ recipe: recipe_request_params.merge({ is_select_all_outlet: true, production_outlet_ids: [] }) }}
          let(:product_id) { latte.id }

          it 'should create recipe that is allowed for production in all outlet' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, production_outlet_ids: brand.locations.outlet.pluck(:id)).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['is_select_all_outlet']).to eq(true)
          end
        end

        context 'when select all outlet and exclude some outlet' do
          let(:param) {{ recipe: recipe_request_params.merge({ is_select_all_outlet: true, exclude_production_outlet_ids: [owned_branch_1.id] }) }}
          let(:product_id) { latte.id }

          it 'should create recipe that is allowed for production in all outlet' do |example|
            expect do
              submit_request(example.metadata)
            end.to change { Recipe.where(product: latte, production_outlet_ids: brand.locations.outlet.where.not(id: owned_branch_1.id).pluck(:id)).size }.from(0).to(1)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['is_select_all_outlet']).to eq(false)
          end
        end
      end

      response(422, 'unprocessable') do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { latte.id }

        context 'when product remove and swap is the same' do
          let(:param) { { recipe: recipe_latte_error_with_swap_params }}
          let(:product_id) { latte.id }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should not be able to create recipe with recipe swap' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"recipe_lines"=>["Recipe lines Product chosen on 'Remove product from original recipe' cannot be used in 'Swap recipe - From (Original item)'"]}})
          end
        end

        context 'when product is not internal produce' do
          let(:param) { { recipe: recipe_request_params.merge({ access_list_ids: [main_branch_permission.id] }) } }
          let(:product_id) { latte.id }

          before do |example|
            latte.update!(internal_produce_type: false)
            submit_request(example.metadata)
          end

          it 'returns a valid 422 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"product"=>["Product type must be 'Internal produce'"]}})
          end
        end

        context 'when batches' do
          context 'when provide shelf life but not shelf life type' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = nil
              recipe_request_params[:recipe_type] = 'batches'
              recipe_request_params
            end

            it 'returns a 422 response' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to not_change { latte.reload.recipe.present? }

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when made_to_order' do
          let(:product_id) { latte.id }

          context 'when recipe product is variance parent' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            before do
              latte_variant_choco
            end

            it 'returns a 422 response' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte.reload.recipe.present? }

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>{"product"=>["Product cannot be parent variance"]}}
              )
            end
          end

          context 'when recipe ingredient is variance parent' do
            let(:param) do
              recipe_request_params[:shelf_life] = 7
              recipe_request_params[:shelf_life_type] = "weeks"
              recipe_request_params[:recipe_type] = 'made_to_order'
              recipe_request_params
            end

            before do
              coffee_variant_robusta
            end

            it 'returns a 422 response' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte.reload.recipe.present? }

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>{"recipe_lines[0].product"=>["Recipe lines[0] product cannot be parent variance"]}}
              )
            end
          end

          context 'when same order type ids in 2 custom order' do
            let(:param){ recipe_request_duplicate_params }
            it 'returns order type not unique error' do |example|
              expect do
                submit_request(example.metadata)
              end
                .to not_change { latte.reload.recipe.present? }
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
               {"errors"=>{"OrderType"=>["Ordertype must be unique"]}}

             )
            end
          end
        end

        context 'when direct cyclic', bullet: :skip do
          let(:param) do
            recipe_request_params[:shelf_life] = 7
            recipe_request_params[:shelf_life_type] = "weeks"
            recipe_request_params[:recipe_type] = 'batches'
            recipe_request_params[:product_id] = milk.id
            recipe_request_params[:product_unit_id] = milk.product_unit_id
            recipe_request_params[:recipe_lines_attributes][:product_id] = latte.id
            recipe_request_params[:recipe_lines_attributes][:product_unit_id] = latte.product_unit_id
            recipe_request_params
          end

          before do
            latte_recipe_batches
          end

          it 'returns a 422 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { milk.reload.recipe.present? }.from(false)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"errors"=>{"recipe_lines[0].product"=>["Recipe lines[0] product is the parent recipe of Latte"]}}
            )
          end
        end

        context 'when indirect cyclic', bullet: :skip do
          let(:param) do
            recipe_request_params[:shelf_life] = 7
            recipe_request_params[:shelf_life_type] = "weeks"
            recipe_request_params[:recipe_type] = 'batches'
            recipe_request_params[:product_id] = ginger.id
            recipe_request_params[:product_unit_id] = ginger.product_unit_id
            recipe_request_params[:recipe_lines_attributes][:product_id] = latte.id
            recipe_request_params[:recipe_lines_attributes][:product_unit_id] = latte.product_unit_id
            recipe_request_params
          end

          before do
            # Latte has milk ingredient
            latte_recipe_batches

            # Let say milk recipe has an ingredient of ginger
            @recipe = build(:recipe, :batches, brand: brand, product: milk, product_unit: milk.product_unit, access_list_ids: [AccessList.brand_owner_id, AccessList.location_owner_id])
            @recipe.recipe_lines << build(:recipe_line, product: ginger, product_unit: ginger.product_unit)
            @recipe.save!

            recipe_request_params[:recipe_line_customs_attributes] = []
            # Then ginger recipe has ingredient of latte (param above)
          end

          it 'returns a 422 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { ginger.reload.recipe.present? }.from(false)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"errors"=>{"recipe_lines[0].product"=>["Recipe lines[0] product is the parent recipe of Latte"]}}
            )
          end
        end
      end
    end
  end

  path '/api/locations/{location_id}/products/{product_id}/recipes/{id}' do
    parameter name: 'location_id', in: :path, type: :string, description: 'location_id'
    parameter name: 'product_id', in: :path, type: :string, description: 'product_id'
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      response(200, 'successful') do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { recipe_with_unit_conversion.product_id }
        let(:id) { recipe_with_unit_conversion.id }

        context 'when has no permission to view recipe lines' do
          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = false
            access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response, without recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(0)
            expect(response_body['recipe']['visible']).to eq(false)
          end
        end

        context 'when has permission to view recipe lines' do
          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response, with recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
          end
        end

        context 'when access list not in access_list_ids and access_list_ids is empty' do
          before do |example|
            locations_user = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first
            locations_user.update(access_list_id: AccessList.location_owner_id)
            access_list = locations_user.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['show'] = true
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            submit_request(example.metadata)
          end

          it 'should show recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
            expect(response_body['recipe']['visible']).to eq(true)
          end
        end

        context 'when access list allowed in only visible to' do
          before do |example|
            locations_user = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first
            locations_user.update(access_list_id: AccessList.location_owner_id)
            access_list = locations_user.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['show'] = true
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            recipe_with_unit_conversion.update(access_list_ids: [AccessList.location_owner_id])
            submit_request(example.metadata)
          end

          it 'should show recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
          end
        end

        context 'when product is internal distribution type, location type is outlet and the outlet is allowed for production' do
          let(:location_id) { owned_branch_1.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          before do |example|
            recipe_with_unit_conversion.update(production_outlet_ids: [owned_branch_1.id])
            access_list = owner.locations_users_by_brand(brand: owned_branch_1.brand).where(location: owned_branch_1).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response, without recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
          end
        end

        context 'when product is internal distribution type, location type is outlet and the role is allowed to view' do
          let(:location_id) { owned_branch_1.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          before do |example|
            recipe_with_unit_conversion.update(access_list_ids: [AccessList.brand_owner_id])
            access_list = owner.locations_users_by_brand(brand: owned_branch_1.brand).where(location: owned_branch_1).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response, without recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
          end
        end

        context 'when it is an option set recipe' do
          let(:product_id) { latte.id }
          let(:id) { latte_option_set_recipe.id }

          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            SyncRecipeToProductJob.perform_now(latte.id)
            Product.search_index.refresh
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response, with recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['lines'].length).to eq(2)
          end
        end

        context 'when has recipe line custom' do
          let(:location_id) { owned_branch_1.id }
          let(:product_id) { recipe_line_custom_with_detail.recipe.product.id }
          let(:id) { recipe_line_custom_with_detail.recipe.id }

          context 'when has mapping' do
            before do |example|
              access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
              location_permission = access_list.location_permission
              location_permission['recipe']['manage_recipe_lines'] = true
              access_list.save!
              espresso_product_category_mapping
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response, with storage section in the mapping' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              section_name = response_body['recipe']['lines_customs'][0]['recipe_line_custom_details'][0]['storage_section']['name']
              expect(section_name).to eq(branch_1_ingredient_section_1.name)
            end
          end

          context 'When there is no mapping but a default storage section in the location exists' do
            before do |example|
              access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
              location_permission = access_list.location_permission
              location_permission['recipe']['manage_recipe_lines'] = true
              branch_1_refrigerator
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response, with default storage section in the location' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              section_name = response_body['recipe']['lines_customs'][0]['recipe_line_custom_details'][0]['storage_section']['name']
              expect(section_name).to eq(branch_1_refrigerator.name)
            end
          end

          context 'when does not have storage section' do
            before do |example|
              access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
              location_permission = access_list.location_permission
              location_permission['recipe']['manage_recipe_lines'] = true
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response, with default storage section in the location' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              section_name = response_body['recipe']['lines_customs'][0]['recipe_line_custom_details'][0]['storage_section']['name']
              expect(section_name).to eq(StorageSection::Unassigned.instance.name)
            end
          end
        end
      end

      context 'when use auto substitution and has negative stock' do
        response(200, 'successful') do
          let(:location_id) { central_kitchen.id }
          let(:product_id) { latte_recipe_batches.product_id }
          let(:id) { latte_recipe_batches.id }

          before do |example|
            substitute_line_1
            create(:inventory,
                   product: latte_recipe_batches.recipe_lines.first.product,
                   location: central_kitchen,
                   in_stock: 0, out_stock: 100,
                   stock_date: Date.today,
                   resource: today_owned_branch_1_stock_adjustment,
                   resource_line: today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
            )
            create(:inventory,
                   product: substitute_line_1.product,
                   location: central_kitchen,
                   in_stock: 100, out_stock: 0,
                   stock_date: Date.today,
                   resource: today_owned_branch_1_stock_adjustment,
                   resource_line: today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
            )
            brand.production_setting.update_columns(use_recipe_substitute_on_no_stock: true)
            submit_request(example.metadata)
          end

          it 'should be able to substitute product as recipe line and substitutes' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            recipe_line = latte_recipe_batches.recipe_lines.first
            recipe_line_2 = latte_recipe_batches.recipe_lines.second
            expect(response_body['recipe']['lines']).to eql(
              [
                {
                  "recipe_line_substitutes"=>
                  [
                    {
                      "id"=>substitute_line_1.id,
                      "product"=>{"id"=>substitute_line_1.product_id, "name"=>substitute_line_1.product.name,
                                  "image_url"=>"",
                                  "sku"=>substitute_line_1.product.sku},
                      "product_unit"=>{"id"=>substitute_line_1.product_unit_id,
                                       "name"=>substitute_line_1.product_unit.name},
                      "quantity"=>"1.0",
                      "is_selected"=>true,
                      "stock"=>"100.0",
                      "storage_section"=>{
                        "id"=>0,
                        "name"=>"Default Section"
                      }
                    }
                  ],
                  "product"=>
                    {
                      "id"=>recipe_line.product_id, "image_url"=>"",
                      "name"=>recipe_line.product.name, "sku"=>recipe_line.product.sku
                    },
                  "product_unit"=>
                    {
                      "id"=>recipe_line.product_unit_id, "name"=>recipe_line.product_unit.name
                    },
                  "quantity"=>"1.0",
                  "is_selected"=>false,
                  "stock"=>"-100.0",
                  "storage_section"=>{
                    "id"=>0,
                    "name"=>"Default Section"
                  },
                  "id"=>recipe_line.id
                },
                {
                  "id"=>recipe_line_2.id,
                  "recipe_line_substitutes"=>[],
                  "product"=>{"id"=>recipe_line_2.product_id, "image_url"=>"",
                              "name"=>recipe_line_2.product.name, "sku"=>recipe_line_2.product.sku},
                  "product_unit"=>{"id"=>recipe_line_2.product_unit_id, "name"=>recipe_line_2.product_unit.name},
                  "quantity"=>"1.0",
                  "is_selected"=>true,
                  "stock"=>"0.0",
                  "storage_section"=>{
                    "id"=>0,
                    "name"=>"Default Section"
                  }
                }
              ]
            )
          end
        end
      end

      context 'user has permission to see product price' do
        before do |example|
          access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
          location_permission = access_list.location_permission
          location_permission['product'] = location_permission['product'].merge({ 'show' => true })
          access_list.save!
          submit_request(example.metadata)
        end

        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_recipe'
          let(:location_id) { central_kitchen.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array %w[recipe]

            expect(response_body['recipe'].keys).to match_array %w[
              id product product_unit product_unit_conversions recipe_type
              expected_yield status instruction shelf_life shelf_life_type
              updated_at lines lines_customs can_edit can_toggle_status access_lists is_select_all_access_list
              is_select_all_production_access_list production_access_lists production_outlets visible
              exclude_production_outlets is_select_all_outlet line_swap_products
              yield_storage_section
            ]
            expect(response_body['recipe']['product_unit_conversions'].first.keys).to match_array %w[id converted_qty internal_price name]
            expect(response_body['recipe']['product_unit_conversions'].last['internal_price']).should_not be_nil
            expect(response_body['recipe']['product'].keys).to match_array %w[id name internal_distribution_type]
            expect(response_body['recipe']['product_unit'].keys).to match_array %w[id name]

            recipe_line = response_body['recipe']['lines'].first
            expect(recipe_line.keys).to match_array %w[id is_selected product product_unit quantity recipe_line_substitutes stock storage_section]
            expect(recipe_line['product'].keys).to match_array %w[id name sku image_url]
            expect(recipe_line['product_unit'].keys).to match_array %w[id name]
          end

          it 'should be able to see product unit conversion' do |example|
            response_body = JSON.parse(response.body)
            unit_conversions = response_body['recipe']['product_unit_conversions']
            recipe_product = recipe_with_unit_conversion.product

            has_unit_conversion = unit_conversions.filter { |conversion| conversion['id'] != recipe_product.product_unit_id }.length > 0

            expect(has_unit_conversion).to be_truthy
            expect(unit_conversions.length).to be > 1
            expect(unit_conversions.first['id']).to eq(recipe_product.product_unit.id)
          end
        end
      end

      context 'user has permission to see product price and recipe have 1 access list' do
        before do |example|
          access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
          location_permission = access_list.location_permission
          location_permission['product'] = location_permission['product'].merge({ 'show' => true })
          access_list.save!

          recipe_with_unit_conversion.access_list_ids = [access_list.id]
          recipe_with_unit_conversion.save
          submit_request(example.metadata)
        end

        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_recipe'
          let(:location_id) { central_kitchen.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['access_lists']).to eq([{"id"=>1, "name"=>"HQ Preset"}])
          end
        end
      end

      context 'user has permission to see product price and recipe have all access list' do
        before do |example|
          access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
          location_permission = access_list.location_permission
          location_permission['product'] = location_permission['product'].merge({ 'show' => true })
          access_list.save!
          submit_request(example.metadata)
        end

        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_recipe'
          let(:location_id) { central_kitchen.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            recipe = Recipe.where(id: response_body['recipe']['id']).first
            expect(recipe.access_list_ids).to eq([])
            expect(response_body['recipe']['access_lists']).to eq([{"id"=>1, "name"=>"HQ Preset"}, {"id"=>3, "name"=>"Admin"}])
          end
        end
      end

      context 'user dont have permission to see product price' do
        response(200, 'successful') do
          let(:location_id) { central_kitchen.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            location_permission['product'] = location_permission['product'].merge({ 'view_price' => false })
            access_list.save!
            submit_request(example.metadata)
          end

          it 'should return nil as internal price' do
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['product_unit_conversions'].last['internal_price']).to be_nil
          end
        end
      end

      context 'user dont have permission to see product' do
        response(200, 'successful') do
          let(:location_id) { central_kitchen.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          before do |example|
            access_list = owner.locations_users_by_brand(brand: central_kitchen.brand).where(location: central_kitchen).first.access_list
            location_permission = access_list.location_permission
            location_permission['product'] = location_permission['product'].merge({ 'show' => false })
            access_list.save!
            submit_request(example.metadata)
          end

          it 'should return only default unit without unit conversion' do
            response_body = JSON.parse(response.body)
            unit_conversions = response_body['recipe']['product_unit_conversions']
            recipe_product = recipe_with_unit_conversion.product

            has_unit_conversion = unit_conversions.filter { |conversion| conversion['id'] != recipe_product.product_unit_id }.length > 0

            expect(has_unit_conversion).to be_falsy
            expect(unit_conversions.length).to eq(1)
            expect(unit_conversions.first['id']).to eq(recipe_product.product_unit.id)
          end
        end
      end

      context 'user location access_list permission tier is 1' do
        context 'recipe has partial access_lists saved' do
          response(200, 'successful') do
            let(:location_id) { central_kitchen.id }
            let(:product_id) { recipe_with_unit_conversion.product_id }
            let(:id) { recipe_with_unit_conversion.id }
            let(:unused_permission) { create(:access_list, :sub_branch) }

            before do |example|
              unused_permission
              AccessList.all.each do |access_list|
                access_list.location_permission['recipe'] = access_list.location_permission['recipe'].merge({ 'show' => true })
                access_list.permission_tier = 2 if access_list.permission_tier > 1
                access_list.save!
              end

              recipe_with_unit_conversion.access_list_ids = [main_branch_permission.id]
              recipe_with_unit_conversion.save

              submit_request(example.metadata)
            end

            it 'should return is_select_all_access_list with value true' do
              response_body = JSON.parse(response.body)
              expect(response_body['recipe']['is_select_all_access_list']).to eq(false)
            end
          end
        end

        context 'recipe has all access_lists saved' do
          response(200, 'successful') do
            let(:location_id) { central_kitchen.id }
            let(:product_id) { recipe_with_unit_conversion.product_id }
            let(:id) { recipe_with_unit_conversion.id }
            let(:unused_permission) { create(:access_list, :sub_branch) }

            before do |example|
              unused_permission
              AccessList.all.each do |access_list|
                access_list.location_permission['recipe'] = access_list.location_permission['recipe'].merge({ 'show' => true })
                access_list.permission_tier = 2 if access_list.permission_tier > 1
                access_list.save!
              end

              submit_request(example.metadata)
            end

            it 'should return is_select_all_access_list with value true' do
              response_body = JSON.parse(response.body)
              expect(response_body['recipe']['is_select_all_access_list']).to eq(true)
            end
          end
        end
      end

      context 'user location access_list permission tier > 1' do
        context 'recipe has partial access_lists saved' do
          response(200, 'successful') do
            let(:location_id) { central_kitchen.id }
            let(:product_id) { recipe_with_unit_conversion.product_id }
            let(:id) { recipe_with_unit_conversion.id }
            let(:unused_permission) { create(:access_list, :sub_branch) }

            before do |example|
              unused_permission
              AccessList.all.each do |access_list|
                access_list.location_permission['recipe'] = access_list.location_permission['recipe'].merge({ 'show' => true })
                access_list.permission_tier = 2 if access_list.permission_tier > 1
                access_list.save!
              end

              LocationsUser.all.each do |location_users|
                location_users.access_list_id = sub_branch_permission.id
                location_users.save
              end

              recipe_with_unit_conversion.access_list_ids = [main_branch_permission.id, sub_branch_permission.id]
              recipe_with_unit_conversion.save

              submit_request(example.metadata)
            end

            it 'should return is_select_all_access_list with value true' do
              response_body = JSON.parse(response.body)
              expect(response_body['recipe']['is_select_all_access_list']).to eq(false)
            end
          end
        end

        context 'recipe has all access_lists saved' do
          response(200, 'successful') do
            let(:location_id) { central_kitchen.id }
            let(:product_id) { recipe_with_unit_conversion.product_id }
            let(:id) { recipe_with_unit_conversion.id }
            let(:unused_permission) { create(:access_list, :sub_branch) }

            before do |example|
              unused_permission
              AccessList.all.each do |access_list|
                access_list.location_permission['recipe'] = access_list.location_permission['recipe'].merge({ 'show' => true })
                access_list.permission_tier = 2 if access_list.permission_tier > 1
                access_list.save!
              end

              LocationsUser.all.each do |location_users|
                location_users.access_list_id = sub_branch_permission.id
                location_users.save
              end

              submit_request(example.metadata)
            end

            it 'should return is_select_all_access_list with value true' do
              response_body = JSON.parse(response.body)
              expect(response_body['recipe']['is_select_all_access_list']).to eq(true)
            end
          end
        end
      end

      response(403, 'forbidden') do
        context 'when product is internal distribution type, location type is outlet and location not allowed for production and role not allowed to view' do
          let(:location_id) { owned_branch_1.id }
          let(:product_id) { recipe_with_unit_conversion.product_id }
          let(:id) { recipe_with_unit_conversion.id }

          before do |example|
            # Brand owner id is always in the list but for test purpose there will only be location owner id
            recipe_with_unit_conversion.update(access_list_ids: [AccessList.location_owner_id])
            access_list = owner.locations_users_by_brand(brand: owned_branch_1.brand).where(location: owned_branch_1).first.access_list
            location_permission = access_list.location_permission
            location_permission['recipe']['manage_recipe_lines'] = true
            access_list.save!
            submit_request(example.metadata)
          end

          it 'should be unauthorized to view' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end
    end

    patch('update recipe') do
      tags 'Restaurant - Recipe'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_update_recipe'
      }

      context 'when create recipe with recipe swap attributes' do
        response(200, 'successful') do
          let(:param) { recipe_latte_with_swap_update_params }
          let(:product_id) { latte.id }
          let(:location_id) { central_kitchen.id }
          let(:id) { recipe_latte_with_swap.id }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to update recipe with new recipe swap' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['recipe']['line_swap_products']).to match_array(
              [
                {"id"=>Recipe.last.recipe_line_swap_products.last.id,
                  "product_from"=>
                    {"id"=>coffee_milk.id, "name"=>"Coffee Milk", "sku"=>"coffee_milk", "image_url"=>""},
                  "product_to"=>
                    {"id"=>ginger_milk.id,  "name"=>"Ginger Milk", "sku"=>"ginger_milk", "image_url"=>""},
                  "ratio"=>"75.0"}]
            )
          end
        end
      end

      context 'when non recipe swap' do
        let(:location_id) { central_kitchen.id }
        let(:product_id) { cheese_burger.id }
        let(:id) { cheese_burger_recipe_batches.id }

        context 'when selected access_list_ids is Brand Owner' do
          response(200, :ok) do
            schema '$ref' => '#/components/responses/response_update_recipe'

            let(:param) { { recipe: { recipe_type: 'made_to_order',
                                    access_list_ids: [main_branch_permission.id] } } }

            it 'returns a valid 200 response' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end.not_to change { Recipe.where(id: cheese_burger_recipe_batches.id).first.access_list_ids }.from([main_branch_permission.id])

              response_body = JSON.parse(response.body)

              recipe_response = response_body['recipe']
              expect(recipe_response['id']).to eq(cheese_burger_recipe_batches.id)
              expect(recipe_response['recipe_type']).to eq('made_to_order')
            end
          end
        end

        context 'when selected 1 access_list_ids is Not Brand Owner' do
          response(200, :ok) do
            let(:param) { { recipe: { recipe_type: 'made_to_order',
                                    access_list_ids: [sub_branch_permission.id] } } }

            it 'returns a Recipe with default Brand Owner added' do |example|
              expect do
                submit_request(example.metadata)
              end.to change { Recipe.where(id: cheese_burger_recipe_batches.id).first.access_list_ids }.from([main_branch_permission.id]).to([main_branch_permission.id, sub_branch_permission.id])
              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when all access_list_ids' do
          response(200, :ok) do
            schema '$ref' => '#/components/responses/response_update_recipe'

            let(:param) { { recipe: { recipe_type: 'made_to_order',
                                      access_list_ids: [] } } }

            it 'returns a valid 200 response' do |example|
              expect do
                submit_request(example.metadata)
              end.to change { Recipe.where(id: cheese_burger_recipe_batches.id).first.access_list_ids }.from([main_branch_permission.id]).to([])
              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when batches' do
          let(:product_id) { cheese_burger.id }
          let(:id) { cheese_burger_recipe_batches.id }

          context 'when duplicate products' do
            response(422, 'unprocessable_entity') do
              let(:param) do
                recipe_request_params = { recipe: {} }

                recipe_line_1 = cheese_burger_recipe_batches.recipe_lines.first
                recipe_line_2 = cheese_burger_recipe_batches.recipe_lines.second
                recipe_request_params[:recipe][:shelf_life] = 9
                recipe_request_params[:recipe][:shelf_life_type] = "weeks"
                recipe_request_params[:recipe][:recipe_type] = 'batches'
                recipe_request_params[:recipe][:recipe_lines_attributes] = [
                  {
                    id: recipe_line_1.id,
                    product_id: recipe_line_1.product_id,
                    product_unit_id: recipe_line_1.product_unit_id,
                    quantity: recipe_line_1.quantity,
                    recipe_line_substitutes_attributes: []
                  },
                  {
                    id: recipe_line_2.id,
                    product_id: recipe_line_2.product_id,
                    product_unit_id: recipe_line_2.product_unit_id,
                    quantity: recipe_line_2.quantity,
                    recipe_line_substitutes_attributes: [],
                  },
                  {
                    id: nil,
                    product_id: recipe_line_1.product_id,
                    product_unit_id: recipe_line_1.product_unit_id,
                    quantity: recipe_line_1.quantity,
                    recipe_line_substitutes_attributes: []
                  }
                ]

                recipe_request_params
              end

              it 'should not be able to update recipe' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { cheese_burger_recipe_batches.reload.shelf_life }.from(nil)
                .and not_change { cheese_burger_recipe_batches.reload.shelf_life_type }.from(nil)

                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eql({"message"=>"These products are duplicated: #{cheese_burger_recipe_batches.recipe_lines.first.product.name}"})
              end
            end
          end

          context 'when provide shelf life and shelf life type' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = { recipe: {} }

                recipe_line_1 = cheese_burger_recipe_batches.recipe_lines.first
                recipe_line_2 = cheese_burger_recipe_batches.recipe_lines.second
                recipe_request_params[:recipe][:shelf_life] = 9
                recipe_request_params[:recipe][:shelf_life_type] = "weeks"
                recipe_request_params[:recipe][:recipe_type] = 'batches'
                recipe_request_params[:recipe][:recipe_lines_attributes] = [
                  {
                    id: recipe_line_1.id,
                    product_id: recipe_line_1.product_id,
                    product_unit_id: recipe_line_1.product_unit_id,
                    quantity: recipe_line_1.quantity,
                    recipe_line_substitutes_attributes: [],
                    _destroy: true
                  },
                  {
                    id: recipe_line_2.id,
                    product_id: recipe_line_2.product_id,
                    product_unit_id: recipe_line_2.product_unit_id,
                    quantity: recipe_line_2.quantity,
                    recipe_line_substitutes_attributes: [],
                  },
                  {
                    id: nil,
                    product_id: recipe_line_1.product_id,
                    product_unit_id: recipe_line_1.product_unit_id,
                    quantity: recipe_line_1.quantity,
                    recipe_line_substitutes_attributes: []
                  }
                ]

                recipe_request_params
              end

              it 'should be able to update recipe' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.shelf_life }.from(nil).to(9)
                .and change { cheese_burger_recipe_batches.reload.shelf_life_type }.from(nil).to('weeks')

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when does not provide shelf life but provide shelf life type' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = {}

                recipe_request_params[:shelf_life] = nil
                recipe_request_params[:shelf_life_type] = "weeks"
                recipe_request_params[:recipe_type] = 'batches'
                recipe_request_params
              end

              it 'returns a 201 response' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { cheese_burger_recipe_batches.reload.shelf_life }
                .and change { cheese_burger_recipe_batches.reload.shelf_life_type }.from(nil).to('weeks')

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when provide shelf life but not shelf life type' do
            response(422, 'unprocessable') do
              let(:param) do
                recipe_request_params[:shelf_life] = 7
                recipe_request_params[:shelf_life_type] = nil
                recipe_request_params[:recipe_type] = 'batches'
                recipe_request_params
              end

              it 'returns a 422 response' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { cheese_burger_recipe_batches.reload.shelf_life }
                .and not_change { cheese_burger_recipe_batches.reload.shelf_life_type }
                .and not_change { cheese_burger_recipe_batches.reload.recipe_type }

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when has production schedule' do
            response(200, :ok) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'batches'
                recipe_request_params
              end

              before do
                cheese_burger_recipe_batches.update_columns(recipe_type: 'made_to_order')
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.recipe_type }.from('made_to_order').to('batches')

                response_body = JSON.parse(response.body)
                recipe_response = response_body['recipe']
                expect(recipe_response['id']).to eq(cheese_burger_recipe_batches.id)
                expect(recipe_response['recipe_type']).to eq('batches')
              end
            end
          end

          context 'when has production schedule but deactivated', document: false do
            response(200, :ok) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'batches'
                recipe_request_params
              end

              before do
                cheese_burger_recipe_batches.update_columns(recipe_type: 'made_to_order')
                production_schedule.update_columns(status: 'deactivated')
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.recipe_type }.from('made_to_order').to('batches')

                response_body = JSON.parse(response.body)
                recipe_response = response_body['recipe']
                expect(recipe_response['id']).to eq(cheese_burger_recipe_batches.id)
                expect(recipe_response['recipe_type']).to eq('batches')
              end
            end
          end
        end

        context 'when update to made_to_order' do
          let(:product_id) { cheese_burger.id }
          let(:id) { cheese_burger_recipe_batches.id }

          context 'when provide shelf life and shelf life type' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = {}

                recipe_request_params[:shelf_life] = 7
                recipe_request_params[:shelf_life_type] = "weeks"
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              it 'returns a 201 response' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.shelf_life }.from(nil).to(7)
                .and change { cheese_burger_recipe_batches.reload.shelf_life_type }.from(nil).to('weeks')
                .and change { cheese_burger_recipe_batches.reload.recipe_type }.from('batches').to('made_to_order')

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when provide shelf life but not shelf life type' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = {}

                recipe_request_params[:shelf_life] = 7
                recipe_request_params[:shelf_life_type] = nil
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              it 'returns a 201 response' do |example|
                expect do
                  submit_request(example.metadata)
                end.to change { cheese_burger_recipe_batches.reload.shelf_life }.from(nil).to(7)
                  .and not_change { cheese_burger_recipe_batches.reload.shelf_life_type }
                  .and change { cheese_burger_recipe_batches.reload.recipe_type }.from('batches').to('made_to_order')

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when does not provide shelf life but provide shelf life type' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = {}

                recipe_request_params[:shelf_life] = nil
                recipe_request_params[:shelf_life_type] = "weeks"
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              it 'returns a 201 response' do |example|
                expect do
                  submit_request(example.metadata)
                  cheese_burger_recipe_batches.reload
                end.to not_change { cheese_burger_recipe_batches.shelf_life }
                  .and change { cheese_burger_recipe_batches.shelf_life_type }.from(nil).to('weeks')
                  .and change { cheese_burger_recipe_batches.recipe_type }.from('batches').to('made_to_order')
                expect(cheese_burger_recipe_batches).to be_made_to_order
                expect(cheese_burger_recipe_batches.shelf_life).to be_nil
                expect(cheese_burger_recipe_batches.shelf_life_type).to eq('weeks')

                assert_response_matches_metadata(example.metadata)
              end
            end
          end

          context 'when has active week and time production schedule' do
            let(:id) { latte_recipe_batches.id }
            let(:product_id) { latte.id }

            response(422, :unprocessable_entity) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                travel_to Time.utc(2022, 6, 14, 11, 0)
                @header = authentication_header(owner)

                production_schedule
                production_schedule_one_time_multiple_schedule_line
              end

              after do
                travel_back
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end.to not_change { latte_recipe_batches.reload.recipe_type }

                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"errors"=>{"base"=>["Recipe has production schedules: MyString"]}}
                )
              end
            end
          end

          context 'when has active week production schedule' do
            let(:id) { latte_recipe_batches.id }
            let(:product_id) { latte.id }

            response(422, :unprocessable_entity) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                production_schedule
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to not_change { latte_recipe_batches.reload.recipe_type }

                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"errors"=>{"base"=>["Recipe has production schedules: MyString"]}}
                )
              end
            end
          end

          context 'when has deactivated week production schedule' do
            response(200, :unprocessable_entity) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                production_schedule.deactivated!
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.recipe_type }.from('batches').to('made_to_order')

                response_body = JSON.parse(response.body)
                recipe_response = response_body['recipe']
                expect(recipe_response['id']).to eq(cheese_burger_recipe_batches.id)
                expect(recipe_response['recipe_type']).to eq('made_to_order')
              end
            end
          end

          context 'when has active one time production schedule' do
            let(:id) { latte_recipe_batches.id }
            let(:product_id) { latte.id }

            response(422, :unprocessable_entity) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                travel_to Time.utc(2022, 6, 14, 11, 0)
                @header = authentication_header(owner)

                production_schedule_one_time
              end

              after do
                travel_back
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to not_change { latte_recipe_batches.reload.recipe_type }

                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"errors"=>{"base"=>["Recipe has production schedules: MyString"]}}
                )
              end
            end
          end

          context 'when has deactivated one time production schedule' do
            response(200, :ok) do
              let(:param) do
                recipe_request_params = {}
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                travel_to Time.utc(2022, 6, 14, 11, 0)
                @header = authentication_header(owner)

                production_schedule_one_time.deactivated!
              end

              after do
                travel_back
              end

              it 'should not update the recipe' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to change { cheese_burger_recipe_batches.reload.recipe_type }.from('batches').to('made_to_order')

                response_body = JSON.parse(response.body)
                recipe_response = response_body['recipe']
                expect(recipe_response['id']).to eq(cheese_burger_recipe_batches.id)
                expect(recipe_response['recipe_type']).to eq('made_to_order')
              end
            end
          end

          context 'when product is batch recipe ingredient' do
            response(422, :unprocessable_entity) do
              let(:param) do
                { recipe_type: 'made_to_order' }
              end

              before do
                create(:recipe_line, recipe: espresso_variant_choco_recipe_batches, product: cheese_burger, product_unit: cheese_burger.product_unit)
              end

              it 'should not update to made to order' do |example|
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end
                .to not_change { cheese_burger_recipe_batches.reload.recipe_type }.from('batches')

                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"errors"=>
                  {"product"=>["Product is part of recipe, can't be used as made to order"]}}
                )
              end
            end
          end

          context 'when has vendor products' do
            response(200, :ok) do
              schema '$ref' => '#/components/responses/response_update_recipe'

              let(:param) do
                recipe_request_params = {}

                recipe_request_params[:shelf_life] = 7
                recipe_request_params[:shelf_life_type] = "weeks"
                recipe_request_params[:recipe_type] = 'made_to_order'
                recipe_request_params
              end

              before do
                cheese_burger_recipe_batches
                vendor_product_all_locations_cheese_burger
              end

              it 'returns a 201 response' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to change { cheese_burger.reload.vendor_products.count }.from(1).to(0)

                assert_response_matches_metadata(example.metadata)
              end
            end
          end
        end

        context 'when recipe has option set' do
          let(:product_id) { latte.id }
          let(:id) { latte_option_set_recipe.id }

          response(200, :ok) do
            schema '$ref' => '#/components/responses/response_update_recipe'

            let(:param) { { recipe: { recipe_type: 'made_to_order',
                                      access_list_ids: [] } } }

            it 'returns a valid 200 response' do |example|
              expect do
                submit_request(example.metadata)
              end.to change { Recipe.where(id: latte_option_set_recipe.id).first.access_list_ids }.from([main_branch_permission.id, sub_branch_permission.id]).to([])
              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end
    end
  end

  path '/api/products' do
    get('list recipes') do
      tags 'Restaurant - Recipes'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :sort_key, in: :query, type: :string, required: false
      parameter name: :sort_order, in: :query, type: :string, required: false
      parameter name: :only_contain_recipe, in: :query, type: :string, required: false
      parameter name: :no_stock, in: :query, type: :string, required: false
      parameter name: :recipe_status, in: :query, type: :string, required: false
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :internal_produce_type, in: :query, type: :string, required: false
      parameter name: :recipe_line_product, in: :query, type: :string, required: false

      response(200, 'successful') do
        let(:location_id) { main_branch.id }
        let(:sort_key) { 'name' }
        let(:sort_order) { 'desc' }
        let(:only_contain_recipe) { 'true' }
        let(:no_stock) { 'false' }
        let(:recipe_status) { 'active' }
        let(:internal_produce_type) { 'true' }

        context 'show recipes that has allowed access_list_ids' do
          before do |example|
            cheese_burger_recipe_batches
            spicy_burger_recipe_batches
            latte_recipe_batches
            espresso_variant_choco_recipe_batches.access_list_ids = []
            espresso_variant_choco_recipe_batches.save

            Recipe.all.each do |found_recipe|
              SyncRecipeToProductJob.perform_now(found_recipe.product_id)
            end

            Product.search_index.refresh
            owner.selected_brand = brand
            submit_request(example.metadata)
          end

          it 'should return products with correct recipe permission', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            access_list_ids = Restaurant::Services::Locations::LocationUserAccessListIds.new(main_branch, owner).call
            expected_results = Recipe.where("access_list_ids @> ARRAY[?]::integer[]", access_list_ids).count
            expect(response_body['products'].size).to eq(expected_results)
          end
        end
      end

      response(200, 'successful') do
        let(:location_id) { main_branch.id }
        let(:no_stock) { 'false' }

        context 'show all recipes when only_contain_recipe is true' do
          let(:only_contain_recipe) { 'true' }
          let(:internal_produce_type) { 'true' }
          let(:recipe_status) { 'active' }
          let(:sort_key) { 'name' }
          let(:sort_order) { 'desc' }

          before do |example|
            cheese_burger_recipe_batches.access_list_ids = []
            cheese_burger_recipe_batches.save
            spicy_burger_recipe_batches.access_list_ids = []
            spicy_burger_recipe_batches.save
            latte_recipe_batches.access_list_ids = []
            latte_recipe_batches.save
            espresso_variant_choco_recipe_batches.access_list_ids = []
            espresso_variant_choco_recipe_batches.save

            Recipe.all.each do |found_recipe|
              SyncRecipeToProductJob.perform_now(found_recipe.product_id)
            end

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response when return 4 products', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expected_products_with_recipe = Recipe.includes(:product).where(recipe_type: :batches).where(products: { variance_parent_product_id: nil }).map { |recipe| recipe.product.name }.sort
            response_products = response_body['products'].map { |prod| prod['name'] }.sort
            expect(expected_products_with_recipe).to eq(response_products)
          end
        end

        context 'show all recipes when recipe_line_product is true' do
          let(:status) { 'activated' }
          let(:recipe_line_product) { 'true' }

          before do |example|
            cheese_burger_recipe_batches.access_list_ids = []
            cheese_burger_recipe_batches.save
            spicy_burger_recipe_batches.access_list_ids = []
            spicy_burger_recipe_batches.save
            latte_recipe_batches.access_list_ids = []
            latte_recipe_batches.save
            espresso_variant_choco_recipe_batches.access_list_ids = []
            espresso_variant_choco_recipe_batches.save

            Recipe.all.each do |found_recipe|
              SyncRecipeToProductJob.perform_now(found_recipe.product_id)
            end

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should return products without recipe and product with recipe', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expected_products_without_recipe = Product.includes(:recipe).where(recipe: { id: nil }, status: 'activated', no_stock: false, variance_parent_product_id: nil).select { |product| product.location_ids.include?(main_branch.id) }.map(&:name)
            expected_products_with_recipe = Recipe.includes(:product).where(recipe_type: :batches).where(products: { variance_parent_product_id: nil }).map { |recipe| recipe.product.name }
            expected_products = (expected_products_without_recipe + expected_products_with_recipe).uniq
            response_products = response_body['products'].map { |prod| prod['name'] }

            expect((expected_products - response_products).empty?).to be_truthy
          end
        end
      end
    end

    get('list recipes batches') do
      tags 'Restaurant - Recipes'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :sort_key, in: :query, type: :string, required: true
      parameter name: :sort_order, in: :query, type: :string, required: true
      parameter name: :only_contain_recipe, in: :query, type: :string, required: true
      parameter name: :no_stock, in: :query, type: :string, required: true
      parameter name: :recipe_status, in: :query, type: :string, required: true
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :recipe_type, in: :query, type: :string, required: true
      parameter name: :exclude_product_with_variances, in: :query, type: :string, required: true

      response(200, 'successful') do
        let(:location_id) { main_branch.id }
        let(:sort_key) { 'name' }
        let(:sort_order) { 'desc' }
        let(:only_contain_recipe) { 'true' }
        let(:no_stock) { 'false' }
        let(:recipe_status) { 'active' }
        let(:recipe_type) { 'batches' }
        let(:exclude_product_with_variances) { 'true' }

        context 'show recipes that has allowed access_list_ids' do
          before do |example|
            cheese_burger_recipe_batches.recipe_type = Recipe.recipe_types['made_to_order']
            cheese_burger_recipe_batches.save
            spicy_burger_recipe_batches.recipe_type = Recipe.recipe_types['batches']
            spicy_burger_recipe_batches.save
            latte_recipe_batches.recipe_type = Recipe.recipe_types['made_to_order']
            latte_recipe_batches.save
            espresso_variant_choco_recipe_batches.recipe_type = Recipe.recipe_types['batches']
            espresso_variant_choco_recipe_batches.save

            Recipe.all.each do |found_recipe|
              SyncRecipeToProductJob.perform_now(found_recipe.product_id)
            end

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response when return 2 products', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].size).to eq(2)
          end
        end
      end
    end
  end
end
