require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/swagger'
require './spec/shared/upload_url'
require './spec/shared/procurements'
require './spec/shared/wastes'
require './spec/shared/order_transaction_invoices'
require './spec/shared/stock_adjustments'
require './spec/shared/stock_openings'
require './spec/shared/disassemble_transactions'

describe 'api/products', type: :request do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'swagger after response'
  include_context 'procurements creations'
  include_context 'wastes creations'
  include_context 'order transaction invoices creations'
  include_context 'stock adjustments creations'
  include_context 'disassemble transactions creations'
  include_context 'stock openings creation'

  before(:each) do
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:another_brand) { create(:brand, name: 'another brand') }

  let(:user_access_list) do
    location_user = LocationsUser.find_by(location: central_kitchen, user: user)
    location_user.access_list
  end

  let(:online_ordering_order_type) { create(:online_ordering_order_type, online_platform_fee: 550) }
  let(:custom_order_type) { create(:order_type, name: 'sample', brand: brand, online_platform_fee: 550) }

  let(:order_from_franchise_set_product_price_by_system) { true }

  let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product_unit_2) { create(:product_unit, brand: brand, name: 'liter') }

  let(:product_unit_3) { create(:product_unit, brand: brand, name: 'galon') }

  let(:product_unit_4) { create(:product_unit, brand: brand, name: 'galon jumbo') }

  let(:product_unit_different_brand) { create(:product_unit, brand: another_brand, name: 'galon') }

  let(:other_product_unit) { create(:product_unit, brand: brand, name: 'Kg') }
  let(:product) do
    create(:product, brand: brand, product_unit: product_unit, location_ids: [owned_branch_1.id], owner_location_id: central_kitchen.id, tax: tax,
                     internal_tax: tax, exclude_location_ids: [owned_branch_2.id])
  end
  let(:product_2) { create(:product, brand: brand, product_unit: product_unit, owner_location_id: central_kitchen.id) }
  let(:product_3) { create(:product, brand: brand, product_unit: product_unit, owner_location_id: central_kitchen.id) }
  let(:product_4) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [central_kitchen.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end
  let(:product_5) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end
  let(:product_6) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [central_kitchen.id, owned_branch_2.id],
                     owner_location_id: central_kitchen.id, product_unit: product_unit)
  end
  let(:product_7) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end

  let(:product_setting_location) { create(:product_setting_location, product: product_4, location: central_kitchen) }
  let(:product_modifier) { create(:product, :modifier, brand: brand, product_unit: product_unit) }
  let(:product_variance) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }
  let(:product_2_variance) { create(:product, variance_parent_product_id: product_2.id, brand: brand, product_unit: product_unit) }
  let(:product_owned_branch_2) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], product_unit: product_unit)
  end
  let(:product_unit_conversion) { create(:product_unit_conversion, product: product, product_unit: product_unit) }
  let(:product_unit_conversion_2) { create(:product_unit_conversion, product: product, product_unit: product_unit_2) }

  let(:procurement_unit_1) do
    create(:procurement_unit, product: product, product_unit: product_unit)
  end
  let(:procurement_unit_2) do
    create(:procurement_unit, product: product, product_unit: product_unit_2)
  end

  let(:product_with_unit_conversion_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit.id)])
  end
  let(:product_with_unit_conversion_params_no_price) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit.id, internal_price: 0)])
  end
  let(:product_with_unit_conversions) do
    create(:product, brand: brand, product_unit: product_unit, product_unit_conversions: [
             build(:product_unit_conversion, product_unit: other_product_unit)
           ])
  end

  let(:product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:product_with_procurement_units_smallest_only) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit.id }])
  end

  let(:product_with_procurement_units_params_with_smallest) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }, { product_unit_id: product_unit.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:blank_procurement_units) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:blank_procurement_units_and_conversion_units) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id)
  end

  let(:partially_valid_product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }, { product_unit_id: product_unit_3.id }, { product_unit_id: product_unit_4.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:invalid_product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_3.id, internal_price: 0)])
  end

  let(:invalid_product_with_procurement_units_params_blank_unit_conversions) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }])
  end

  let(:pos_product_layout) { create(:product_layout, location_id: central_kitchen.id, payload: { products: [{ id: product.id, sequence: 1 }] }) }
  let(:option_set) { create(:option_set, brand: brand) }
  let(:product_option_set) { create(:product_option_set, product_id: product.id, option_set_id: option_set.id) }
  let(:create_setting_params) { build(:product_setting_location_params, par_unit_id: product_unit.id, location_id: central_kitchen.id) }

  let(:grab_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :grab_food,
      location: central_kitchen,
      partner_outlet_id: 'G6199595402'
    )
  end

  let(:go_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :go_food,
      location: central_kitchen,
      partner_outlet_id: 'G619959540',
    )
  end

  let(:shopee_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :shopee_food,
      location: central_kitchen,
      partner_outlet_id: '20667332'
    )
  end

  let(:variance_keys) do
    [
      "id", "name", "sku", "upc", "description", "internal_price", "sell_price", "status", "modifier", "no_stock",
      "internal_distribution_type", "external_vendor_type", "internal_produce_type", "out_of_stock_flag",
      "available_stock_flag", "variance_parent_product_id", "option_set_auto_prompt", "allow_custom_sell_price",
      "image_url", "original_image_url", "owner_location", "sell_to_customer_type", "sell_to_dine_in",
      "sell_to_grab_food", "sell_to_go_food", "sell_to_shopee_food", "sell_to_online_ordering", "internal_tax",
      "tax", "sell_tax", "sell_tax_setting", "product_category", "product_unit", "back_office_unit", "sell_unit",
      "par_unit", "price_per_order_global", "price_per_order_location", "product_option_sets"
    ]
  end

  path '/api/products/update_sell_tax_bulk', bullet: :skip do
    patch('update sell tax bulk') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_update_sell_tax_bulk'
      }

      before do
        latte
        spicy_burger

        central_kitchen
        owned_branch_1
      end

      context 'when no sell_tax' do
        context 'when multi locations with order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: custom_order_type.id, sell_tax_id: nil, sell_tax_setting: 'no_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductPricePerOrderType.count }.from(0).to(4)
              .and not_change { ProductSettingLocation.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map { |product_price_per_order_type| { location_name: product_price_per_order_type.location.name, order_type_name: product_price_per_order_type.order_type.name, sell_tax: product_price_per_order_type.sell_tax, sell_tax_setting: product_price_per_order_type.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>"Central Kitchen Location Pasar Jeruk",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"}]
              )
            end
          end
        end

        context 'when no locations' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ sell_tax_setting: 'price_include_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                latte.reload
              end
              .to not_change { ProductPricePerOrderType.count }
              .and not_change { ProductSettingLocation.count }
              .and change { latte.sell_tax_setting }.from('price_exclude_tax').to('price_include_tax')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when default locations with order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: custom_order_type.id, sell_tax_id: nil, sell_tax_setting: 'no_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductPricePerOrderType.count }.from(0).to(2)
              .and not_change { ProductSettingLocation.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map { |product_price_per_order_type| { location_name: product_price_per_order_type.location&.name, order_type_name: product_price_per_order_type.order_type.name, sell_tax: product_price_per_order_type.sell_tax, sell_tax_setting: product_price_per_order_type.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>nil,
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>nil,
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"}]
              )
            end
          end
        end

        context 'when multi locations, no order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: nil, sell_tax_id: nil, sell_tax_setting: 'no_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductSettingLocation.count }.from(0).to(4)
              .and not_change { ProductPricePerOrderType.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductSettingLocation.all.map { |product_setting_location| { location_name: product_setting_location.location.name, sell_tax: product_setting_location.sell_tax, sell_tax_setting: product_setting_location.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>nil,
                :sell_tax_setting=>"no_tax"}]
              )
            end
          end
        end

        context 'when default locations no order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: nil, sell_tax_id: nil, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                latte.reload
                spicy_burger.reload
              end
              .to not_change { ProductSettingLocation.count }
              .and not_change { ProductPricePerOrderType.count }
              .and not_change { latte.sell_tax }
              .and not_change { latte.sell_tax_setting }
              .and not_change { spicy_burger.sell_tax }
              .and not_change { spicy_burger.sell_tax_setting }

              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end

      context 'when with sell_tax' do
        context 'when multi locations with order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductPricePerOrderType.count }.from(0).to(4)
              .and not_change { ProductSettingLocation.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map { |product_price_per_order_type| { location_name: product_price_per_order_type.location.name, order_type_name: product_price_per_order_type.order_type.name, sell_tax: product_price_per_order_type.sell_tax, sell_tax_setting: product_price_per_order_type.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>"Central Kitchen Location Pasar Jeruk",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>"Owned Location Parung",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>"Owned Location Parung",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"}]
              )
            end
          end
        end

        context 'when default locations with order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductPricePerOrderType.count }.from(0).to(2)
              .and not_change { ProductSettingLocation.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map { |product_price_per_order_type| { location_name: product_price_per_order_type.location&.name, order_type_name: product_price_per_order_type.order_type.name, sell_tax: product_price_per_order_type.sell_tax, sell_tax_setting: product_price_per_order_type.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>nil,
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax"}]
              )
            end
          end
        end

        context 'when multi locations, no order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: nil, sell_tax_id: tax.id, sell_tax_setting: 'no_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductSettingLocation.count }.from(0).to(4)
              .and not_change { ProductPricePerOrderType.count }

              assert_response_matches_metadata(example.metadata)

              result = ProductSettingLocation.all.map { |product_setting_location| { location_name: product_setting_location.location.name, sell_tax: product_setting_location.sell_tax, sell_tax_setting: product_setting_location.sell_tax_setting  } }
              expect(result).to match_array(
                [{:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax"},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax"}]
              )
            end
          end
        end

        context 'when default locations no order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: nil, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                latte.reload
                spicy_burger.reload
              end
              .to not_change { ProductSettingLocation.count }
              .and not_change { ProductPricePerOrderType.count }
              .and change { latte.sell_tax }.from(nil).to(tax)
              .and not_change { latte.sell_tax_setting }
              .and change { spicy_burger.sell_tax }.from(nil).to(tax)
              .and not_change { spicy_burger.sell_tax_setting }

              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end

      context 'when related with variants' do
        before do
          latte_variant_choco
          latte_variant_mint

          @latte_choco_custom_order_type_price = create(:product_price_per_order_type, product: latte_variant_choco, order_type: custom_order_type, sell_price: 11000)
          @latte_mint_custom_order_type_price = create(:product_price_per_order_type, product: latte_variant_mint, order_type: custom_order_type, sell_price: 15000)

          @latte_choco_owned_branch_1 = create(:product_setting_location, product: latte_variant_choco, location: owned_branch_1, sell_price: 13000)
          @latte_mint_owned_branch_1 = create(:product_setting_location, product: latte_variant_mint, location: owned_branch_1, sell_price: 19000)
        end

        context 'when multi locations with order type, parent price is nil' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
              end
              .to change { ProductPricePerOrderType.count }.from(2).to(10)
              .and not_change { ProductSettingLocation.count }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map do |product_price_per_order_type|
                {
                  location_name: product_price_per_order_type.location&.name,
                  product_name: product_price_per_order_type.product.name,
                  order_type_name: product_price_per_order_type.order_type.name,
                  sell_tax: product_price_per_order_type.sell_tax,
                  sell_tax_setting: product_price_per_order_type.sell_tax_setting,
                  sell_price: product_price_per_order_type.sell_price
                }
              end

              expect(result).to match_array(
                [{:location_name=>nil,
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"default",
                :sell_price=>11000},
               {:location_name=>nil,
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"default",
                :sell_price=>15000},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil}]
              )
            end
          end
        end

        context 'when multi locations with order type, parent price is present' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            before do
              @latte_order_type_price = create(:product_price_per_order_type, product: latte, order_type: custom_order_type, sell_price: 11700, location: central_kitchen)
              @latte_order_type_price = create(:product_price_per_order_type, product: latte, order_type: custom_order_type, sell_price: 11700, location: owned_branch_1)

              @latte_choco_custom_order_type_price.update_columns(sell_price: 11000)
              @latte_mint_custom_order_type_price.update_columns(sell_price: 15000)
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
              end
              .to change { ProductPricePerOrderType.count }.from(6).to(10)
              .and not_change { ProductSettingLocation.count }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map do |product_price_per_order_type|
                {
                  location_name: product_price_per_order_type.location&.name,
                  product_name: product_price_per_order_type.product.name,
                  order_type_name: product_price_per_order_type.order_type.name,
                  sell_tax: product_price_per_order_type.sell_tax,
                  sell_tax_setting: product_price_per_order_type.sell_tax_setting,
                  sell_price: product_price_per_order_type.sell_price
                }
              end

              expect(result).to match_array(
                [{:location_name=>nil,
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"default",
                :sell_price=>11000},
               {:location_name=>nil,
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>nil,
                :sell_tax_setting=>"default",
                :sell_price=>15000},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>11700},
               {:location_name=>"Owned Location Parung",
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_tax_setting=>"price_exclude_tax",
                :sell_price=>nil}]
              )
            end
          end
        end

        context 'when default locations with order type, parent price is nil' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
              end
              .to change { ProductPricePerOrderType.count }.from(2).to(4)
              .and not_change { ProductSettingLocation.count }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map do |product_price_per_order_type|
                {
                  location_name: product_price_per_order_type.location&.name,
                  product_name: product_price_per_order_type.product.name,
                  order_type_name: product_price_per_order_type.order_type.name,
                  sell_tax: product_price_per_order_type.sell_tax,
                  sell_tax_setting: product_price_per_order_type.sell_tax_setting,
                  sell_price: product_price_per_order_type.sell_price
                }
              end
              expect(result).to match_array(
                [{:location_name=>nil,
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>nil,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>11000,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>15000,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>nil,
                :sell_tax_setting=>"price_exclude_tax"},
               ]
              )
            end
          end
        end

        context 'when default locations with order type, parent price is present' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: custom_order_type.id, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            before do
              @latte_order_type_price = create(:product_price_per_order_type, product: latte, order_type: custom_order_type, sell_price: 11700)
              @latte_choco_custom_order_type_price.update_columns(sell_price: 11000)
              @latte_mint_custom_order_type_price.destroy
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { ProductPricePerOrderType.count }.from(2).to(4)
              .and not_change { ProductSettingLocation.count }
              .and not_change { @latte_choco_custom_order_type_price.reload.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.reload.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductPricePerOrderType.all.map do |product_price_per_order_type|
                {
                  location_name: product_price_per_order_type.location&.name,
                  product_name: product_price_per_order_type.product.name,
                  order_type_name: product_price_per_order_type.order_type.name,
                  sell_tax: product_price_per_order_type.sell_tax,
                  sell_tax_setting: product_price_per_order_type.sell_tax_setting,
                  sell_price: product_price_per_order_type.sell_price
                }
              end
              expect(result).to match_array(
                [{:location_name=>nil,
                :product_name=>"Latte",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>11700,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Latte Variant Choco",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>11000,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Latte Variant Mint",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>11700,
                :sell_tax_setting=>"price_exclude_tax"},
               {:location_name=>nil,
                :product_name=>"Spicy Burger",
                :order_type_name=>"sample",
                :sell_tax=>tax,
                :sell_price=>nil,
                :sell_tax_setting=>"price_exclude_tax"},
               ]
              )
            end
          end
        end

        context 'when multi locations, no order type, parent price is nil' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: nil, sell_tax_id: tax.id, sell_tax_setting: 'no_tax' }]
              }
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
              end
              .to change { ProductSettingLocation.count }.from(2).to(8)
              .and not_change { ProductPricePerOrderType.count }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductSettingLocation.all.map do |product_setting_location|
                {
                  location_name: product_setting_location.location.name,
                  sell_tax: product_setting_location.sell_tax,
                  sell_tax_setting: product_setting_location.sell_tax_setting,
                  sell_price: product_setting_location.sell_price
                }
              end
              expect(result).to match_array(
                [{:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13000},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>19000},
               {:location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil}]
              )
            end
          end
        end

        context 'when multi locations, no order type, parent price is present' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [central_kitchen.id, owned_branch_1.id],
                order_types: [{ id: nil, sell_tax_id: tax.id, sell_tax_setting: 'no_tax' }]
              }
            end

            before do
              @latte_owned_branch_1 = create(:product_setting_location, product: latte, location: owned_branch_1, sell_price: 13500)
              @latte_ck = create(:product_setting_location, product: latte, location: central_kitchen, sell_price: 13500)
              @latte_choco_owned_branch_1.update_columns(sell_price: 13000)
              @latte_mint_owned_branch_1.update_columns(sell_price: 19000)
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
              end
              .to change { ProductSettingLocation.count }.from(4).to(8)
              .and not_change { ProductPricePerOrderType.count }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }

              assert_response_matches_metadata(example.metadata)

              result = ProductSettingLocation.all.map do |product_setting_location|
                {
                  product_name: product_setting_location.product.name,
                  location_name: product_setting_location.location.name,
                  sell_tax: product_setting_location.sell_tax,
                  sell_tax_setting: product_setting_location.sell_tax_setting,
                  sell_price: product_setting_location.sell_price
                }
              end

              expect(result).to match_array(
                [{:product_name=>"Latte",
                :location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13500},
               {:product_name=>"Latte Variant Choco",
                :location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13500},
               {:product_name=>"Latte Variant Mint",
                :location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13500},
               {:product_name=>"Spicy Burger",
                :location_name=>"Central Kitchen Location Pasar Jeruk",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil},
               {:product_name=>"Latte",
                :location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13500},
               {:product_name=>"Latte Variant Choco",
                :location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>13000},
               {:product_name=>"Latte Variant Mint",
                :location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>19000},
               {:product_name=>"Spicy Burger",
                :location_name=>"Owned Location Parung",
                :sell_tax=>tax,
                :sell_tax_setting=>"no_tax",
                :sell_price=>nil}]
              )
            end
          end
        end

        context 'when default locations no order type' do
          response(204, 'ok') do
            let(:param) do
              {
                location_ids: [],
                order_types: [{ id: nil, sell_tax_id: tax.id, sell_tax_setting: 'price_exclude_tax' }]
              }
            end

            before do
              latte_variant_choco.update_columns(sell_price: 11111)
              latte_variant_mint.update_columns(sell_price: 11323)
            end

            it 'should update the tax settings' do |example|
              expect do
                submit_request(example.metadata)

                latte.reload
                spicy_burger.reload
                @latte_choco_custom_order_type_price.reload
                @latte_mint_custom_order_type_price.reload
                latte_variant_choco.reload
                latte_variant_mint.reload
              end
              .to not_change { ProductSettingLocation.count }
              .and not_change { ProductPricePerOrderType.count }
              .and not_change { latte.sell_price }
              .and not_change { spicy_burger.sell_price }
              .and change { latte.sell_tax }.from(nil).to(tax)
              .and not_change { latte.sell_tax_setting }
              .and change { spicy_burger.sell_tax }.from(nil).to(tax)
              .and not_change { spicy_burger.sell_tax_setting }
              .and not_change { @latte_choco_custom_order_type_price.sell_price }
              .and not_change { @latte_mint_custom_order_type_price.sell_price }
              .and not_change { latte_variant_choco.sell_price }
              .and not_change { latte_variant_mint.sell_price }

              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end
    end
  end

  path '/api/products/update_internal_price_bulk' do
    patch('bulk internal price') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :is_immediate, in: :query, type: :string, required: false
      parameter name: :apply_to_pending_orders, in: :query, type: :string, required: false
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_product_internal_price_location'
      }

      response(204, 'successful') do
        context 'when empty products params' do
          let(:param) do
            {
              products: []
            }
          end

          it 'should not create internal price', bullet: :skip do |example|
            expect do
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to not_change { latte.product_internal_price_locations.count }
            .and not_change { latte.internal_price }
            .and not_change { latte.product_unit_conversions.count }
            .and not_change { cheese_burger.internal_price }
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }

            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when creating new product internal price locations' do
          let(:param) do
            {
              products: [
                {
                  id: latte.id, internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                  ],
                  product_unit_conversions_attributes: [
                    id: latte_unit_conversion_party_5_l.id,
                    internal_price: 45000
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect do
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(0).to(2)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(0).to(1)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:internal_price=>8500, :location_name=>"Owned Location Parung", :product_unit=>"cup 500 ml"}, {:internal_price=>8330, :location_name=>"Owned Location Sudirman", :product_unit=>"cup 500 ml"}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')
          end
        end

        context 'when creating new product internal price locations and has existing orders' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7676
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_party_5_l.id,
                      product_unit_id: party_5_l.id,
                      internal_price: 45000
                    },
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)
            latte_unit_conversion_party_5_l

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(0).to(4)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @latte_unit_conversion_line.reload.product_buy_price }.from(1500).to(45000)
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(337500.0)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1535.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_tax }.from(300).to(9000)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1535.2)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Owned Location Parung",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8500},
                {:location_name=>"Owned Location Sudirman",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8330},
                {:location_name=>"Franchise Location Ciputat",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7676},
                {:location_name=>"Franchise Location Rawa Buaya",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7333}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders, but not apply_to_pending_orders' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'false' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7676
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_party_5_l.id,
                      product_unit_id: party_5_l.id,
                      internal_price: 45000
                    },
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)
            latte_unit_conversion_party_5_l

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
              .to_not receive(:perform_later)

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(0).to(4)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and not_change { @latte_product_line.reload.product_buy_price }
            .and not_change { @latte_unit_conversion_line.reload.product_buy_price }
            .and not_change { @latte_custom_location_line.reload.product_buy_price }
            .and not_change { @spicy_burger_unit_conversion_line.reload.product_buy_price }
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and not_change { order_transaction_location_from_is_franchise.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise_2.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise_3.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise.reload.total_tax }
            .and not_change { order_transaction_location_from_is_franchise_2.reload.total_tax }
            .and not_change { order_transaction_location_from_is_franchise_3.reload.total_tax }

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Owned Location Parung",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8500},
                {:location_name=>"Owned Location Sudirman",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8330},
                {:location_name=>"Franchise Location Ciputat",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7676},
                {:location_name=>"Franchise Location Rawa Buaya",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7333}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take
            expect(log).to be_completed
            expect(log.order_ids).to be_blank

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to eq([])
          end
        end

        context 'when creating new product internal price locations and has existing orders, custom setting location (self product unit) and product unit conversion' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)

            latte_internal_price_franchise_1
            latte_unit_conversion_party_5_l.update_columns(internal_price: 45000)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(1).to(4)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @latte_unit_conversion_line.reload.product_buy_price }.from(1500).to(45000)
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(337500.0)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1535.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_tax }.from(300).to(9000)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1535.2)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Owned Location Parung",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8500},
                {:location_name=>"Owned Location Sudirman",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8330},
                {:location_name=>"Franchise Location Ciputat",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7676},
                {:location_name=>"Franchise Location Rawa Buaya",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7333}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders, and duplicated setting location and location internal price' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_setting_locations_attributes: [{"location_id"=>franchise_branch_1.id}],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)

            latte_setting_location_franchise_1
            latte_internal_price_franchise_1
            latte_unit_conversion_party_5_l.update_columns(internal_price: 45000)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(1).to(3)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @latte_unit_conversion_line.reload.product_buy_price }.from(1500).to(45000)
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(337500.0)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1535.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_tax }.from(300).to(9000)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1535.2)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Franchise Location Ciputat",
                :product_unit=>"cup 500 ml",
                :internal_price=>7676},
               {:location_name=>"Owned Location Parung",
                :product_unit=>"cup 500 ml",
                :internal_price=>8500},
               {:location_name=>"Owned Location Sudirman",
                :product_unit=>"cup 500 ml",
                :internal_price=>8330}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders, custom setting location (self product unit) and product unit conversion (but no price)' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)

            latte_internal_price_franchise_1
            latte_unit_conversion_party_5_l.update_columns(internal_price: nil)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(1).to(4)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7676)
            .and not_change { @latte_unit_conversion_line.reload.product_buy_price } # new one will be nil, so not changing
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(241800.0)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1535.2)
            .and not_change { order_transaction_location_from_is_franchise_2.reload.total_tax }
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1535.2)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Owned Location Parung",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8500},
                {:location_name=>"Owned Location Sudirman",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>8330},
                {:location_name=>"Franchise Location Ciputat",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7676},
                {:location_name=>"Franchise Location Rawa Buaya",
                  :product_unit=>"cup 500 ml",
                  :internal_price=>7333}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to be_nil
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders, custom setting location (different product unit)' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7676
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_party_5_l.id,
                      product_unit_id: party_5_l.id,
                      internal_price: 45000
                    },
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)
            latte_unit_conversion_party_5_l

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)

            latte_internal_price_franchise_1_party_cup_5_l
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(1).to(5)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @latte_unit_conversion_line.reload.product_buy_price }.from(1500).to(7676) # must use the custom location setting / higher hierarchy
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7676)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(255387.2)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27387.2)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1535.2)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_tax }.from(300).to(1535.2)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1535.2)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:location_name=>"Franchise Location Ciputat",
                :product_unit=>"party size 5 l",
                :internal_price=>7676},
               {:location_name=>"Owned Location Parung",
                :product_unit=>"cup 500 ml",
                :internal_price=>8500},
               {:location_name=>"Owned Location Sudirman",
                :product_unit=>"cup 500 ml",
                :internal_price=>8330},
               {:location_name=>"Franchise Location Ciputat",
                :product_unit=>"cup 500 ml",
                :internal_price=>7676},
               {:location_name=>"Franchise Location Rawa Buaya",
                :product_unit=>"cup 500 ml",
                :internal_price=>7333}]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders and custom location setting is deleting' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7676,
                      _destroy: true
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 7333,
                      _destroy: true
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_party_5_l.id,
                      product_unit_id: party_5_l.id,
                      internal_price: 45000
                    },
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 10000
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)
            latte_unit_conversion_party_5_l

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @latte_unit_conversion_line.update_columns(product_unit_id: party_5_l.id)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)
            @spicy_burger_unit_conversion_line.update_columns(product_unit_id: family_pack.id)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)
            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)
            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)
          end

          it 'creates internal prices correctly', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @owner_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_2_web_push_token,
              title: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              body: '1 order to Central Kitchen Location Pasar Jeruk location has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>
                     "%{count} order to %{location_to_name} location has a price change.",
                    "variables"=>
                     {"count"=>1,
                      "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect(Restaurant::Jobs::PushNotificationsJob)
            .to receive(:perform_later).with(
              web_push_token: @brand_owner_3_web_push_token,
              title: '2 orders to several locations has a price change.',
              body: '2 orders to several locations has a price change.',
              icon: nil,
              data: hash_including(
                {
                :active=>true,
                :brand_uuid=>nil,
                :large_icon=>nil,
                :icon=>nil,
                :location=>{},
                :payload=>
                 {:title=>
                   {"data"=>"%{count} orders to several locations has a price change.",
                    "variables"=>{"count"=>2}},
                  :description=>{:data=>nil},
                  :filters=>nil,
                  :resource_type=>"BulkUpdateInternalPriceLog",
                  :resource_id=>1}}
              )
            ).once

            expect do
              spicy_burger_unit_conversion_family_pack.update!(internal_price: 120_000)
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(0).to(2)
            .and change { latte.internal_price }.from(1500).to(7800)
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and change { @latte_product_line.reload.product_buy_price }.from(1500).to(7800)
            .and change { @latte_unit_conversion_line.reload.product_buy_price }.from(1500).to(45000)
            .and change { @latte_custom_location_line.reload.product_buy_price }.from(1500).to(7800)
            .and change { @spicy_burger_unit_conversion_line.reload.product_buy_price }.from(6000).to(120000)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }.from(0).to(1)
            .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13800).to(27660)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_amount }.from(13800).to(337500.0)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_amount }.from(13800).to(27660.0)
            .and change { order_transaction_location_from_is_franchise.reload.total_tax }.from(300).to(1560)
            .and change { order_transaction_location_from_is_franchise_2.reload.total_tax }.from(300).to(9000)
            .and change { order_transaction_location_from_is_franchise_3.reload.total_tax }.from(300).to(1560)

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [
                  {:internal_price=>8500, :location_name=>"Owned Location Parung", :product_unit=>"cup 500 ml"},
                  {:internal_price=>8330, :location_name=>"Owned Location Sudirman", :product_unit=>"cup 500 ml"}
                ]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(45000)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to match_array(
              [{:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                 {"data"=>
                   "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada lokasi %{location_to_name} memiliki perubahan harga.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "en"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "zh-CN"=>
                  {"data"=>
                    "发往 %{location_to_name} 地点的 %{count} 个订单价格有变动。",
                  "variables"=>
                    {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}}},
              :notification_type=>"completed_bulk_update_internal_price"},
             {:associated_id=>log.id,
              :associated_type=>"Restaurant::Models::BulkUpdateInternalPriceLog",
              :notif_message=>
               {"id"=>
                 {"data"=>
                   "%{count} pesanan kepada beberapa lokasi memiliki perubahan harga.",
                  "variables"=>{"count"=>2}},
                "en"=>
                 {"data"=>"%{count} orders to several locations has a price change.",
                  "variables"=>{"count"=>2}},
                "zh-CN"=>
                  {"data"=>"发往多个地点的 %{count} 个订单的价格发生了变化。",
                  "variables"=>{"count"=>2}}},
              :notification_type=>"completed_bulk_update_internal_price"}]
            )

            notif = Notification.last
            notification_presentation = NotificationHelper.build_notification_detail(notif, owner, nil)
            expect(notification_presentation.as_json(except: :click_action)).to eq(
              {"id"=>notif.id,
              "created_at"=>notif.created_at.as_json,
              "active"=>true,
              "brand_uuid"=>nil,
              "large_icon"=>nil,
              "icon"=>nil,
              "location"=>{},
              "payload"=>
               {"title"=>
                 {"data"=>
                   "%{count} order to %{location_to_name} location has a price change.",
                  "variables"=>
                   {"count"=>1,
                    "location_to_name"=>"Central Kitchen Location Pasar Jeruk"}},
                "description"=>{"data"=>nil},
                "filters"=>nil,
                "resource_type"=>"BulkUpdateInternalPriceLog",
                "resource_id"=>log.id}}
            )
          end
        end

        context 'when creating new product internal price locations and has existing orders, but param is same prices' do
          let(:is_immediate) { 'true' }
          let(:apply_to_pending_orders) { 'true' }
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 1500,
                  product_internal_price_locations_attributes: [
                    {
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8500,
                    },
                    {
                      location_id: owned_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 8330,
                    },
                    {
                      location_id: franchise_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 1500,
                    },
                    {
                      location_id: franchise_branch_2.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 1500,
                    }
                  ],
                  product_unit_conversions_attributes: [
                    {
                      id: latte_unit_conversion_party_5_l.id,
                      product_unit_id: party_5_l.id,
                      internal_price: 1500
                    },
                    {
                      id: latte_unit_conversion_cup_1_l.id,
                      product_unit_id: cup_1_l.id,
                      internal_price: 1500
                    },
                  ]
                },
                { id: cheese_burger.id, internal_price: 9500 }
              ]
            }
          end

          before do
            latte.update!(internal_tax: tax)
            latte_unit_conversion_party_5_l

            create(:notification_setting, brand: brand, user: brand_owner_2)
            create(:notification_setting, brand: brand, user: brand_owner_3)

            @owner_web_push_token = create(:restaurant_web_push_token, user: owner, brand: brand)
            @brand_owner_2_web_push_token = create(:restaurant_web_push_token, user: brand_owner_2, brand: brand)
            @brand_owner_3_web_push_token = create(:restaurant_web_push_token, user: brand_owner_3, brand: brand)

            # source is self-price but has new location setting above
            # location setting is updated/created, then order line update is processed later
            order_transaction_location_from_is_franchise.update_columns(created_by_id: owner.id)
            @latte_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: latte)
            @spicy_burger_product_line = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger)

            # source is product unit conversion
            order_transaction_location_from_is_franchise_2.update_columns(created_by_id: brand_owner_2.id)
            order_transaction_location_from_is_franchise_2.order_transaction_lines.update_all(
              product_unit_id: latte_unit_conversion_party_5_l.product_unit_id)

            @latte_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: latte)
            @spicy_burger_unit_conversion_line = order_transaction_location_from_is_franchise_2.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location
            order_transaction_location_from_is_franchise_3.update_columns(created_by_id: brand_owner_3.id)

            @latte_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line = order_transaction_location_from_is_franchise_3.order_transaction_lines.find_by(product: spicy_burger)

            # source is custom location also, but different location_to
            order_transaction_location_from_is_franchise_4.update_columns(created_by_id: brand_owner_3.id, location_to_id: central_kitchen_2.id)

            @latte_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: latte)
            @spicy_burger_custom_location_line_2 = order_transaction_location_from_is_franchise_4.order_transaction_lines.find_by(product: spicy_burger)
          end

          it 'creates internal prices correctly, but no notification of bulk update internal price', bullet: :skip do |example|
            expect(Restaurant::Jobs::PushNotificationsJob)
              .to_not receive(:perform_later)

            expect do
              submit_request(example.metadata)

              latte.reload
              cheese_burger.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(0).to(4)
            .and not_change { latte.internal_price }
            .and change { latte.product_unit_conversions.count }.from(1).to(2)
            .and change { cheese_burger.internal_price }.from(4500).to(9500)
            .and change { Restaurant::Models::BulkUpdateInternalPriceLog.count }
            .and not_change { cheese_burger.product_internal_price_locations.count }
            .and not_change { cheese_burger.product_unit_conversions.count }
            .and not_change { @latte_product_line.reload.product_buy_price }
            .and not_change { @latte_unit_conversion_line.reload.product_buy_price }
            .and not_change { @latte_custom_location_line.reload.product_buy_price }
            .and not_change { order_transaction_location_from_is_franchise.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise_2.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise_3.reload.total_amount }
            .and not_change { order_transaction_location_from_is_franchise.reload.total_tax }
            .and not_change { order_transaction_location_from_is_franchise_2.reload.total_tax }
            .and not_change { order_transaction_location_from_is_franchise_3.reload.total_tax }

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [
                  {:internal_price=>8500, :location_name=>"Owned Location Parung", :product_unit=>"cup 500 ml"},
                  {:internal_price=>8330, :location_name=>"Owned Location Sudirman", :product_unit=>"cup 500 ml"},
                  {:internal_price=>1500, :location_name=>"Franchise Location Ciputat", :product_unit=>"cup 500 ml"},
                  {:internal_price=>1500, :location_name=>"Franchise Location Rawa Buaya", :product_unit=>"cup 500 ml"}
                ]
              )

            latte_product_unit_conversion = latte.product_unit_conversions.first
            expect(latte_product_unit_conversion.converted_qty).to eq(10)
            expect(latte_product_unit_conversion.internal_price).to eq(1500)
            expect(latte_product_unit_conversion.product_unit.name).to eq('party size 5 l')

            log = Restaurant::Models::BulkUpdateInternalPriceLog.take

            expect(log).to be_completed

            notifications = Notification.all.map do |notif|
              { associated_id: notif.associated_id, associated_type: notif.associated_type,
                notif_message: JSON.parse(notif.notif_message),
                notification_type: notif.notification_type }
            end

            expect(notifications).to be_blank
          end
        end

        context 'when updating existing product internal price locations that has product variances that also has their own internal price locations' do
          before do
            latte
            latte_owned_branch_1_internal_price
            latte_variant_choco
            latte_variant_choco_owned_branch_1_internal_price
            latte_variant_mint
          end

          let(:param) do
            {
              products: [
                {
                  id: latte.id, internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      id: latte_owned_branch_1_internal_price.id,
                      location_id: owned_branch_1.id,
                      product_unit_id: latte.product_unit_id,
                      internal_price: 12500,
                    },
                  ]
                },
                {
                  id: cheese_burger.id, internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      id: cheese_burger_owned_branch_1_internal_price.id,
                      location_id: owned_branch_1.id,
                      product_unit_id: cheese_burger.product_unit_id,
                      internal_price: 44444,
                    },
                  ]
                }
              ]
            }
          end

          it 'updates internal prices parent & child variances products', bullet: :skip do |example|
            expect do
              submit_request(example.metadata)
            end.to not_change { latte.reload.product_internal_price_locations.count }
               .and not_change { latte_variant_choco.reload.product_internal_price_locations.count }
               .and change { latte_owned_branch_1_internal_price.reload.internal_price }.from(5000).to(12500)
               .and change { latte_variant_choco_owned_branch_1_internal_price.reload.internal_price }.from(5000).to(12500) # update existing
               .and change { latte_variant_mint.reload.product_internal_price_locations.count }.from(0).to(1)
               .and change { cheese_burger_owned_branch_1_internal_price.reload.internal_price }.from(23333).to(44444) # no variant

            assert_response_matches_metadata(example.metadata)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
              .to match_array(
                [{:internal_price=>12500, :location_name=>"Owned Location Parung", :product_unit=>"cup 500 ml"}]
              )

            # newly created for existing variant that has no product_internal_price_locations
            latte_mint_internal_price_location = latte_variant_mint.reload.product_internal_price_locations.first
            expect(latte_mint_internal_price_location.internal_price).to eq(12500)
            expect(latte_mint_internal_price_location.location).to eq(owned_branch_1)
          end
        end

        context 'when destroying a product internal price location and product setting location', search: true, bullet: :skip do
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      id: latte_owned_branch_1_internal_price.id,
                      _destroy: true
                    }
                  ],
                  product_setting_locations_attributes: [{ "id": latte_setting_location_ck.id, "location_id"=>franchise_branch_1.id, _destroy: true }]
                }
              ]
            }
          end

          before do
            latte_owned_branch_1_internal_price
            latte_owned_branch_2_internal_price
            latte_setting_location_ck
          end

          it 'deletes location internal prices' do |example|
            expect do
              submit_request(example.metadata)

              latte.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(2).to(1)
            .and change { latte.product_setting_locations.count }.from(1).to(0)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
            .to match_array(
              [{:internal_price=>5000, :location_name=>"Owned Location Sudirman", :product_unit=>"cup 500 ml"}]
            )

            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when destroying a product internal price location and product setting location, but previously destroyed (race condition)', bullet: :skip do
          let(:param) do
            {
              products: [
                {
                  id: latte.id,
                  internal_price: 7800,
                  product_internal_price_locations_attributes: [
                    {
                      id: latte_owned_branch_1_internal_price.id
                    },
                    {
                      id: latte_owned_branch_2_internal_price.id,
                      _destroy: true
                    },
                    { location_id: owned_branch_3.id, internal_price: 8000, product_unit_id: latte.product_unit_id }
                  ],
                  product_setting_locations_attributes: [
                    { id: latte_setting_location_ck.id, location_id: franchise_branch_1.id, _destroy: true },
                    { location_id: franchise_branch_2.id },
                    { id: latte_setting_location_owned_1.id, location_id: owned_branch_1.id },
                  ]
                }
              ]
            }
          end

          before do
            latte_owned_branch_1_internal_price
            latte_owned_branch_2_internal_price.update_columns(deleted: true)
            latte_setting_location_ck.update_columns(deleted: true)
            latte_setting_location_owned_1
          end

          it 'deletes location internal prices and product setting location correctly' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              latte.reload
            end
            .to change { latte.product_internal_price_locations.count }.from(1).to(2)
            .and change { latte.product_setting_locations.count }.from(1).to(2)

            expect(latte.product_internal_price_locations.map { |product_internal_price_location| { location_name: product_internal_price_location.location.name, product_unit: product_internal_price_location.product_unit.name, internal_price: product_internal_price_location.internal_price } })
            .to match_array(
              [{:location_name=>"Owned Location Parung",
              :product_unit=>"cup 500 ml",
              :internal_price=>5000},
             {:location_name=>"Owned Location Sukamulya",
              :product_unit=>"cup 500 ml",
              :internal_price=>8000}]
            )

            product_setting_locations = latte.product_setting_locations.as_json(only: :location_id)
            expect(product_setting_locations).to match_array(
              [
                {"location_id"=>owned_branch_1.id}, {"location_id"=>franchise_branch_2.id},
              ]
            )
          end
        end
      end
    end
  end

  path '/api/products/bulk_update_option_sets' do
    patch('bulk_update_option_sets product') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_product_option_set'
      }
      response(204, 'successful') do
        let(:param) do
          { product: build(:product_params, owner_location_id: central_kitchen.id,
                                            product_option_sets_attributes: [{ product_id: product.id, option_set_id: option_set.id }]) }
        end

        before do |example|
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response', bullet: :skip do |example|
          assert_response_matches_metadata(example.metadata)
        end
      end

      response(400, 'invalid params') do
        context 'blank product_option_sets_attributes' do
          let(:param) do
            { product: build(:product_params, owner_location_id: central_kitchen.id,
                                              product_option_sets_attributes: []) }
          end

          before do |example|
            submit_request(example.metadata)
          end

          it 'returns a valid 204 response', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"message"=>"Product option sets cannot be empty"}
            )
          end
        end
      end
    end
  end
end
