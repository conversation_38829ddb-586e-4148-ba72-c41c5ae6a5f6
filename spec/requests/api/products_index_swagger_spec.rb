require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/swagger'
require './spec/shared/upload_url'
require './spec/shared/procurements'
require './spec/shared/wastes'
require './spec/shared/order_transaction_invoices'
require './spec/shared/stock_adjustments'
require './spec/shared/stock_openings'
require './spec/shared/disassemble_transactions'
require './spec/shared/sub_brands'
require './spec/shared/customers'
require './spec/shared/procurements'
require './spec/shared/vendor_products'
require './spec/shared/product_groups'
require './spec/shared/courses'

RSpec.describe 'api/products', type: :request do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'swagger after response'
  include_context 'procurements creations'
  include_context 'wastes creations'
  include_context 'order transaction invoices creations'
  include_context 'stock adjustments creations'
  include_context 'disassemble transactions creations'
  include_context 'stock openings creation'
  include_context 'sub brands creations'
  include_context 'customers creations'
  include_context 'procurements creations'
  include_context 'vendor products creations'
  include_context 'product group creations'

  # cleaning up
  before(:all) do
    Product.reindex
  end

  before(:each) do
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:another_brand) { create(:brand, name: 'another brand') }

  let(:user_access_list) do
    location_user = LocationsUser.find_by(location: central_kitchen, user: user)
    location_user.access_list
  end

  let(:online_ordering_order_type) { create(:online_ordering_order_type, online_platform_fee: 550) }
  let(:custom_order_type) { create(:order_type, name: 'sample', brand: brand, online_platform_fee: 550) }

  let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product_unit_2) { create(:product_unit, brand: brand, name: 'liter') }

  let(:product_unit_3) { create(:product_unit, brand: brand, name: 'galon') }

  let(:product_unit_4) { create(:product_unit, brand: brand, name: 'galon jumbo') }

  let(:product_unit_different_brand) { create(:product_unit, brand: another_brand, name: 'galon') }

  let(:other_product_unit) { create(:product_unit, brand: brand, name: 'Kg') }
  let(:product) do
    create(:product, brand: brand, product_unit: product_unit, location_ids: [owned_branch_1.id], owner_location_id: central_kitchen.id, tax: tax,
                     internal_tax: tax, exclude_location_ids: [owned_branch_2.id])
  end
  let(:product_2) { create(:product, brand: brand, product_unit: product_unit, owner_location_id: central_kitchen.id) }
  let(:product_3) { create(:product, brand: brand, product_unit: product_unit, owner_location_id: central_kitchen.id) }
  let(:product_4) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [central_kitchen.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end
  let(:product_5) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end
  let(:product_6) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [central_kitchen.id, owned_branch_2.id],
                     owner_location_id: central_kitchen.id, product_unit: product_unit)
  end
  let(:product_7) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], owner_location_id: central_kitchen.id,
                     product_unit: product_unit)
  end

  let(:product_setting_location) { create(:product_setting_location, product: product_4, location: central_kitchen) }
  let(:product_modifier) { create(:product, :modifier, brand: brand, product_unit: product_unit) }
  let(:product_variance) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }
  let(:product_2_variance) { create(:product, variance_parent_product_id: product_2.id, brand: brand, product_unit: product_unit) }
  let(:product_owned_branch_2) do
    create(:product, brand: brand, is_select_all_location: false, location_ids: [owned_branch_2.id], product_unit: product_unit)
  end
  let(:product_unit_conversion) { create(:product_unit_conversion, product: product, product_unit: product_unit_3) }
  let(:product_unit_conversion_2) { create(:product_unit_conversion, product: product, product_unit: product_unit_2) }

  let(:procurement_unit_1) do
    create(:procurement_unit, product: product, product_unit: product_unit)
  end
  let(:procurement_unit_2) do
    create(:procurement_unit, product: product, product_unit: product_unit_2)
  end

  let(:product_with_unit_conversion_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: other_product_unit.id)])
  end
  let(:product_with_unit_conversion_params_no_price) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: other_product_unit.id, internal_price: 0)])
  end
  let(:product_with_unit_conversions) do
    create(:product, brand: brand, product_unit: product_unit, product_unit_conversions: [
             build(:product_unit_conversion, product_unit: other_product_unit)
           ])
  end

  let(:product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:product_with_procurement_units_smallest_only) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit.id }])
  end

  let(:product_with_procurement_units_params_with_smallest) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }, { product_unit_id: product_unit.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:blank_procurement_units) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:blank_procurement_units_and_conversion_units) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id)
  end

  let(:partially_valid_product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }, { product_unit_id: product_unit_3.id }, { product_unit_id: product_unit_4.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:invalid_product_with_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_3.id, internal_price: 0)])
  end

  let(:invalid_product_with_procurement_units_params_blank_unit_conversions) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           procurement_units_attributes: [{ product_unit_id: product_unit_2.id }])
  end

  let(:product_with_outlet_to_outlet_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           outlet_to_outlet_procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:product_with_vendor_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           vendor_procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:product_with_central_kitchen_procurement_units_params) do
    build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id,
                           central_kitchen_procurement_units_attributes: [{ product_unit_id: product_unit_2.id }],
                           product_unit_conversions_attributes: [build(:product_unit_conversion_params, product_unit_id: product_unit_2.id, internal_price: 0)])
  end

  let(:pos_product_layout) { create(:product_layout, location_id: central_kitchen.id, payload: { products: [{ id: product.id, sequence: 1 }] }) }
  let(:option_set) { create(:option_set, brand: brand) }
  let(:product_option_set) { create(:product_option_set, product_id: product.id, option_set_id: option_set.id) }
  let(:create_setting_params) { build(:product_setting_location_params, par_unit_id: product_unit.id, location_id: central_kitchen.id) }

  let(:grab_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :grab_food,
      location: central_kitchen,
      partner_outlet_id: 'G6199595402'
    )
  end

  let(:go_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :go_food,
      location: central_kitchen,
      partner_outlet_id: 'G619959540',
    )
  end

  let(:shopee_food_delivery_integration) do
    create(
      :food_delivery_integration,
      :shopee_food,
      location: central_kitchen,
      partner_outlet_id: '20667332'
    )
  end

  let(:product_keys) do
    [
      "id", "name", "sku", "upc", "description", "internal_price", "sell_price", "status", "modifier", "no_stock", "internal_distribution_type",
      "external_vendor_type", "internal_produce_type", "out_of_stock_flag", "available_stock_flag", "variance_parent_product_id",
      "option_set_auto_prompt","allow_custom_sell_price", "image_url", "original_image_url", "owner_location", "sell_to_customer_type", 'sell_to_pos', 'sell_to_kiosk',
      "sell_to_dine_in", "sell_to_grab_food", "sell_to_go_food", "sell_to_shopee_food", "sell_to_online_ordering", "sell_to_procurement_from_customer", "internal_tax", "tax",
      "sell_tax", "sell_tax_setting", "product_category", "print_category", "product_unit", "back_office_unit", "sell_unit", "par_unit", "par_quantity",
      "is_select_all_location", "locations", "exclude_location_ids", "exclude_locations", "location_type", "variances", "product_option_sets",
      "product_setting_locations", "product_internal_price_locations", "product_price_per_order_types", "product_unit_conversions",
      "product_procurement_units", "outlet_to_outlet_procurement_units", "vendor_procurement_units", "price_per_order_global",
      "price_per_order_location", "pos_product_layout", "pos_product_layout_per_sub_brand", "product_internal_price_table_units", "vendor_products",
      "exclude_location_group_ids", "exclude_location_groups", "is_select_all_location_group", "location_groups",
      "product_groups", "product_category_group", "central_kitchen_procurement_units", "outlet_back_office_unit",
      "central_kitchen_back_office_unit", "order_price_editing_by_franchisor"
    ]
  end

  let(:variance_keys) do
    [
      "id", "name", "sku", "upc", "description", "internal_price", "sell_price", "status", "modifier", "no_stock",
      "internal_distribution_type", "external_vendor_type", "internal_produce_type", "out_of_stock_flag",
      "available_stock_flag", "variance_parent_product_id", "option_set_auto_prompt", "allow_custom_sell_price",
      "image_url", "original_image_url", "owner_location", "sell_to_customer_type", "sell_to_dine_in", 'sell_to_pos', 'sell_to_kiosk',
      "sell_to_grab_food", "sell_to_go_food", "sell_to_shopee_food", "sell_to_online_ordering", "sell_to_procurement_from_customer", "internal_tax",
      "tax", "sell_tax", "sell_tax_setting", "product_category", "print_category", "product_unit", "back_office_unit", "sell_unit",
      "par_unit", "price_per_order_global", "price_per_order_location", "product_option_sets", "par_quantity",
      "product_procurement_units", "product_unit_conversions", "outlet_to_outlet_procurement_units",
      "vendor_procurement_units", "product_internal_price_table_units", "vendor_products", "product_category_group",
      "central_kitchen_procurement_units", "outlet_back_office_unit", "central_kitchen_back_office_unit", "order_price_editing_by_franchisor"
    ]
  end

  let(:simple_variance_keys) do
    [
      "id", "name", "sku", "upc", "description", "sell_price", "modifier", "version", "product_groups",
      "out_of_stock_flag", "available_stock_flag",  "allow_custom_sell_price", "image_url",
      "tax", "sell_tax", "sell_tax_setting", "product_category", "print_category", "product_unit", "back_office_unit",
      "internal_price", "price_per_order_global", "price_per_order_location", "product_option_sets", "product_procurement_units"
    ]
  end

  path '/api/products/group_by_category', search: true do
    get 'List of products group by category' do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :skip_location, in: :query, type: :string, required: false
      parameter name: :any_external_vendor_type, in: :query, type: :string, required: false
      parameter name: :no_stock, in: :query, type: :string, required: false
      parameter name: :internal_distribution_type, in: :query, type: :string, required: false
      parameter name: :external_vendor_type, in: :query, type: :string, required: false
      parameter name: :sell_to_customer_type, in: :query, type: :string, required: false
      parameter name: :sell_procurement_from_customer, in: :query, type: :string, required: false
      parameter name: :exclude_product_with_variances, in: :query, type: :string, required: false
      parameter name: :recipe_line_product, in: :query, type: :string, required: false
      parameter name: :keyword, in: :query, type: :string, required: false
      parameter name: :exclude_ids, in: :query, type: :string, required: false
      parameter name: :status, in: :query, type: :string, enum: Product.statuses.keys, required: false
      parameter name: :sort_key, in: :query, type: :string, enum: ['asc', 'desc'], required: false
      parameter name: :sort_order, in: :query, type: :string, enum: ['product_category_name'], required: false
      parameter name: :exclude_category_ids, in: :query, type: :string, required: false
      parameter name: :internal_produce_type, in: :query, type: :string, required: false
      parameter name: :only_contain_recipe, in: :query, type: :string, required: false
      parameter name: :recipe_type, in: :query, type: :string, required: false
      parameter name: :skip_child_variances, in: :query, type: :string, required: false
      parameter name: :recipe_status, in: :query, type: :string, required: false

      response(200, 'successful') do
        context 'when filter location_id CK & status activated' do
          let!(:location_id) { central_kitchen.id }
          let(:status) { 'activated' }

          before do |example|
            deactivated_coffee
            activated_product
            uncategorized_product

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should return active product id with category from CK and return uncategorized product count' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            response_categories = response_body['data']['product_categories']

            expect(response_categories.map { |group| group['id'] }).to match_array([activated_product.product_category_id])
            expect(response_body['data']['no_category_count']).to eq 1

            response_product_ids = response_categories.map { |group| group['product_ids'] }.flatten
            expect(response_product_ids).to match_array([activated_product.id])
          end
        end

        context 'when filter location_id CK & location_to_id outlet' do
          let(:status) { 'activated' }
          let!(:location_id) { central_kitchen.id }
          let!(:location_to_id) { owned_branch_1.id }
          let(:product_in_ck_only) do
            create(:product, owner_location_id: central_kitchen.id, name: 'Product CK only', sku: 'product-ck-only', brand: brand, product_category: burgers_category, product_unit: cup_300_ml, internal_price: 2500, sell_price: 2800, status: :activated, is_select_all_location: false, location_ids: [central_kitchen.id])
          end
          let(:product_in_outlet_only) do
            create(:product, owner_location_id: central_kitchen.id, name: 'Product Outlet only', sku: 'product-outlet-only', brand: brand, product_category: coffee_drinks_category, product_unit: cup_300_ml, internal_price: 2500, sell_price: 2800, status: :activated, is_select_all_location: false, location_ids: [owned_branch_1.id])
          end
          let(:product_in_ck_and_outlet) do
            create(:product, owner_location_id: central_kitchen.id, name: 'Product CK & Outlet', sku: 'product-ck-and-outlet', brand: brand, product_category: tea_category, product_unit: cup_300_ml, internal_price: 2500, sell_price: 2800, status: :activated, is_select_all_location: false, location_ids: [central_kitchen.id, owned_branch_1.id])
          end

          before do |example|
            product_in_ck_only
            product_in_outlet_only
            product_in_ck_and_outlet

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should return active product id with category from both CK & outlet' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            response_categories = response_body['data']['product_categories']

            expect(response_body['data']['no_category_count']).to eq 0
            expect(response_categories.map { |group| group['id'] }).to match_array([product_in_ck_and_outlet.product_category_id])

            response_product_ids = response_categories.map { |group| group['product_ids'] }.flatten
            expect(response_product_ids).to match_array([product_in_ck_and_outlet.id])
          end
        end

        context 'when filter by product internal_distribution_type truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:no_stock) { 'false' }
          let(:internal_distribution_type) { 'true' }
          let(:page) { 1 }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger

            deactivated_product_category
            deactivated_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns category that have product type internal_distribution_type' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_ids = response_body['data']['product_categories'].map{ |x| x['id'] }
            expect(response_ids).to eq [burgers_category.id, deactivated_product_category.id]
          end
        end

        context 'when has uncategorized product' do
          let(:location_id) { owned_branch_1.id }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger

            deactivated_product_category
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns uncategorized category' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']

            data_response = response_body['data']
            expect(data_response.keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_ids = data_response['product_categories'].map{ |x| x['id'] }
            expect(data_response['no_category_count']).to eq 1
            expect(response_ids).to match_array [coffee_drinks_category.id, burgers_category.id, deactivated_product_category.id]

            expect(data_response['no_category_product_ids']).to match_array([uncategorized_product.id])
          end
        end

        context 'when filter by product external_vendor_type truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:no_stock) { 'false' }
          let(:external_vendor_type) { 'true' }
          let(:page) { 1 }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            deactivated_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns category that have product type external_vendor_type' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_ids = response_body['data']['product_categories'].map{ |x| x['id'] }
            expect(response_body['data']['no_category_count']).to eq 0
            expect(response_ids).to eq [burgers_category.id, deactivated_product_category.id]
          end
        end

        context 'when filter by product sell_to_customer_type truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:no_stock) { 'false' }
          let(:sell_to_customer_type) { 'true' }
          let(:page) { 1 }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            deactivated_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns category that have product type sell_to_customer_type' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_ids = response_body['data']['product_categories'].map{ |x| x['id'] }
            expect(response_body['data']['no_category_count']).to eq 0
            expect(response_ids).to eq [coffee_drinks_category.id, deactivated_product_category.id]
          end
        end

        context 'when has uncategorized and sort by product category name asc' do
          let(:location_id) { owned_branch_1.id }
          let(:sort_order) { 'asc' }
          let(:sort_key) { 'product_category_name' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            # NOTE: Why upcase? PSQL sort lower case differently, depends on our local / server PostgreSQL LC_COLLATE configuration.
            deactivated_product_category.update(name: deactivated_product_category.name.upcase)
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_names = response_body['data']['product_categories'].map{ |x| x['name'] }
            expect(response_body['data']['no_category_count']).to eq 1
            expect(response_names).to eq [burgers_category.name, coffee_drinks_category.name, deactivated_product_category.name]
          end
        end

        context 'when has uncategorized and sort by product category name desc' do
          let(:location_id) { owned_branch_1.id }
          let(:sort_order) { 'desc' }
          let(:sort_key) { 'product_category_name' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            # NOTE: Why upcase? PSQL sort lower case differently, depends on our local / server PostgreSQL LC_COLLATE configuration.
            deactivated_product_category.update(name: deactivated_product_category.name.upcase)
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns categories sorted descendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_names = response_body['data']['product_categories'].map{ |x| x['name'] }
            expect(response_body['data']['no_category_count']).to eq 1
            expect(response_names).to eq [deactivated_product_category.name, coffee_drinks_category.name, burgers_category.name]
          end
        end

        context 'when has categories and filter by exclude_category_ids' do
          let(:location_id) { owned_branch_1.id }
          let(:exclude_category_ids) { [coffee_drinks_category.id].join(',') }
          let(:sort_order) { 'desc' }
          let(:sort_key) { 'product_category_name' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            uncategorized_product

            owned_branch_1

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should not return excluded category' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['data']
            expect(response_body['data'].keys).to match_array ['product_categories', 'no_category_count', 'no_category_product_ids']
            response_names = response_body['data']['product_categories'].map{ |x| x['name'] }
            expect(response_names).to_not match_array [coffee_drinks_category.name]
          end
        end

        context 'when outlet to vendor, with vendor product' do
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }
          let(:skip_location) { 'true' }

          before do |example|
            spicy_burger
            cheese_burger
            paper_bag

            vendor_product_all_locations_latte
            vendor_product_all_locations_soursop_juice

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"data"=>
              {"product_categories"=>
                [{"id"=>burgers_category.id, "name"=>"Burgers", "count"=>2, "product_ids"=>[cheese_burger.id, spicy_burger.id]},
                 {"id"=>coffee_drinks_category.id, "name"=>"Coffee Drinks", "count"=>1, "product_ids"=>[latte.id]},
                 {"id"=>juice_category.id, "name"=>"Juice", "count"=>1, "product_ids"=>[soursop_juice.id]}],
               "no_category_product_ids"=>[paper_bag.id],
               "no_category_count"=>1}}
            )
          end
        end
      end
    end
  end

  path '/api/products/upload_url' do
    post('upload_url product') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          product: {
            type: :object
          },
          presigned_url: {
            type: :object
          }
        }
      }

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_upload_url'

        let(:params) do
          {
            product: build(:product_params, product_unit_id: product_unit.id, product_setting_locations_attributes: [create_setting_params]),
            presigned_url: { filename: 'random-file-name.png', content_type: 'image/png', content_length: 15.megabyte }
          }
        end

        before do |example|
          submit_request(example.metadata)
        end

        it_should_behave_like 'upload url response'
      end

      response 422, 'unprocessable entity', document: false do
        let(:params) { { presigned_url: { filename: 'test', content_type: 'image/svg', content_length: 20 } } }

        before do |example|
          submit_request(example.metadata)
        end

        it 'returns a valid 422 response' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq('content_type can only be one of image/png,image/jpeg,image/jpg')
        end
      end
    end
  end

  path '/api/products', search: true do
    get('list products') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :ids, in: :query, type: :string, required: false
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :location_group_id, in: :query, type: :string, required: false
      parameter name: :location_from_ids, in: :query, type: :string, required: false, description: "comma separated numbers"
      parameter name: :all_location_froms, in: :query, type: :string, required: false
      parameter name: :include_variance, in: :query, type: :string, required: false
      parameter name: :show_pos_position, in: :query, type: :string, required: false
      parameter name: :exclude_used_in_recipe_line, in: :query, type: :string, required: false
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :status, in: :query, type: :string, required: false
      parameter name: :only_contain_recipe, in: :query, type: :string, required: false
      parameter name: :recipe_status, in: :query, type: :string, required: false
      parameter name: :recipe_type, in: :query, type: :string, required: false
      parameter name: :group_category, in: :query, type: :string, required: false
      parameter name: :include_variance, in: :query, type: :string, required: false
      parameter name: :exclude_product_with_variances, in: :query, type: :string, required: false
      parameter name: :out_of_stock_flag, in: :query, type: :string, required: false
      parameter name: :external_vendor_type, in: :query, type: :string, required: false
      parameter name: :any_external_vendor_type, in: :query, type: :string, required: false
      parameter name: :vendor_id, in: :query, type: :string, required: false
      parameter name: :internal_distribution_type, in: :query, type: :string, required: false
      parameter name: :sell_to_customer_type, in: :query, type: :string, required: false
      parameter name: :internal_produce_type, in: :query, type: :string, required: false
      parameter name: :recipe_line_product, in: :query, type: :string, required: false
      parameter name: :show_pos_position, in: :query, type: :string, required: false
      parameter name: :wbo, in: :query, type: :string, required: false
      parameter name: :category_ids, in: :query, type: :string, required: false
      parameter name: :exclude_categories_id, in: :query, type: :string, required: false
      parameter name: :exclude_ids, in: :query, type: :string, required: false
      parameter name: :presentation, in: :query, type: :string, enum: ProductQuery::PRODUCT_PRESENTATIONS.keys, required: false
      parameter name: :sell_to_pos, in: :query, type: :string, required: false
      parameter name: :sell_to_kiosk, in: :query, type: :string, required: false
      parameter name: :sell_to_dine_in, in: :query, type: :string, required: false
      parameter name: :sell_to_grab_food, in: :query, type: :string, required: false
      parameter name: :sell_to_go_food, in: :query, type: :string, required: false
      parameter name: :sell_to_shopee_food, in: :query, type: :string, required: false
      parameter name: :sell_to_online_ordering, in: :query, type: :string, required: false
      parameter name: :sell_to_procurement_from_customer, in: :query, type: :string, required: false
      parameter name: :sell_to_procurement, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_procurement, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_pos, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_grab_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_go_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_shopee_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_online_ordering, in: :query, type: :boolean, required: false
      parameter name: :exclude_used_in_recipe_line, in: :query, type: :string, required: false
      parameter name: :no_stock, in: :query, type: :string, required: false
      parameter name: :skip_child_variances, in: :query, type: :string, required: false
      parameter name: :variance_presentations, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_variance_presentations' }
      parameter name: :sort_key, in: :query, type: :string, enum: ['asc'], required: false
      parameter name: :sort_order, in: :query, type: :string, enum: ['product_category_name', 'sku', 'name'], required: false
      parameter name: :filter_by_sub_brand, in: :query, type: :string, required: false
      parameter name: :procurement_from_customer, in: :query, type: :string, required: false
      parameter name: :allow_procurement_out_of_stock_flag, in: :query, type: :string, required: false
      parameter name: :exclude_category_ids, in: :query, type: :string, required: false
      parameter name: :show_permissions, in: :query, type: :string, required: false
      parameter name: :show_detail_course, in: :query, type: :string, required: false, enum: ['false', 'true']
      parameter name: :check_empty_stock, in: :query, type: :string, required: false
      parameter name: :is_select_all_location, in: :query, type: :string, required: false
      parameter name: :keyword, in: :query, type: :string, required: false

      response(200, 'successful') do
        let!(:location_id) { owned_branch_1.id }
        let(:show_pos_position) { 'true' }

        context 'when send show_detail_course true' do
          include_context 'courses creations'

          let(:show_detail_course) { 'true' }

          before do
            latte
            cheese_burger

            course_1

            Product.search_index.refresh
          end

          it 'should returns alid 200 response' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['products', 'paging']

            paging = response_body['paging']
            expect(paging).to eq({
              "current_page" => 1,
              "next_page" => nil,
              "prev_page" => nil,
              "total_item" => 2
            })

            products = response_body['products']
            expect(products.size).to eq(2)

            product = products.detect { |obj| obj['id'] == latte.id}
            expect(product.keys).to eq([
              "id", "name", "sku", "upc", "description", "internal_price", "sell_price",
              "status", "modifier", "no_stock", "internal_distribution_type", "external_vendor_type",
              "internal_produce_type", "out_of_stock_flag", "available_stock_flag", "variance_parent_product_id",
              "option_set_auto_prompt", "allow_custom_sell_price", "image_url", "original_image_url",
              "owner_location", "sell_to_customer_type", "sell_to_pos", "sell_to_kiosk", "sell_to_dine_in", "sell_to_grab_food",
              "sell_to_go_food", "sell_to_shopee_food", "sell_to_online_ordering",
              "sell_to_procurement_from_customer", "vendor_products", "order_price_editing_by_franchisor",
              "internal_tax", "tax", "sell_tax", "sell_tax_setting", "product_category", "product_category_group",
              "print_category", "product_unit", "back_office_unit", "outlet_back_office_unit",
              "central_kitchen_back_office_unit", "sell_unit", "par_unit", "par_quantity",
              "is_select_all_location", "locations", "exclude_location_ids", "exclude_locations",
              "location_type", "is_select_all_location_group", "location_groups", "exclude_location_group_ids",
              "exclude_location_groups", "product_groups", "product_option_sets", "product_setting_locations",
              "product_internal_price_locations", "product_price_per_order_types", "product_unit_conversions",
              "product_procurement_units", "outlet_to_outlet_procurement_units", "vendor_procurement_units",
              "central_kitchen_procurement_units", "product_internal_price_table_units", "price_per_order_global",
              "price_per_order_location", "pos_product_layout", "pos_product_layout_per_sub_brand", "course"
            ])
            expect(product['id']).to eq(latte.id)
            expect(product['name']).to eq(latte.name)
            expect(product['sku']).to eq(latte.sku)
            expect(product['upc']).to eq(latte.upc)
            expect(product['course']).to eq({ 'id' => course_1.id, 'name' => course_1.name })

            product = products.detect { |obj| obj['id'] == cheese_burger.id}
            expect(product['id']).to eq(cheese_burger.id)
            expect(product['name']).to eq(cheese_burger.name)
            expect(product['sku']).to eq(cheese_burger.sku)
            expect(product['upc']).to eq(cheese_burger.upc)
            expect(product['course']).to eq(nil)
          end
        end

        context 'when using simple pos query' do
          context 'when has option set and product procurement units' do
            before do |example|
              Flipper.enable :simple_pos_product_query
              allow_any_instance_of(Api::ProductsController).to receive(:request_from_pos?).and_return(true)
              @product_for_option_set = product_option_set.option_set.option_set_options.first.product
              @product_for_option_set.update(sell_to_customer_type: true, sell_to_pos: true)

              product
              product_2
              product_3

              Product.search_index.refresh

              submit_request(example.metadata)
            end

            after do
              Flipper.disable :simple_pos_product_query
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response).to have_http_status(:ok)
              expect(response_body.keys).to match_array ['products', 'paging']
              response_product = response_body['products'].find { |p| p['id'] === product.id }
              expect(response_product['internal_price']).to_not eq 0
              expect(response_body['paging']['next_page']).to be_falsey
              expect(response_body['paging']['prev_page']).to be_falsey

              product_response = response_body['products'].map do |product|
                { id: product['id'], pos_product_layout: product['pos_product_layout'] }
              end
              expect(product_response).to match_array(
                [{ id: product.id, pos_product_layout: 9999 },
                 { id: product_2.id, pos_product_layout: 9999 },
                 { id: product_3.id, pos_product_layout: 9999 },
                 { id: @product_for_option_set.id, pos_product_layout: 9999 }]
              )

              expect(response_product['product_option_sets'].length).to eq(1)
              expect(response_product['product_option_sets'].first['product_id']).to eq(product.id)
              expect(response_product['product_option_sets'].first['option_set']['option_set_options'].first['product_id']).to eq(@product_for_option_set.id)

              product_procurement_units = response_product['product_procurement_units']
              expect(product_procurement_units.count).to eq(1)
              expect(product_procurement_units[0]['product_unit']['id']).to eq(product_unit.id)
              expect(product_procurement_units[0]['product_unit']['name']).to eq(product_unit.name)
            end
          end

          context 'when product variance has option set' do
            let(:product_variance_option_set) { create(:product_option_set, product_id: product_variance.id, option_set_id: option_set.id) }
            let(:product_variance_2) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }
            let(:include_variance) { 'true' }

            before do |example|
              Flipper.enable :simple_pos_product_query
              allow_any_instance_of(Api::ProductsController).to receive(:request_from_pos?).and_return(true)
              @product_for_option_set = product_variance_option_set.option_set.option_set_options.first.product
              @product_for_option_set.update(sell_to_customer_type: true, sell_to_pos: true)

              product
              product_variance
              product_variance_2
              product_2

              Product.search_index.refresh

              submit_request(example.metadata)
            end

            after do
              Flipper.disable :simple_pos_product_query
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response).to have_http_status(:ok)
              expect(response_body.keys).to match_array ['products', 'paging']
              response_product = response_body['products'].find { |p| p['id'] === product.id }
              expect(response_product['internal_price']).to_not eq 0
              expect(response_body['paging']['next_page']).to be_falsey
              expect(response_body['paging']['prev_page']).to be_falsey

              product_response = response_body['products'].map do |product|
                { id: product['id'], pos_product_layout: product['pos_product_layout'] }
              end
              expect(product_response).to match_array(
                [{ id: product.id, pos_product_layout: 9999 },
                 { id: product_2.id, pos_product_layout: 9999 },
                 { id: @product_for_option_set.id, pos_product_layout: 9999 }]
              )

              expect(response_body['products'].first['variances'].length).to eq(2)
              expect(response_body['products'].first['variances'].first.keys).to match_array(simple_variance_keys)
              expect(response_body['products'].first['variances'].first['product_option_sets'].length).to eq(1)
              expect(response_body['products'].first['variances'].first['product_option_sets'].first['option_set']['option_set_options'].first['product_id']).to eq(@product_for_option_set.id)
              expect(response_body['products'].first['variances'].first['product_groups']).to eq([])
              expect(response_body['products'].first['variances'].second.keys).to match_array(simple_variance_keys)
              expect(response_body['products'].first['variances'].second['product_option_sets'].length).to eq(0)
              expect(response_body['products'].first['variances'].second['product_groups']).to eq([])
            end
          end

          context 'when sell to pos is true' do
            let(:product_variance_2) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }
            let(:include_variance) { 'true' }

            before do
              Flipper.enable :simple_pos_product_query
              allow_any_instance_of(Api::ProductsController).to receive(:request_from_pos?).and_return(true)

              product.update(sell_to_pos: true)
              product_variance.update(sell_to_pos: true, sequence: 1)
              product_variance_2.update(sell_to_pos: true, sequence: 2)
              product_2.update(sell_to_pos: true)

              Product.search_index.refresh
            end

            after do
              Flipper.disable :simple_pos_product_query
            end

            context 'when product variance sell_to_pos = false' do
              before do
                product_variance_2.update(sell_to_pos: false)

                Product.search_index.refresh
              end

              context 'when send sell_to_pos query = true' do
                let(:sell_to_pos) { 'true' }

                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(1)
                  expect(variances[0]['id']).to eq(product_variance.id)
                  expect(variances[0]['name']).to eq(product_variance.name)

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end

              context 'when send sell_to_pos not send' do
                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(2)
                  expect(variances[0]['id']).to eq(product_variance.id)
                  expect(variances[0]['name']).to eq(product_variance.name)
                  expect(variances[1]['id']).to eq(product_variance_2.id)
                  expect(variances[1]['name']).to eq(product_variance_2.name)

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end
            end

            context 'when all product variance sell_to_pos = false' do
              before do
                product_variance.update(sell_to_pos: false)
                product_variance_2.update(sell_to_pos: false)

                Product.search_index.refresh
              end

              context 'when send sell_to_pos query = true' do
                let(:sell_to_pos) { 'true' }

                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0) # all variance is not sold to POS

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end

              context 'when send sell_to_pos not send' do
                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(2)
                  expect(variances[0]['id']).to eq(product_variance.id)
                  expect(variances[0]['name']).to eq(product_variance.name)
                  expect(variances[1]['id']).to eq(product_variance_2.id)
                  expect(variances[1]['name']).to eq(product_variance_2.name)

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end
            end

            context 'when product variance.product_location_stock sell_to_pos = false' do
              let(:product_variance_2_product_setting_location) do
                create(
                  :product_setting_location,
                  product_id: product_variance_2.id,
                  location_id: owned_branch_1.id,
                  is_product_type_active: true,
                  sell_to_pos: false,
                )
              end

              before do
                product_variance_2_product_setting_location
              end

              context 'when send sell_to_pos query = true' do
                let(:sell_to_pos) { 'true' }

                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(1)
                  expect(variances[0]['id']).to eq(product_variance.id)
                  expect(variances[0]['name']).to eq(product_variance.name)

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end

              context 'when send sell_to_pos not send' do
                it 'should response product' do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  expect(response_body.keys).to match_array ['products', 'paging']

                  paging = response_body['paging']
                  expect(paging['next_page']).to be_falsey
                  expect(paging['prev_page']).to be_falsey

                  response_products = response_body['products']
                  expect(response_products.size).to eq(2)

                  product_response = response_products.map do |product|
                    { id: product['id'], pos_product_layout: product['pos_product_layout'] }
                  end
                  expect(product_response).to match_array(
                    [
                      { id: product.id, pos_product_layout: 9999 },
                      { id: product_2.id, pos_product_layout: 9999 },
                    ]
                  )

                  response_product = response_products.detect { |prod| prod['id'] == product.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(2)
                  expect(variances[0]['id']).to eq(product_variance.id)
                  expect(variances[0]['name']).to eq(product_variance.name)
                  expect(variances[1]['id']).to eq(product_variance_2.id)
                  expect(variances[1]['name']).to eq(product_variance_2.name)

                  response_product = response_products.detect { |prod| prod['id'] == product_2.id }
                  expect(response_product).to be_present
                  expect(response_product['id']).to eq(product_2.id)

                  variances = response_product['variances']
                  expect(variances.length).to eq(0)
                end
              end
            end
          end
        end

        context 'when has uncategorized and sort by product category name asc' do
          let(:sort_order) { 'asc' }
          let(:sort_key) { 'product_category_name' }
          let(:include_variance) { 'true' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            # NOTE: Why upcase? PSQL sort lower case differently, depends on our local / server PostgreSQL LC_COLLATE configuration.
            deactivated_product_category.update(name: deactivated_product_category.name.upcase)
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            response_names = response_body['products'].map{ |x| x['product_category']['name'] }
            expect(response_names).to eq [burgers_category.name, coffee_drinks_category.name, deactivated_product_category.name, nil]
            expect(response_body['products'][0]['available_stock_flag'].keys).to eql(["shopee_food"])
          end
        end

        context 'when product unit has order line' do
          let(:sort_order) { 'asc' }
          let(:sort_key) { 'product_category_name' }
          let(:include_variance) { 'true' }

          before do |example|
            latte
            latte_unit_conversion_party_5_l
            latte_unit_conversion_party_10_l

            order_transaction_non_franchises
            order_transaction_non_franchises.order_transaction_lines.first.update_columns(product_id: latte_unit_conversion_party_5_l.product_id, product_unit_conversion_id: latte_unit_conversion_party_5_l.id, product_unit_id: latte_unit_conversion_party_5_l.product_unit_id)
            order_transaction_non_franchises.order_transaction_lines.second.update_columns(product_id: latte_unit_conversion_party_10_l.product_id, product_unit_conversion_id: latte_unit_conversion_party_10_l.id, product_unit_id: latte_unit_conversion_party_10_l.product_unit_id)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            latte_response = response_body['products'].detect { |product_response| product_response['id'] == latte.id }
            expect(latte_response['product_unit_conversions'].map { |unit_conversion_response| unit_conversion_response['can_delete'] }).to eq(
              [false, false]
            )
          end
        end

        context 'when show permissions' do
          let(:sort_order) { 'asc' }
          let(:include_variance) { 'true' }
          let(:show_permissions) { 'true' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            # NOTE: Why upcase? PSQL sort lower case differently, depends on our local / server PostgreSQL LC_COLLATE configuration.
            deactivated_product_category.update(name: deactivated_product_category.name.upcase)
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            expect(response_body['products'].first.keys).to include(
              "can_update", "can_reactivate", "can_deactivate"
            )
          end
        end

        context 'when use new option set' do
          let(:paket_family) do
            create(
              :product,
              name: 'Paket Family',
              brand: brand,
              product_unit: cup_500_ml
            )
          end

          let(:size_level) do
            create(
              :option_set,
              product: small,
              brand: brand,
              name: "Size Level"
            )
          end

          let(:ice_level) do
            create(
              :option_set,
              product: few_ice,
              brand: brand,
              name: "Ice Level"
            )
          end

          let(:sugar_level) do
            create(
              :option_set,
              product: few_sugar,
              brand: brand,
              name: "Sugar Level"
            )
          end

          let(:opsi_minuman) do
            opsi_minuman = create(
              :option_set,
              product: latte, # product with option set
              brand: brand,
              name: "Opsi Minuman"
            )

            opsi_minuman.option_set_options << build(:option_set_option, option_set_as_option: sugar_level) # option set as option

            opsi_minuman
          end

          let(:opsi_makanan) do
            create(
              :option_set,
              product: cheese_burger, # product with variance
              brand: brand,
              name: "Opsi Makanan"
            )
          end

          let(:paket_family_opsi_minuman) do
            create(:product_option_set, product_id: paket_family.id, option_set_id: opsi_minuman.id)
          end

          let(:paket_family_opsi_makanan) do
            create(:product_option_set, product_id: paket_family.id, option_set_id: opsi_makanan.id)
          end

          let(:paket_family_ice_level) do
            create(:product_option_set, product_id: paket_family.id, option_set_id: ice_level.id)
          end

          let(:cheese_burger_variant_beef) { create(:product, owner_location_id: central_kitchen.id, name: 'Cheese Burger Beef', sku: 'cheese-burger-beef', brand: brand, product_category: burgers_category, product_unit: cheese_burger.product_unit, internal_price: 2500, sell_price: 2800, variance_parent_product_id: cheese_burger.id, sell_to_customer_type: true, sell_unit: cheese_burger.product_unit) }

          before do
            cheese_burger
            cheese_burger_variant_chicken
            cheese_burger_variant_beef

            few_sugar
            sugar_level

            latte
            small
            size_level
            product_option_set_size_level

            opsi_minuman
            opsi_makanan

            few_ice
            ice_level

            paket_family
            paket_family_opsi_minuman
            paket_family_opsi_makanan
            paket_family_ice_level

            Product.search_index.refresh
          end

          it 'returns categories sorted ascendingly' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)

            # find paket family
            response_products = response_body['products']
            expect(response_products).to be_present

            response_paket_family = response_products.detect { |product| product['id'] == paket_family.id }
            expect(response_paket_family).to be_present

            # find product option set
            response_product_option_sets = response_paket_family['product_option_sets']
            expect(response_product_option_sets.size).to eq(3)

            # find option set - opsi minuman
            response_opsi_minuman = response_product_option_sets.detect { |option_set| option_set['option_set_id'] == opsi_minuman.id }
            expect(response_opsi_minuman['option_set']['option_set_options'].size).to eq(2)

            # # find product with option set from opsi_minuman -  option set option product with option set
            response_opsi_minuman_latte = response_opsi_minuman['option_set']['option_set_options'].detect { |option_set_option| option_set_option['product_id'] == latte.id }
            expect(response_opsi_minuman_latte).to be_present
            expect(response_opsi_minuman_latte['enable_max_chosen']).to eq(false)
            expect(response_opsi_minuman_latte['max_chosen_quantity']).to eq('0.0')

            expect(response_opsi_minuman_latte['product']['option_sets'].size).to eq(1)
            expect(response_opsi_minuman_latte['product']['option_sets'][0]['id']).to eq(size_level.id)
            expect(response_opsi_minuman_latte['product']['option_sets'][0]['option_set_options'].size).to eq(1)
            expect(response_opsi_minuman_latte['product']['option_sets'][0]['option_set_options'][0]['product_id']).to eq(small.id)

            expect(response_opsi_minuman_latte['product']['option_sets'][0]['option_set_options'][0]['enable_max_chosen']).to eq(false)
            expect(response_opsi_minuman_latte['product']['option_sets'][0]['option_set_options'][0]['max_chosen_quantity']).to eq('0.0')

            # find  -  option set option is option set
            response_opsi_minuman_sugar_level = response_opsi_minuman['option_set']['option_set_options'].detect { |option_set_option| option_set_option['option_set_as_option_id'] == sugar_level.id }
            expect(response_opsi_minuman_sugar_level).to be_present
            expect(response_opsi_minuman_sugar_level['option_set']['option_set_options'].size).to eq(1)
            expect(response_opsi_minuman_sugar_level['option_set']['option_set_options'][0]['product_id']).to eq(few_sugar.id)

            # find option set - ice_level
            response_opsi_ice_level = response_product_option_sets.detect { |option_set| option_set['option_set_id'] == ice_level.id }
            expect(response_opsi_ice_level).to be_present
            expect(response_opsi_ice_level['option_set']['option_set_options'].size).to eq(1)
            expect(response_opsi_ice_level['option_set']['option_set_options'][0]['product_id']).to eq(few_ice.id)

            # find option set - opsi_makanan - option set option product with variace
            response_opsi_opsi_makanan = response_product_option_sets.detect { |option_set| option_set['option_set_id'] == opsi_makanan.id }
            expect(response_opsi_opsi_makanan).to be_present
            expect(response_opsi_opsi_makanan['option_set']['option_set_options'].size).to eq(1)
            expect(response_opsi_opsi_makanan['option_set']['option_set_options'][0]['product_id']).to eq(cheese_burger.id)
            expect(response_opsi_opsi_makanan['option_set']['option_set_options'][0]['product']['variances'].size).to eq(2)
            expect(response_opsi_opsi_makanan['option_set']['option_set_options'][0]['product']['variances'][0]['product_id']).to eq(cheese_burger_variant_chicken.id)
            expect(response_opsi_opsi_makanan['option_set']['option_set_options'][0]['product']['variances'][1]['product_id']).to eq(cheese_burger_variant_beef.id)
          end
        end

        context 'when product unit has no order line' do
          let(:sort_order) { 'asc' }
          let(:sort_key) { 'product_category_name' }
          let(:include_variance) { 'true' }

          before do |example|
            latte
            latte_unit_conversion_party_5_l
            latte_unit_conversion_party_10_l

            order_transaction_non_franchises

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            latte_response = response_body['products'].detect { |product_response| product_response['id'] == latte.id }
            expect(latte_response['product_unit_conversions'].map { |unit_conversion_response| unit_conversion_response['can_delete'] }).to eq(
              [true, true]
            )
          end
        end

        context 'when sort by sku asc' do
          let(:sort_order) { 'asc' }
          let(:sort_key) { 'sku' }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            deactivated_product_category
            # NOTE: Why upcase? PSQL sort lower case differently, depends on our local / server PostgreSQL LC_COLLATE configuration.
            deactivated_product_category.update(name: deactivated_product_category.name.upcase)
            deactivated_product

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns categories sorted ascendingly' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            response_names = response_body['products'].map{ |x| x['sku'] }
            expect(response_names).to eq [cheese_burger_external_vendor_type.sku, deactivated_product.sku, latte_sell_to_customer_type.sku, uncategorized_product.sku]
          end
        end

        context 'when has option set' do
          before do |example|
            @product_for_option_set = product_option_set.option_set.option_set_options.first.product
            product
            product_2
            product_3

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['products', 'paging']
            response_product = response_body['products'].find { |p| p['id'] === product.id }
            expect(response_product['internal_price']).to_not eq 0
            expect(response_body['paging']['next_page']).to be_falsey
            expect(response_body['paging']['prev_page']).to be_falsey

            product_response = response_body['products'].map do |product|
              { id: product['id'], pos_product_layout: product['pos_product_layout'] }
            end
            expect(product_response).to match_array(
              [{ id: product.id, pos_product_layout: 9999 },
               { id: product_2.id, pos_product_layout: 9999 },
               { id: product_3.id, pos_product_layout: 9999 },
               { id: @product_for_option_set.id, pos_product_layout: 9999 }]
            )

            expect(response_body['products'].first['product_option_sets'].length).to eq(1)
            expect(response_body['products'].first['product_option_sets'].first['product_id']).to eq(product.id)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['product_id']).to eq(@product_for_option_set.id)
          end
        end

        context 'when presentation is all', bullet: :skip do
          let(:presentation) { 'all' }
          let(:product_keys_without_variances) { product_keys - ['variances'] }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns products with incoming_quantity' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            expect(response_body['products'].first.keys).to match_array(product_keys_without_variances)
          end
        end

        context 'when presentation is product_category_and_procurement_units', bullet: :skip do
          let(:presentation) { 'product_category_and_procurement_units' }
          let(:product_keys_without_variances) { product_keys - ['variances'] }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns products with incoming_quantity' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            expect(response_body['products'].first.keys).to match_array(["id", "name", "pos_product_layout", "pos_product_layout_per_sub_brand", "price_per_order_global", "price_per_order_location", "product_category", "product_procurement_units", "vendor_procurement_units", "sku", 'upc'])
          end
        end

        context 'when presentation is all', bullet: :skip do
          let(:presentation) { 'all_locations' }
          let(:is_select_all_location) { 'true' }
          let(:show_pos_position) { 'false' }
          let(:keys) { ['id', 'name', 'sku', 'product_category', 'is_multi_location', 'location_name', 'image_url', 'status'] }

          before do |example|
            coffee_drinks_category
            latte_sell_to_customer_type

            burgers_category
            cheese_burger_external_vendor_type

            uncategorized_product

            owned_branch_1

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns products with incoming_quantity' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ["paging", "products"]
            expect(response_body['products'].first.keys).to match_array(keys)
          end
        end

        context 'when product variance has option set' do
          let(:product_variance_option_set) { create(:product_option_set, product_id: product_variance.id, option_set_id: option_set.id) }
          let(:product_variance_2) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }
          let(:include_variance) { 'true' }

          before do |example|
            @product_for_option_set = product_variance_option_set.option_set.option_set_options.first.product
            product
            product_variance
            product_variance_2
            product_2

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['products', 'paging']
            response_product = response_body['products'].find { |p| p['id'] === product.id }
            expect(response_product['internal_price']).to_not eq 0
            expect(response_body['paging']['next_page']).to be_falsey
            expect(response_body['paging']['prev_page']).to be_falsey

            product_response = response_body['products'].map do |product|
              { id: product['id'], pos_product_layout: product['pos_product_layout'] }
            end
            expect(product_response).to match_array(
              [{ id: product.id, pos_product_layout: 9999 },
               { id: product_2.id, pos_product_layout: 9999 },
               { id: @product_for_option_set.id, pos_product_layout: 9999 }]
            )

            expect(response_body['products'].first['variances'].length).to eq(2)
            expect(response_body['products'].first['variances'].first.keys).to match_array(variance_keys)
            expect(response_body['products'].first['variances'].first['product_option_sets'].length).to eq(1)
            expect(response_body['products'].first['variances'].first['product_option_sets'].first['option_set']['option_set_options'].first['product_id']).to eq(@product_for_option_set.id)
            expect(response_body['products'].first['variances'].second.keys).to match_array(variance_keys)
            expect(response_body['products'].first['variances'].second['product_option_sets'].length).to eq(0)
          end
        end

        context 'when has option set but not for location', bullet: :skip do
          before do |example|
            @product_for_option_set = product_option_set.option_set.option_set_options.first.product
            @product_for_option_set.locations_products.find_by!(location: owned_branch_1).destroy!

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['products', 'paging']

            response_product = response_body['products'].find { |p| p['id'] === product.id }
            expect(response_product['internal_price']).to_not eq 0
            expect(response_body['paging']['next_page']).to be_falsey
            expect(response_body['paging']['prev_page']).to be_falsey

            expect(response_body['products'].first['product_option_sets'].length).to eq(1)
            expect(response_body['products'].first['product_option_sets'].first['product_id']).to eq(product.id)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options']).to be_empty
          end
        end

        context 'when with filter ids & location_id' do
          let(:ids) { product.id }
          let!(:location_to_id) { central_kitchen.id }

          before do |example|
            locations_product = product.locations_products.find { |lp| lp.location_id == central_kitchen.id }
            locations_product.update(out_of_stock_flag: true)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['products', 'paging']

            response_product = response_body['products'].find { |p| p['id'] === product.id }
            expect(response_product['out_of_stock_flag']).to be_truthy
          end
        end

        context 'when has multiple category layout' do
          let!(:location_id) { central_kitchen.id }
          let(:show_pos_position) { 'true' }

          before do |example|
            product.update!(product_category: coffee_drinks_category)
            product_2.update!(product_category: coffee_drinks_category)
            product_3.update!(product_category: coffee_drinks_category)
            product_4.update!(product_category: burgers_category)
            product_5.update!(product_category: burgers_category)
            product_6.update!(product_category: burgers_category)
            product_7.update!(product_category: burgers_category)

            create(
              :product_layout, layout_type: 'pos', location_id: central_kitchen.id,
                               payload: {
                                 product_categories: [
                                   { id: burgers_category.id, sequence: 1,
                                     products: [{ id: product_4.id, sequence: 1 }, { id: product_6.id, sequence: 2 }] },
                                   { id: coffee_drinks_category.id, sequence: 2,
                                     products: [{ id: product_3.id, sequence: 1 }, { id: product_2.id, sequence: 2 }] }
                                 ]
                               }
            )

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            product_response = response_body['products'].map do |product|
              { id: product['id'], pos_product_layout: product['pos_product_layout'] }
            end
            expect(product_response)
              .to match_array(
                [{ id: product.id, pos_product_layout: 9999 },
                 { id: product_2.id, pos_product_layout: 2 },
                 { id: product_3.id, pos_product_layout: 1 },
                 { id: product_4.id, pos_product_layout: 1 },
                 { id: product_6.id, pos_product_layout: 2 }]
              )
          end
        end

        context 'when has single category layout' do
          let!(:location_id) { central_kitchen.id }
          let(:show_pos_position) { 'true' }

          before do |example|
            product.update!(product_category: coffee_drinks_category)
            product_2.update!(product_category: coffee_drinks_category)
            product_3.update!(product_category: coffee_drinks_category)
            product_4.update!(product_category: burgers_category)
            product_5.update!(product_category: burgers_category)
            product_6.update!(product_category: burgers_category)
            product_7.update!(product_category: burgers_category)

            create(
              :product_layout, layout_type: 'pos', location_id: central_kitchen.id,
              payload: {
                product_categories: [
                  { id: burgers_category.id, sequence: 1, products: [{ id: product_4.id, sequence: 1 }, { id: product_6.id, sequence: 2 }] }
                ]
              }
            )
            create(
              :product_layout, layout_type: 'sub_brand_pos', location_id: central_kitchen.id,
                               payload: {
                                 sub_brands: [
                                   {
                                     id: brand.sub_brands.first.id,
                                     name: brand.sub_brands.first.name,
                                     product_categories: [
                                       { id: burgers_category.id, sequence: 1, products: [{ id: product_4.id, sequence: 1 }] },
                                       { id: 4321, sequence: 2, products: [{id: product_6.id, sequence: 1}]}
                                     ]
                                   }
                                 ]
                               }
            )

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            product_response = response_body['products'].map do |product|
              { id: product['id'], pos_product_layout: product['pos_product_layout'], pos_product_layout_per_sub_brand: product['pos_product_layout_per_sub_brand'] }
            end
            expect(product_response)
              .to match_array(
                [{ id: product.id, pos_product_layout: 9999, pos_product_layout_per_sub_brand: [{"id"=>1, "name"=>"runchise", "pos_product_layout"=>9999}]},
                 { id: product_2.id, pos_product_layout: 9999, pos_product_layout_per_sub_brand: [{"id"=>1, "name"=>"runchise", "pos_product_layout"=>9999}] },
                 { id: product_3.id, pos_product_layout: 9999, pos_product_layout_per_sub_brand: [{"id"=>1, "name"=>"runchise", "pos_product_layout"=>9999}] },
                 { id: product_4.id, pos_product_layout: 1, pos_product_layout_per_sub_brand: [{"id"=>1, "name"=>"runchise", "pos_product_layout"=>1}] },
                 { id: product_6.id, pos_product_layout: 2, pos_product_layout_per_sub_brand: [{"id"=>1, "name"=>"runchise", "pos_product_layout"=>9999}] }]
              )
          end
        end

        context 'when with product variance global order type' do
          let(:include_variance) { 'true' }
          before do |example|
            create(:product_price_per_order_type, product: product_variance, sell_price: 80_000, order_type: online_ordering_order_type)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should return variances nested under product with price_per_order_global' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            response_variance = response_body['products'].first['variances'].first
            expect(response_variance.keys).to match_array(variance_keys)

            expect(response_variance['price_per_order_global']).to eq(
              [{ 'order_type' => { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' }, 'location' => nil, 'sell_price' => '80000.0',
                 'sell_tax' => nil, 'sell_tax_setting' => 'default' }]
            )
            expect(response_variance['price_per_order_location']).to eq(
              [{"order_type"=>{"id"=>online_ordering_order_type.id, "name"=>"(R) Online Order"},
              "location"=>{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"},
              "sell_price"=>"80000.0",
              "sell_tax"=>nil,
              "sell_tax_setting"=>"price_exclude_tax"}]
            )
          end
        end

        context 'when with product variance location order type', bullet: :skip do
          let(:include_variance) { 'true' }
          before do |example|
            create(:product_price_per_order_type, product: product_variance, sell_price: 80_000, order_type: online_ordering_order_type,
                                                  location_id: owned_branch_1.id)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['variances'].first['price_per_order_location']).to eq(
              [{ 'order_type' => { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' },
                 'location' => { 'id' => owned_branch_1.id, 'name' => 'Owned Location Parung' }, 'sell_price' => '80000.0', 'sell_tax' => nil, 'sell_tax_setting' => 'price_exclude_tax' }]
            )
            expect(response_body['products'].first['variances'].first['price_per_order_global']).to eq([])
          end
        end

        context 'when has variances & filter with include_variance & variance_presentations internal_price_units' do
          let(:variance_presentations) { 'internal_price_units' }
          let(:include_variance) { 'true' }
          let(:product_variance_product_unit_conversion) { create(:product_unit_conversion, product: product_variance, product_unit: product_unit_2) }
          let(:variance_keys_with_procurement_units) { variance_keys.concat ["product_unit_conversions", "product_procurement_units", "product_internal_price_table_units"] }

          before do |example|
            owned_branch_1
            product
            product_unit_conversion
            product.procurement_units.create({ product_unit_id: product.product_unit_id })

            product_variance
            product_variance_product_unit_conversion
            product_variance.procurement_units.create({ product_unit_id: product_variance.product_unit_id })

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns variances nested under parent product with their procurement unit & internal price values' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            response_product = response_body['products'].first
            expect(response_product.keys).to match_array(product_keys)

            response_variance = response_product['variances'].first
            expect(response_variance.keys).to match_array(variance_keys_with_procurement_units.uniq)

            response_variance_unit_conversion = response_variance['product_unit_conversions'].first
            expect(response_variance_unit_conversion.keys).to match_array([
              "id", "converted_qty", "product_unit_id", "product_id", "created_at", "updated_at", "deleted", "internal_price", "product_unit", "can_delete"
            ])

            response_variance_procurement_unit = response_variance['product_procurement_units'].first
            expect(response_variance_procurement_unit.keys).to match_array([
              "id", "product_unit", "internal_price", "sequence"
            ])
          end
        end

        context 'when with product variance location and global order type' do
          let(:include_variance) { 'true' }
          before do |example|
            create(:product_price_per_order_type, product: product_variance, sell_price: 80_000, order_type: online_ordering_order_type)
            create(:product_price_per_order_type, product: product_variance, sell_price: 77_777, order_type: online_ordering_order_type,
                                                  location_id: owned_branch_1.id)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['variances'].first['price_per_order_global']).to eq(
              [{ 'order_type' => { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' }, 'location' => nil, 'sell_price' => '80000.0',
                 'sell_tax' => nil, 'sell_tax_setting' => 'default' }]
            )
            expect(response_body['products'].first['variances'].first['price_per_order_location']).to eq(
              [{ 'order_type' => { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' },
                 'location' => { 'id' => owned_branch_1.id, 'name' => 'Owned Location Parung' }, 'sell_price' => '77777.0', 'sell_tax' => nil, 'sell_tax_setting' => 'price_exclude_tax' }]
            )
          end
        end

        context 'when has option set but product is archived/deactivated', bullet: :skip do
          before do |example|
            @product_for_option_set = product_option_set.option_set.option_set_options.first.product
            @product_for_option_set.update!(status: :deactivated)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['products', 'paging']

            response_product = response_body['products'].find { |p| p['id'] === product.id }
            expect(response_product['internal_price']).to_not eq 0
            expect(response_body['paging']['next_page']).to be_falsey
            expect(response_body['paging']['prev_page']).to be_falsey

            expect(response_product['product_option_sets'].length).to eq(1)
            expect(response_product['product_option_sets'].first['product_id']).to eq(product.id)
            expect(response_product['product_option_sets'].first['option_set']['option_set_options']).to be_empty
          end
        end

        context 'when has option set option location custom price per location with order type' do
          let(:option_set_option) { OptionSetOption.last }

          before do |example|
            product_option_set
            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id,
                                                      order_type: online_ordering_order_type, price: 34_343)
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id,
                                                      order_type: online_ordering_order_type, price: 22_222)
            create(:option_set_custom_price_location, option_set_option_id: option_set_option.id, order_type: online_ordering_order_type,
                                                      price: 18_188)

            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id,
                                                      order_type: custom_order_type, price: 44_222)
            create(:option_set_custom_price_location, option_set_option_id: option_set_option.id, order_type: custom_order_type, price: 11_111)
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id,
                                                      order_type: custom_order_type, price: 10_000)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns specific location custom price in option set' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price']).to eq(option_set_option.price.to_f.to_s)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price_order_types'])
              .to eq(
                [{ 'order_type' => { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' },
                   'price' => '34343.0' },
                 { 'order_type' => { 'id' => custom_order_type.id, 'name' => 'sample' }, 'price' => '44222.0' }]
              )
          end
        end

        context 'when has option set option location custom price per location with and without order type' do
          let(:option_set_option) { OptionSetOption.last }
          let(:custom_price_global_per_location) do
            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 34_343)
          end

          before do |example|
            owned_branch_1
            owned_branch_2
            brand.online_delivery_setting.update!(enable: true)
            product_option_set

            custom_price_global_per_location
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 22_222)

            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id,
                                                      order_type: custom_order_type, price: 44_222)
            create(:option_set_custom_price_location, option_set_option_id: option_set_option.id, order_type: custom_order_type, price: 11_111)
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id,
                                                      order_type: custom_order_type, price: 10_000)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns both specific location with order type & global location custom price in option set' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price']).to eq(custom_price_global_per_location.price.to_f.to_s)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price_order_types'])
              .to eq(
                [{"order_type"=>{"id"=>1, "name"=>"(R) Online Order"},
                  "price"=>"34343.0"},
                 {"order_type"=>{"id"=>2, "name"=>"sample"}, "price"=>"44222.0"}]
              )
          end
        end

        context 'when has option set option location custom price per location without order type', bullet: :skip do
          let(:option_set_option) { OptionSetOption.last }
          let(:custom_price_global_per_location) do
            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 34_343)
          end

          before do |example|
            product_option_set
            brand.online_delivery_setting.update!(enable: true)
            custom_price_global_per_location
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 22_222)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns global location custom price in option set' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price']).to eq(custom_price_global_per_location.price.to_f.to_s)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price_order_types']).to eq(
              [{"order_type"=>{"id"=>1, "name"=>"(R) Online Order"},
               "price"=>"34343.0"}]
            )
          end
        end

        context 'when has option set option location custom price per location with order type but from different location' do
          let(:option_set_option) { OptionSetOption.last }
          let(:custom_price_global_per_location) do
            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 34_343)
          end
          let(:custom_price_global_per_location_2) do
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id, order_type: nil,
                                                      price: 9999)
          end

          before do
            product_option_set
            brand.online_delivery_setting.update!(enable: true)
            custom_price_global_per_location
            create(:option_set_custom_price_location, location_id: owned_branch_1.id, option_set_option_id: option_set_option.id,
                                                      order_type: online_ordering_order_type, price: 11_111)
            create(:option_set_custom_price_location, location_id: owned_branch_2.id, option_set_option_id: option_set_option.id,
                                                      order_type: online_ordering_order_type, price: 22_222)

            Product.search_index.refresh
          end

          it 'returns custom price in option set filtered by location id' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price']).to eq(custom_price_global_per_location.price.to_f.to_s)
            expect(response_body['products'].first['product_option_sets'].first['option_set']['option_set_options'].first['price_order_types']).to eq([
                                                                                                                                                        {
                                                                                                                                                          'order_type' => {
                                                                                                                                                            'id' => 1, 'name' => '(R) Online Order'
                                                                                                                                                          }, 'price' => '11111.0'
                                                                                                                                                        }
                                                                                                                                                      ])
          end
        end

        context 'when has product price per order type' do
          before do |example|
            brand.online_delivery_setting.update!(enable: true)
            create(:product_price_per_order_type, location_id: owned_branch_1.id, product_id: product.id, order_type: online_ordering_order_type,
                                                  sell_price: 34_343)
            create(:product_price_per_order_type, location_id: owned_branch_2.id, product_id: product.id, order_type: online_ordering_order_type,
                                                  sell_price: 22_222)
            create(:product_price_per_order_type, product_id: product.id, order_type: online_ordering_order_type, sell_price: 18_188)

            create(:product_price_per_order_type, location_id: owned_branch_1.id, product_id: product.id, order_type: custom_order_type,
                                                  sell_price: 44_222)
            create(:product_price_per_order_type, product_id: product.id, order_type: custom_order_type, sell_price: 11_111)
            create(:product_price_per_order_type, location_id: owned_branch_2.id, product_id: product.id, order_type: custom_order_type,
                                                  sell_price: 10_000)

            product.reload

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].first['product_price_per_order_types'].count).to eq(4)
            expect(response_body['products'].first['product_price_per_order_types'].map do |product_price_per_order_type|
                     { order_type: product_price_per_order_type['order_type'], location: product_price_per_order_type['location'],
                       sell_price: product_price_per_order_type['sell_price'] }
                   end)
              .to eq(
                [{ order_type: { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' },
                   location: { 'id' => owned_branch_1.id, 'name' => 'Owned Location Parung', 'branch_type' => 'outlet' },
                   sell_price: '34343.0' },
                 { order_type: { 'id' => online_ordering_order_type.id, 'name' => '(R) Online Order' },
                   location: nil,
                   sell_price: '18188.0' },
                 { order_type: { 'id' => custom_order_type.id, 'name' => 'sample' },
                   location: { 'id' => owned_branch_1.id, 'name' => 'Owned Location Parung', 'branch_type' => 'outlet' },
                   sell_price: '44222.0' },
                 { order_type: { 'id' => custom_order_type.id, 'name' => 'sample' },
                   location: nil,
                   sell_price: '11111.0' }]
              )
          end
        end

        context 'when params exclude_used_in_recipe_line is truthy' do
          let(:product_as_recipe_line) do
            recipe = build(:recipe, recipe_type: 'made_to_order', product: latte, brand: brand, product_unit: latte.product_unit)
            recipe_line = build(:recipe_line, product: espresso, recipe: recipe, product_unit: espresso.product_unit, quantity: 10)
            recipe.recipe_lines << recipe_line
            recipe.save
            espresso
          end

          let(:ids) { product_as_recipe_line.id }
          let(:exclude_used_in_recipe_line) { 'true' }

          before do |example|
            product_as_recipe_line.reindex

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should exclude product that has been used as recipe lines' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['products'].find{|x| x['id'] == espresso.id}).to be_nil
          end
        end

        context 'when has filter location_to_id & location_id and outlet to ck' do
          let(:location_id) { owned_branch_1.id }
          let(:location_to_id) { central_kitchen.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            product
            product_2
            product_3

            LocationsProduct.where(location_id: central_kitchen.id, product_id: [product.id, product_2.id]).destroy_all
            Product.reindex

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to eql([product_3.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when has filter location_to_id & location_id and ck to ck' do
          let(:location_id) { central_kitchen.id }
          let(:location_to_id) { central_kitchen_2.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            product
            product_2
            product_3

            LocationsProduct.where(location_id: central_kitchen_2.id, product_id: [product.id, product_2.id]).destroy_all

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to eql([product_3.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when has filter location_to_id & multiple location_from_ids' do
          let(:location_from_ids) { "#{owned_branch_1.id},#{owned_branch_2.id}" }
          let(:location_to_id) { central_kitchen.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            cheese_burger_owned_branch_2_only
            plastic_bag_multiple_location_ids
            latte_no_category

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([plastic_bag_multiple_location_ids.id, latte_no_category.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when has filter location_to_id & one location_from_ids' do
          let(:location_from_ids) { "#{owned_branch_1.id}" }
          let(:location_to_id) { central_kitchen.id }

          before do |example|
            cheese_burger_owned_branch_3_only
            plastic_bag_multiple_location_ids
            latte_no_category

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([latte_no_category.id, plastic_bag_multiple_location_ids.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when filter by location_from_ids & location_to_id' do
          let(:all_location_froms) { 'true' }
          let(:location_to_id) { central_kitchen.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            cheese_burger_owned_branch_2_only
            plastic_bag_multiple_location_ids
            latte_no_category

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([plastic_bag_multiple_location_ids.id, latte_no_category.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when with filter skip_child_variances set to true' do
          let(:skip_child_variances) { 'true' }

          before do |example|
            product_variance

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'does NOT return any product with child variance' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            response_product = response_body['products'].find { |p| p['id'] == product_variance.id }
            expect(response_product).to be_nil
          end
        end

        context 'when with filter skip_child_variances set to false' do
          let(:skip_child_variances) { 'false' }

          before do |example|
            product_variance

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns product with child variance' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            response_product = response_body['products'].find { |p| p['id'] == product_variance.id }
            expect(response_product).not_to be_nil
            expect(response_product['variance_parent_product_id']).not_to be_nil
          end
        end

        context 'when has filter location_to_id & location_id and outlet to outlet' do
          let(:location_id) { owned_branch_3.id }
          let(:location_to_id) { owned_branch_2.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            cheese_burger_external_vendor_type.update!(internal_distribution_type: true, internal_produce_type: true)

            product_3
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

            LocationsProduct.where(location_id: owned_branch_2.id, product_id: [product.id, product_2.id]).destroy_all

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'returns a valid product response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([product_3.id, cheese_burger_external_vendor_type.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when has filter location_to_id & location_id and outlet to outlet, product not available in location from' do
          let(:location_id) { owned_branch_3.id }
          let(:location_to_id) { owned_branch_2.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            cheese_burger_external_vendor_type.update!(internal_distribution_type: true, internal_produce_type: true)

            product_3
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

            LocationsProduct.where(location_id: owned_branch_2.id, product_id: [product.id, product_2.id]).destroy_all
            LocationsProduct.where(location_id: owned_branch_2.id, product_id: [cheese_burger_external_vendor_type.id]).destroy_all

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should not return product that has no locations product' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([product_3.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when has filter location_to_id & location_id and outlet to outlet, product not available in location to' do
          let(:location_id) { owned_branch_3.id }
          let(:location_to_id) { owned_branch_2.id }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }

          before do |example|
            cheese_burger_external_vendor_type.update!(internal_distribution_type: true, internal_produce_type: true)

            product_3
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)

            LocationsProduct.where(location_id: owned_branch_2.id, product_id: [product.id, product_2.id]).destroy_all
            LocationsProduct.where(location_id: owned_branch_3.id, product_id: [cheese_burger_external_vendor_type.id]).destroy_all

            Product.reindex # manually reindex

            submit_request(example.metadata)
          end

          it 'should not return product that has no locations product' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] }).to match_array([product_3.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when outlet to vendor, no vendor product' do
          let(:location_id) { owned_branch_1.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_1.id }

          before do |example|
            spicy_burger
            cheese_burger
            paper_bag

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] })
              .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when outlet to vendor, with vendor product' do
          let(:location_id) { owned_branch_1.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }

          before do |example|
            spicy_burger
            cheese_burger
            paper_bag

            vendor_product_all_locations_latte
            vendor_product_all_locations_soursop_juice

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            responses = response_body['products'].map do |product|
              { product_id: product['id'], sell_price: product['sell_price'],
                vendor_products: product['vendor_products'] }
            end

            expect(responses)
              .to match_array(
                [{:product_id=>latte.id,
                  :sell_price=>"1800.0",
                  :vendor_products=>
                  [{"product_unit_id"=>latte.product_unit.id,
                    "product_unit_name"=>latte.product_unit.name,
                    "sell_price"=>"7700.0",
                    "lock_price"=>false,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]},
                {:product_id=>soursop_juice.id,
                  :sell_price=>"2000.0",
                  :vendor_products=>
                  [{"product_unit_id"=>soursop_juice.product_unit.id,
                    "product_unit_name"=>soursop_juice.product_unit.name,
                    "sell_price"=>"6100.0",
                    "lock_price"=>true,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]}]
              )
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when outlet to vendor, with vendor product, one product has external vendor type in product setting location' do
          let(:location_id) { owned_branch_1.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }

          before do |example|
            latte_setting_location_owned_1.update!(external_vendor_type: true, is_product_type_active: true)
            latte.update!(external_vendor_type: false)
            spicy_burger
            cheese_burger
            paper_bag

            vendor_product_all_locations_latte
            vendor_product_all_locations_soursop_juice

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            responses = response_body['products'].map do |product|
              { product_id: product['id'], sell_price: product['sell_price'],
                vendor_products: product['vendor_products'] }
            end

            expect(responses)
              .to match_array(
                [{:product_id=>latte.id,
                  :sell_price=>"1800.0",
                  :vendor_products=>
                  [{"product_unit_id"=>latte.product_unit.id,
                    "product_unit_name"=>latte.product_unit.name,
                    "sell_price"=>"7700.0",
                    "lock_price"=>false,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]},
                {:product_id=>soursop_juice.id,
                  :sell_price=>"2000.0",
                  :vendor_products=>
                  [{"product_unit_id"=>soursop_juice.product_unit.id,
                    "product_unit_name"=>soursop_juice.product_unit.name,
                    "sell_price"=>"6100.0",
                    "lock_price"=>true,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]}]
              )
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when CK to vendor, no vendor product' do
          let(:location_id) { central_kitchen.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }

          before do |example|
            spicy_burger
            cheese_burger
            paper_bag

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |product| product['id'] })
              .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when CK to vendor, with vendor product' do
          let(:location_id) { central_kitchen.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }

          before do |example|
            spicy_burger
            cheese_burger
            paper_bag

            vendor_product_all_locations_latte
            vendor_product_all_locations_soursop_juice

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            responses = response_body['products'].map do |product|
              { product_id: product['id'], sell_price: product['sell_price'],
                vendor_products: product['vendor_products'] }
            end

            expect(responses)
              .to match_array(
                [{:product_id=>latte.id,
                  :sell_price=>"1800.0",
                  :vendor_products=>
                  [{"product_unit_id"=>latte.product_unit.id,
                    "product_unit_name"=>latte.product_unit.name,
                    "sell_price"=>"7700.0",
                    "lock_price"=>false,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]},
                {:product_id=>soursop_juice.id,
                  :sell_price=>"2000.0",
                  :vendor_products=>
                  [{"product_unit_id"=>soursop_juice.product_unit.id,
                    "product_unit_name"=>soursop_juice.product_unit.name,
                    "sell_price"=>"6100.0",
                    "lock_price"=>true,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]}]
              )
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when CK to vendor, with vendor product, one product has external vendor type in product setting location' do
          let(:location_id) { central_kitchen.id }
          let(:any_external_vendor_type) { 'true' }
          let(:status) { 'activated' }
          let(:exclude_product_with_variances) { 'true' }
          let(:recipe_line_product) { 'true' }
          let(:no_stock) { 'false' }
          let(:vendor_id) { vendor_all_locations.id }

          before do |example|
            latte_setting_location_ck.update!(external_vendor_type: true, is_product_type_active: true)
            latte.update!(external_vendor_type: false)

            spicy_burger
            cheese_burger
            paper_bag

            vendor_product_all_locations_latte
            vendor_product_all_locations_soursop_juice

            Product.reindex
            Product.all.each(&:reindex_es)

            submit_request(example.metadata)
          end

          it 'returns correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            responses = response_body['products'].map do |product|
              { product_id: product['id'], sell_price: product['sell_price'],
                vendor_products: product['vendor_products'] }
            end

            expect(responses)
              .to match_array(
                [{:product_id=>latte.id,
                  :sell_price=>"1800.0",
                  :vendor_products=>
                  [{"product_unit_id"=>latte.product_unit.id,
                    "product_unit_name"=>latte.product_unit.name,
                    "sell_price"=>"7700.0",
                    "lock_price"=>false,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]},
                {:product_id=>soursop_juice.id,
                  :sell_price=>"2000.0",
                  :vendor_products=>
                  [{"product_unit_id"=>soursop_juice.product_unit.id,
                    "product_unit_name"=>soursop_juice.product_unit.name,
                    "sell_price"=>"6100.0",
                    "lock_price"=>true,
                    "internal_tax"=>{"id"=>nil, "name"=>nil, "rate"=>nil},
                    "sell_tax_setting"=>"default"}]}]
              )
            expect(response_body.keys).to match_array ['products', 'paging']
          end
        end

        context 'when subbrand exists' do
          let(:filter_by_sub_brand) { 'true' }

          before do |example|
            outlet_sub_brand_1
            ck_sub_brand_1

            latte
            espresso
            spicy_burger
            cheese_burger
            soursop_juice
            paper_bag

            Product.reindex
          end

          context 'when outlet to CK, 1 subbrand' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to CK, multiple subbrand' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to outlet, both have sub brand' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { owned_branch_2.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to outlet, both have sub brand, but one of them have more categories' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { owned_branch_2.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_3
              outlet_sub_brand_1.update!(product_category_ids: [burgers_category.id])
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to outlet, both have sub brand, but one of them do not have category' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { owned_branch_2.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_3
              outlet_sub_brand_1.update!(product_category_ids: [])
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to outlet, both have sub brand, but both of them do not have category' do
            let(:location_id) { owned_branch_1.id }
            let(:location_to_id) { owned_branch_2.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_3.update!(product_category_ids: [])
              outlet_sub_brand_1.update!(product_category_ids: [])
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([soursop_juice.id, paper_bag.id, cheese_burger.id, espresso.id, latte.id, spicy_burger.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when CK to CK' do
            let(:location_id) { central_kitchen.id }
            let(:location_to_id) { central_kitchen_2.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              latte.location_ids << central_kitchen_2.id
              espresso.location_ids << central_kitchen_2.id
              spicy_burger.location_ids << central_kitchen_2.id
              cheese_burger.location_ids << central_kitchen_2.id
              soursop_juice.location_ids << central_kitchen_2.id
              paper_bag.location_ids << central_kitchen_2.id

              Product.reindex
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([latte.id, espresso.id, spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to vendor and outlet activate procurement_enable_outlet_to_outlet' do
            let(:location_id) { owned_branch_1.id }
            let(:external_vendor_type) { 'true' }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }
            let(:vendor_id) { vendor_1.id }

            before do |example|
              owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)
              LocationsProduct.all.update_all(available_stock_flag_procurement: false)
              submit_request(example.metadata)
            end

            it 'should return correct product response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body['products'].map { |response_product| response_product.dig('available_stock_flag', 'procurement') }).to eql([false, false, false])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when outlet to vendor and outlet deactivate procurement_enable_outlet_to_outlet' do
            let(:location_id) { owned_branch_1.id }
            let(:external_vendor_type) { 'true' }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }
            let(:allow_procurement_out_of_stock_flag) { 'true' }

            before do |example|
              owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: false)
              LocationsProduct.all.update_all(available_stock_flag_procurement: false)
              submit_request(example.metadata)
            end

            it 'should return correct product response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body['products'].map { |response_product| response_product.dig('available_stock_flag', 'procurement') }).to eql([false, false, false])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when CK to vendor' do
            let(:location_id) { central_kitchen.id }
            let(:external_vendor_type) { 'true' }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([latte.id, espresso.id, spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when customer to CK' do
            let(:location_id) { customer.id }
            let(:location_to_id) { central_kitchen.id }
            let(:procurement_from_customer) { 'true' }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([latte.id, espresso.id, spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when customer to outlet' do
            let(:location_id) { customer.id }
            let(:location_to_id) { owned_branch_1.id }
            let(:procurement_from_customer) { 'true' }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to all' do
            let(:all_location_froms) { 'true' }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to all but one of them has less categories' do
            let(:all_location_froms) { 'true' }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              outlet_sub_brand_3.update(product_category_ids: [coffee_drinks_category.id, juice_category.id])
              owned_branch_2
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to all but one of them has no categories' do
            let(:all_location_froms) { 'true' }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              outlet_sub_brand_3

              owned_branch_3

              Product.reindex
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to all but all of them has no categories' do
            let(:all_location_froms) { 'true' }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2.update!(product_category_ids: [])
              outlet_sub_brand_3.update!(product_category_ids: [])
              outlet_sub_brand_1.update!(product_category_ids: [])

              owned_branch_3

              Product.reindex
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([latte.id, espresso.id, spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to one specific locations' do
            let(:all_location_froms) { 'false' }
            let(:location_from_ids) { "#{owned_branch_1.id}" }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              outlet_sub_brand_3
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to multiple specific locations' do
            let(:all_location_froms) { 'false' }
            let(:location_from_ids) { "#{owned_branch_1.id},#{owned_branch_2.id}" }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              outlet_sub_brand_3
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to multiple specific locations, but one of them has less categories' do
            let(:all_location_froms) { 'false' }
            let(:location_from_ids) { "#{owned_branch_1.id},#{owned_branch_2.id}" }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2
              outlet_sub_brand_3.update(product_category_ids: [coffee_drinks_category.id, juice_category.id])
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to multiple specific locations, but one of them has no categories' do
            let(:all_location_froms) { 'false' }
            let(:location_from_ids) { "#{owned_branch_1.id},#{owned_branch_2.id},#{owned_branch_3.id}" }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2

              outlet_sub_brand_3

              owned_branch_3
              Product.reindex
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when bulk order to multiple specific locations, but all of them has no categories' do
            let(:all_location_froms) { 'false' }
            let(:location_from_ids) { "#{owned_branch_1.id},#{owned_branch_2.id},#{owned_branch_3.id}" }
            let(:location_id) { nil }
            let(:location_to_id) { central_kitchen.id }
            let(:status) { 'activated' }
            let(:exclude_product_with_variances) { 'true' }
            let(:recipe_line_product) { 'true' }
            let(:no_stock) { 'false' }

            before do |example|
              outlet_sub_brand_2.update!(product_category_ids: [])
              outlet_sub_brand_3.update!(product_category_ids: [])
              outlet_sub_brand_1.update!(product_category_ids: [])

              owned_branch_3

              Product.reindex
              submit_request(example.metadata)
            end

            it 'returns correct products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].map { |product| product['id'] })
                .to match_array([latte.id, espresso.id, spicy_burger.id, cheese_burger.id, soursop_juice.id, paper_bag.id])
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end
        end

        context 'when filter by deactivated, all active products' do
          let(:status) { 'deactivated' }
          let(:include_variance) { true }

          before do
            latte
            latte_variant_choco
            espresso
            espresso_variant_choco
            spicy_burger
            cheese_burger

            Product.search_index.refresh
          end

          it 'should show empty list' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            products_responses = response_body['products']
            expect(products_responses).to eq([])
          end

          context 'when child variance deactivated but parent still active' do
            before do
              latte_variant_choco.deactivate!(owner)
              Product.search_index.refresh
            end

            it 'should show empty list' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              products_responses = response_body['products']
              result = products_responses.map do |product_response|
                {
                  name: product_response['name'],
                  status: product_response['status'],
                  variances: product_response['variances'].map { |variance| { name: variance['name'], status: variance['status'] } }
                }
              end

              expect(result).to eq(
                [{:name=>"Latte",
                :status=>"deactivated",
                :variances=>[{:name=>"Latte Variant Choco", :status=>"deactivated"}]}]
              )
            end
          end

          context 'when child variance deactivated and parent also deactivated' do
            before do
              latte.deactivate!(owner)
              latte_variant_choco.deactivate!(owner)
              Product.search_index.refresh
            end

            it 'should show empty list' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              products_responses = response_body['products']
              result = products_responses.map do |product_response|
                {
                  name: product_response['name'],
                  status: product_response['status'],
                  variances: product_response['variances'].map { |variance| { name: variance['name'], status: variance['status'] } }
                }
              end

              expect(result).to eq(
                [{:name=>"Latte",
                :status=>"deactivated",
                :variances=>[{:name=>"Latte Variant Choco", :status=>"deactivated"}]}]
              )
            end
          end

          context 'when child variance deactivated and parent also deactivated then reactivated' do
            before do
              latte.deactivate!(owner)
              latte_variant_choco.deactivate!(owner)
              latte.reactivate!(user: owner)
              Product.reindex
            end

            it 'should show empty list' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              products_responses = response_body['products']
              expect(products_responses).to eq([])
            end
          end

          context 'when non variant deactivated' do
            before do
              spicy_burger.deactivate!(owner)
              Product.search_index.refresh
            end

            it 'should show empty list' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              products_responses = response_body['products']
              result = products_responses.map do |product_response|
                {
                  name: product_response['name'],
                  status: product_response['status'],
                  variances: product_response['variances'].map { |variance| { name: variance['name'], status: variance['status'] } }
                }
              end

              expect(result).to eq(
                [{:name=>"Spicy Burger", :status=>"deactivated", :variances=>[]}]
              )
            end
          end
        end

        context 'when exclude category id' do
          let!(:location_id) { central_kitchen.id }
          let(:exclude_category_ids) { "#{coffee_drinks_category.id}" }

          before do |example|
            product.update!(product_category: coffee_drinks_category)
            product_2.update!(product_category: coffee_drinks_category)
            product_3.update!(product_category: coffee_drinks_category)
            product_4.update!(product_category: burgers_category)
            product_6.update!(product_category: burgers_category)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns products without excluded category' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['products'].map { |x| x['product_category']['id'] }).to eq(
              [burgers_category.id, burgers_category.id]
            )
          end
        end

        context 'when with filter location_id & sell_to_procurement_from_customer' do
          let(:sell_to_procurement_from_customer) {true}
          let!(:location_to_id) { central_kitchen.id }

          before do |example|
            banana_juice_all_location_procurement_from_customer

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns product with sell to procurement from customer flag true' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['products', 'paging']

            response_product = response_body['products'].find { |p| p['id'] === banana_juice_all_location_procurement_from_customer.id }
            expect(response_product['sell_to_procurement_from_customer']).to be_truthy
          end
        end

        context 'when filter by product sell_to_pos truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:show_pos_position) { 'true' }
          let(:sell_to_pos) { 'true' }
          let(:include_variance) { 'true' }
          let(:page) { 1 }

          before do |example|
            owned_branch_1

            coffee_drinks_category
            latte_sell_to_pos

            burgers_category
            cheese_burger_external_vendor_type.update(sell_to_pos: false)

            deactivated_product_category
            deactivated_product.update(sell_to_pos: false)

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns category that have product type sell_to_customer_type' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body['products'].count).to eq(1)
            expect(response_body['products'].first['id']).to eq(latte_sell_to_pos.id)
          end
        end

        context 'when has filter check empty stock' do
          let(:location_id) { owned_branch_3.id }
          let(:location_to_id) { owned_branch_2.id }
          let(:status) { 'activated' }
          let(:check_empty_stock) { 'true' }

          before do |example|
            latte
            owned_branch_3.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: true)
            order_transaction_outlet_to_outlet.update_column(:status, 'processing')
          end

          context 'when latte is available' do
            before do |example|
              # latte available_stock = 1000 - 2 = 998 cup 500 ml. 1000 is curr stock, 2 is sum of order lines open qty
              yesterday_owned_branch_2_stock_adjustment_for_procurement

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
              Product.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'returns a valid product response with latte stock_zero_or_below equals to false' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] }).to match_array([latte.id, spicy_burger.id])
              expect(response_body['products'].first['stock_zero_or_below']).to eq false
              expect(response_body['products'].second['stock_zero_or_below']).to eq true
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end

          context 'when latte is not available' do
            before do |example|
              # latte available_stock = 0 - 2 = -2 cup 500 ml. 0 is curr stock, 2 is sum of order lines open qty
              Product.reindex # manually reindex

              submit_request(example.metadata)
            end

            it 'returns a valid product response with latte stock_zero_or_below equals to false' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['products'].map { |product| product['id'] }).to match_array([latte.id, spicy_burger.id])
              expect(response_body['products'].first['stock_zero_or_below']).to eq true
              expect(response_body['products'].second['stock_zero_or_below']).to eq true
              expect(response_body.keys).to match_array ['products', 'paging']
            end
          end
        end
      end

      context 'when checking Stock Availability, and request has location id and location to id' do
        let(:location_id) { franchise_branch_1.id }
        let(:location_to_id) { central_kitchen.id }

        response(200, 'successful') do
          before do |example|
            plastic_bag.update!(location_ids: [franchise_branch_1.id, central_kitchen.id])
            ProductSettingLocation.create!(product: plastic_bag, location: franchise_branch_1, sell_to_shopee_food: false, sell_to_online_ordering: true,
                                           sell_to_grab_food: false, sell_to_go_food: true, sell_to_customer_type: true, is_product_type_active: true)
            ProductSettingLocation.create!(product: plastic_bag, location: central_kitchen, sell_to_shopee_food: true, sell_to_online_ordering: false,
                                           sell_to_grab_food: true, sell_to_go_food: false, sell_to_customer_type: true, is_product_type_active: true)
            Product.reindex
            submit_request(example.metadata)
          end

          it 'should be able to return valid response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            available_stock_flag = response_body['products'].first['available_stock_flag']
            expect(available_stock_flag).to eql({"procurement"=>true, "pos"=>true, "grab_food"=>true, "shopee_food"=>true})
          end
        end
      end

      context 'when checking Stock Availability, and request has location id' do
        let(:location_id) { franchise_branch_1.id }

        response(200, 'successful') do
          before do |example|
            plastic_bag.update!(location_ids: [franchise_branch_1.id, central_kitchen.id])
            ProductSettingLocation.create!(product: plastic_bag, location: franchise_branch_1, sell_to_shopee_food: false, sell_to_online_ordering: true,
                                           sell_to_grab_food: false, sell_to_go_food: true, sell_to_customer_type: true, is_product_type_active: true)
            ProductSettingLocation.create!(product: plastic_bag, location: central_kitchen, sell_to_shopee_food: true, sell_to_online_ordering: false,
                                           sell_to_grab_food: true, sell_to_go_food: false, sell_to_customer_type: true, is_product_type_active: true)

            Product.reindex
            submit_request(example.metadata)
          end

          it 'should be able to return valid response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            available_stock_flag = response_body['products'].first['available_stock_flag']
            expect(franchise_branch_1.procurement_enable_outlet_to_outlet).to be_falsey
            expect(available_stock_flag).to eql({"pos"=>true, "go_food"=>true, "online_ordering"=>true})
          end
        end
      end

      context 'when filter by platform and availability' do
        let(:location_id) { owned_branch_1.id }

        context 'when sell to pos true and available flag true' do
          let(:sell_to_pos) { 'true' }
          let(:available_stock_flag_pos) { 'true' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_pos: true)
              plastic_bag.update(sell_to_pos: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_pos: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(1)

              product_response = response_body['products'].first
              expect(product_response['id']).to eq(plastic_bag.id)
            end
          end
        end

        context 'when sell to channel true and available flag false' do
          let(:sell_to_grab_food) { 'true' }
          let(:available_stock_flag_grab_food) { 'false' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_grab_food: true)
              plastic_bag.update(sell_to_grab_food: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(1)

              product_response = response_body['products'].first
              expect(product_response['id']).to eq(latte.id)
            end
          end
        end

         context 'when sell to channel true and not send available flag' do
          let(:sell_to_grab_food) { 'true' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_grab_food: true)
              plastic_bag.update(sell_to_grab_food: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(2)
              expect(response_body['products'].pluck('id')).to match_array([latte.id, plastic_bag.id])
            end
          end
        end

         context 'when not send sell to channel but send available flag true' do
          let(:available_stock_flag_grab_food) { 'false' }

          response(200, 'successful', document: false) do
            before do |example|
              latte
              plastic_bag

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(2)
              expect(response_body['products'].pluck('id')).to match_array([latte.id, plastic_bag.id])
            end
          end
        end
      end

      context 'when filter by location group' do
        response(200, 'successful', document: false) do
          let(:sell_to_customer_type) { 'true' }
          let(:status) { 'activated' }
          let(:page) { 1 }
          let(:item_per_page) { 10 }
          let(:exclude_product_with_variances) { 'true' }
          let(:location_group_id) { owned_branch_location_groups.id }

          let(:latte_on_owned_branch_1) do
            create(
              :product_setting_location,
              product: latte,
              location: owned_branch_1,
              is_product_type_active: true,
              sell_to_customer_type: false
            )
          end

          let(:latte_on_owned_branch_2) do
            create(
              :product_setting_location,
              product: latte,
              location: owned_branch_2,
              is_product_type_active: true,
              sell_to_customer_type: false
            )
          end

          before do
            owned_branch_1
            owned_branch_2

            owned_branch_location_groups

            latte

            Product.search_index.refresh
            Location.search_index.refresh
          end

          context 'when product sell to customer is false under group for all location' do
            before do
              latte_on_owned_branch_1
              latte_on_owned_branch_2

              latte.reindex
              Product.search_index.refresh
            end

            it 'should not returns any products' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array ['products', 'paging']

              products = response_body['products']
              expect(products.size).to eq(0)
            end
          end

          context 'when product sell to customer is false under group for some location' do
            before do
              latte_on_owned_branch_1

              latte.reindex
              Product.search_index.refresh
            end

            it 'should not returns any products' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array ['products', 'paging']

              products = response_body['products']
              expect(products.size).to eq(1)

              product = products.last
              expect(product['id']).to eq(latte.id)
              expect(product['name']).to eq(latte.name)
            end
          end
        end
      end

      context 'when keyword is too long or invalid for name/code search' do
        response(400, 'bad request') do
          let(:location_id) { franchise_branch_1.id }
          let(:location_to_id) { central_kitchen.id }
          let(:keyword) do
            "Classy Bag Laptop - 11 Inch\t2070220990147\t200,000.00 " * 20
          end

          before do |example|
            cheese_burger
            latte

            Product.search_index.refresh
            submit_request(example.metadata)
          end

          it 'returns 400 with keyword format error' do |example|
            assert_response_matches_metadata(example.metadata)

            expect(response.body).to include("Keyword too long (max 100 characters)")
          end
        end
      end
    end

    post('create product') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'X-Locale', in: :header, type: :string, required: false
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_product'
      }

      context 'with default params' do
        response(201, 'successful') do
          before do |example|
            product
            product_2
            product_3

            submit_request(example.metadata)
          end

          let(:param) do
            param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                      product_setting_locations_attributes: [create_setting_params],
                                                      print_category_id: tea_category.id, name: 'test product name') }
            param
          end

          it 'returns a valid 200 response', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            assert_response_matches_metadata(example.metadata)
            expect(Product.count).to eq(4)

            expect(response_body['product']['name']).to eq('test product name')
            expect(response_body['product']['print_category_id']).to eq(tea_category.id)
          end
        end
      end

      context 'with product group' do
        response(201, 'successful') do
          before do |example|
            submit_request(example.metadata)
          end

          let(:param) do
            param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                      product_setting_locations_attributes: [create_setting_params],
                                                      product_group_ids: [sweet_product_group.id, spicy_product_group.id],
                                                      print_category_id: tea_category.id, name: 'test product name') }
            param
          end

          it 'returns a valid 200 response', bullet: :skip do |example|
            assert_response_matches_metadata(example.metadata)
            _response_body = JSON.parse(response.body)

            product = Product.first
            expect(product.product_groups.size).to eq(2)
            expect(product.product_group_products.size).to eq(2)

            expect(product.product_groups.pluck(:name)).to match_array(
              ["Sweet", "Spicy"]
            )
          end
        end
      end

      context 'with unit conversions params' do
        response(201, 'success', document: false) do
          let(:param) { { product: product_with_unit_conversion_params } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProductUnitConversion, :count).by(1)
          end
        end
      end

      context 'with procurement units params, blank unit conversion' do
        response(400, 'invalid params') do
          let(:param) { { product: invalid_product_with_procurement_units_params_blank_unit_conversions } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(0)
               .and change(ProcurementUnit, :count).by(0)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              { 'message' => 'product unit liter not present in unit conversions or smallest unit' }
            )
          end
        end
      end

      context 'with blank procurement units' do
        response(201, 'success', document: false) do
          let(:param) { { product: blank_procurement_units } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with blank procurement units and conversion units' do
        response(201, 'success', document: false) do
          let(:param) { { product: blank_procurement_units_and_conversion_units } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with procurement units params, partial valid' do
        response(400, 'invalid params', document: false) do
          let(:param) { { product: partially_valid_product_with_procurement_units_params } }

          it 'should not be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(0)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              { 'message' => 'product unit galon, galon jumbo not present in unit conversions or smallest unit' }
            )
          end
        end
      end

      context 'with invalid procurement units params' do
        response(400, 'invalid params', document: false) do
          let(:param) { { product: invalid_product_with_procurement_units_params } }

          it 'should not be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(0)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              { 'message' => 'product unit liter not present in unit conversions or smallest unit' }
            )
          end
        end
      end

      context 'with valid procurement units params' do
        response(201, 'invalid params', document: false) do
          let(:param) { { product: product_with_procurement_units_params } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(1)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with valid procurement units params, smallest only' do
        response(201, 'invalid params', document: false) do
          let(:param) { { product: product_with_procurement_units_smallest_only } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(1)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with valid procurement units params, both conversion unit and smallest' do
        response(201, 'invalid params', document: false) do
          let(:param) { { product: product_with_procurement_units_params_with_smallest } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(2)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when is_select_all_location false but assign location ids' do
        response(201, 'success', document: false) do
          let(:param) do
            {
              "product": {
                "name": 'Bubur Kacang',
                "sku": 'Bubur-3',
                "internal_produce_type": true,
                "product_unit_id": product_unit.id,
                "back_office_unit_id": product_unit.id,
                "is_select_all_location": false,
                "assigned_location_ids": [
                  owned_branch_1.id
                ],
                "exclude_location_ids": [],
                "owner_location_id": owned_branch_1.id
              }
            }
          end

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with tax params' do
        response(201, 'success', document: false) do
          let(:param) do
            { product: build(:product_params, name: SecureRandom.uuid, owner_location_id: central_kitchen.id, product_unit_id: product_unit.id, tax: tax) }
          end

          it 'should be able to create product with tax' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProcurementUnit, :count).by(0)
               .and change(ProductUnitConversion, :count).by(0)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with product price per order type params' do
        response(201, 'success', document: false) do
          let(:online_ordering_order_type) { create(:online_ordering_order_type) }
          let(:param) do
            {
              product: build(
                :product_params,
                name: SecureRandom.uuid,
                sell_price: '12121',
                modifier: true,
                internal_distribution_type: false,
                external_vendor_type: false,
                internal_produce_type: false,
                sell_to_customer_type: true,
                sell_unit_id: product_unit.id,
                product_unit_id: product_unit.id,
                owner_location_id: central_kitchen.id,
                is_select_all_location: false,
                location_ids: [central_kitchen.id],
                exclude_location_ids: [],
                product_internal_price_locations_attributes: [],
                product_setting_locations_attributes: [],
                product_unit_conversions_attributes: [
                  {
                    product_unit_id: other_product_unit.id,
                    converted_qty: "4",
                    internal_price: ""
                  }
                ],
                product_price_per_order_types_attributes: [
                  {
                    order_type_id: online_ordering_order_type.id,
                    sell_price: '10000',
                    sell_tax_id: nil,
                    sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['default']
                  }
                ],
                sell_to_dine_in: true,
                sell_to_grab_food: true,
                sell_to_go_food: true,
                sell_to_online_ordering: true,
                sell_to_shopee_food: true
              )
            }
          end

          context 'when other brand has product without variance' do
            let(:other_brand_unit) { create(:product_unit, name: "cup 500 ml", brand: other_brand) }
            let(:other_brand_latte) do
              create(
                :product,
                owner_location_id: other_brand_central_kitchen.id,
                name: 'Latte other brand',
                sku: 'latte-other-brand',
                brand: other_brand,
                product_unit: other_brand_unit,
                internal_price: 1500,
                sell_price: 1800)
            end

            before do |example|
              brand
              latte

              other_brand
              other_brand_latte
            end

            it 'should be able to create product and unit conversion and product price order type for product brand without affecting other brand' do |example|
              expect do
                submit_request(example.metadata)
              end.to change(Product, :count).by(1)
                 .and change(ProductPricePerOrderType, :count).by(1)
                 .and change(ProductUnitConversion, :count).by(1)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              response_product_id = response_body['product']['id']
              response_product = Product.find_by(id: response_product_id)
              expect(response_product.present?).to be_truthy
            end
          end

          context 'with conversion qty equal to 0' do
            response(422, 'unprocessable entity') do
              let(:param) { { product: product_with_unit_conversion_params } }
              before do |example|
                param[:product]["product_unit_conversions_attributes"][0]["converted_qty"] = 0
                submit_request(example.metadata)
              end

              it 'should raise error unit conversion cannot be 0' do
                response_body = JSON.parse(response.body)
                expect(response).to have_http_status(:unprocessable_entity)
                expect(response_body["errors"]["product_unit_conversions[0].converted_qty"])
                  .to eq(["Product unit conversions[0] converted qty Unit conversion cannot be 0"])
              end
            end
          end
        end
      end

      context 'with outlet to outlet procurement units params' do
        response(201, 'success', document: false) do
          let(:param) { { product: product_with_outlet_to_outlet_procurement_units_params } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with outlet to outlet procurement units params but do not choose order type' do
        response(422, 'unprocessable_entity', document: false) do
          let(:param) { { product: product_with_outlet_to_outlet_procurement_units_params.merge({
            internal_distribution_type: false,
            external_vendor_type: false,
            internal_produce_type: false
          }) } }
          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to create product with product unit conversions' do
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:unprocessable_entity)
            expect(response_body).to eq({"errors"=>{"product_setting_location"=>["Product setting location must at least choose one type"]}})
            expect(Product.count).to eq(0)
            expect(OutletToOutletProcurementUnit.count).to eq(0)
            expect(ProductUnitConversion.count).to eq(0)
          end
        end
      end

      context 'with vendor procurement units params' do
        response(201, 'success', document: false) do
          let(:param) { { product: product_with_vendor_procurement_units_params } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(VendorProcurementUnit, :count).by(1)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with central kitchen procurement units params' do
        response(201, 'success', document: false) do
          let(:param) { { product: product_with_central_kitchen_procurement_units_params } }

          it 'should be able to create product with product unit conversions' do |example|
            expect do
              submit_request(example.metadata)
            end.to change(Product, :count).by(1)
               .and change(CentralKitchenProcurementUnit, :count).by(1)
               .and change(ProductUnitConversion, :count).by(1)
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'with outlet to outlet procurement units params but do not choose order type' do
        response(422, 'unprocessable_entity', document: false) do
          let(:param) { { product: product_with_vendor_procurement_units_params.merge({
            internal_distribution_type: false,
            external_vendor_type: false,
            internal_produce_type: false
          }) } }
          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to create product with product unit conversions' do
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:unprocessable_entity)
            expect(response_body).to eq({"errors"=>{"product_setting_location"=>["Product setting location must at least choose one type"]}})
            expect(Product.count).to eq(0)
            expect(VendorProcurementUnit.count).to eq(0)
            expect(ProductUnitConversion.count).to eq(0)
          end
        end
      end

      context 'when with id locale' do
        let('X-Locale') { 'id' }

        response(422, 'Unexpected Response') do
          before do |example|
            submit_request(example.metadata)
          end

          let(:param) do
            param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                      product_setting_locations_attributes: [create_setting_params]) }
            param[:product][:name] = product_3.name
            param
          end

          it 'should raise error product name already exist' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response_body).to eq("errors" => {"name"=>["Nama sudah dipakai"]})
          end
        end
      end

      context 'when with en locale' do
        let('X-Locale') { 'en' }

        response(422, 'Unexpected Response') do
          before do |example|
            submit_request(example.metadata)
          end

          let(:param) do
            param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                      product_setting_locations_attributes: [create_setting_params]) }
            param[:product][:name] = product_3.name
            param
          end

          it 'should raise error product name already exist' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response_body).to eq("errors" => {"name"=>["Name is used"]})
          end
        end
      end

      context 'with location group' do
        context 'when select location group ids' do
          response(201, 'successful') do
            before do |example|
              submit_request(example.metadata)
            end

            let(:param) do
              param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                        location_group_ids: [owned_branch_location_groups.id],
                                                        print_category_id: tea_category.id, name: 'test product name') }
              param
            end

            it 'should be able to create product with selected locations', bullet: :skip do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              assert_response_matches_metadata(example.metadata)
              expect(Product.count).to eq(1)

              expect(response_body['product']['name']).to eq('test product name')
              expect(response_body['product']['print_category_id']).to eq(tea_category.id)

              location_ids = []
              location_ids += [central_kitchen.id]
              location_ids += owned_branch_location_groups.locations.map { |x| x.id }
              expect(response_body['product']['locations'].map { |x| x['id'] }).to eq(location_ids)
            end
          end
        end

        context 'when select all location groups' do
          response(201, 'successful') do
            before do |example|
              owned_branch_location_groups
              owned_branch_location_groups_2
              submit_request(example.metadata)
            end

            let(:param) do
              param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                        is_select_all_location_group: true,
                                                        print_category_id: tea_category.id, name: 'test product name') }
              param
            end

            it 'should be able to create product with selected locations', bullet: :skip do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              assert_response_matches_metadata(example.metadata)
              expect(Product.count).to eq(1)

              expect(response_body['product']['name']).to eq('test product name')
              expect(response_body['product']['print_category_id']).to eq(tea_category.id)

              location_ids = []
              location_ids += [central_kitchen.id]
              location_ids += owned_branch_location_groups.locations.map { |x| x.id }
              location_ids += owned_branch_location_groups_2.locations.map { |x| x.id }
              expect(response_body['product']['locations'].map { |x| x['id'] }).to eq(location_ids)
            end
          end
        end

        context 'when select all location groups and exclude 1 location group' do
          response(201, 'successful') do
            before do |example|
              owned_branch_location_groups
              owned_branch_location_groups_2
              submit_request(example.metadata)
            end

            let(:param) do
              param = { product: build(:product_params, product_unit_id: product_unit.id, owner_location_id: central_kitchen.id,
                                                        is_select_all_location_group: true,
                                                        exclude_location_group_ids: [owned_branch_location_groups.id],
                                                        print_category_id: tea_category.id, name: 'test product name') }
              param
            end

            it 'should be able to create product with selected locations', bullet: :skip do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              assert_response_matches_metadata(example.metadata)
              expect(Product.count).to eq(1)

              expect(response_body['product']['name']).to eq('test product name')
              expect(response_body['product']['print_category_id']).to eq(tea_category.id)

              location_ids = []
              location_ids += [central_kitchen.id]
              location_ids += owned_branch_location_groups_2.locations.map { |x| x.id }
              expect(response_body['product']['locations'].map { |x| x['id'] }).to eq(location_ids)
            end
          end
        end
      end
    end
  end

  path '/api/products/backoffice_index', search: true, bullet: :skip do
    get('list products') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :ids, in: :query, type: :string, required: false
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :location_from_ids, in: :query, type: :string, required: false, description: "comma separated numbers"
      parameter name: :all_location_froms, in: :query, type: :string, required: false
      parameter name: :include_variance, in: :query, type: :string, required: false
      parameter name: :show_pos_position, in: :query, type: :string, required: false
      parameter name: :exclude_used_in_recipe_line, in: :query, type: :string, required: false
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :status, in: :query, type: :string, required: false
      parameter name: :only_contain_recipe, in: :query, type: :string, required: false
      parameter name: :recipe_status, in: :query, type: :string, required: false
      parameter name: :recipe_type, in: :query, type: :string, required: false
      parameter name: :group_category, in: :query, type: :string, required: false
      parameter name: :include_variance, in: :query, type: :string, required: false
      parameter name: :exclude_product_with_variances, in: :query, type: :string, required: false
      parameter name: :out_of_stock_flag, in: :query, type: :string, required: false
      parameter name: :external_vendor_type, in: :query, type: :string, required: false
      parameter name: :any_external_vendor_type, in: :query, type: :string, required: false
      parameter name: :vendor_id, in: :query, type: :string, required: false
      parameter name: :internal_distribution_type, in: :query, type: :string, required: false
      parameter name: :sell_to_customer_type, in: :query, type: :string, required: false
      parameter name: :internal_produce_type, in: :query, type: :string, required: false
      parameter name: :recipe_line_product, in: :query, type: :string, required: false
      parameter name: :show_pos_position, in: :query, type: :string, required: false
      parameter name: :wbo, in: :query, type: :string, required: false
      parameter name: :category_ids, in: :query, type: :string, required: false
      parameter name: :exclude_categories_id, in: :query, type: :string, required: false
      parameter name: :exclude_ids, in: :query, type: :string, required: false
      parameter name: :presentation, in: :query, type: :string, enum: ProductQuery::PRODUCT_PRESENTATIONS.keys, required: false
      parameter name: :sell_to_pos, in: :query, type: :string, required: false
      parameter name: :sell_to_kiosk, in: :query, type: :string, required: false
      parameter name: :sell_to_dine_in, in: :query, type: :string, required: false
      parameter name: :sell_to_grab_food, in: :query, type: :string, required: false
      parameter name: :sell_to_go_food, in: :query, type: :string, required: false
      parameter name: :sell_to_shopee_food, in: :query, type: :string, required: false
      parameter name: :sell_to_online_ordering, in: :query, type: :string, required: false
      parameter name: :sell_to_procurement_from_customer, in: :query, type: :string, required: false
      parameter name: :sell_to_procurement, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_procurement, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_pos, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_grab_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_go_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_shopee_food, in: :query, type: :boolean, required: false
      parameter name: :available_stock_flag_online_ordering, in: :query, type: :boolean, required: false
      parameter name: :exclude_used_in_recipe_line, in: :query, type: :string, required: false
      parameter name: :no_stock, in: :query, type: :string, required: false
      parameter name: :skip_child_variances, in: :query, type: :string, required: false
      parameter name: :variance_presentations, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_variance_presentations' }
      parameter name: :sort_key, in: :query, type: :string, enum: ['asc'], required: false
      parameter name: :sort_order, in: :query, type: :string, enum: ['product_category_name', 'sku', 'name'], required: false
      parameter name: :filter_by_sub_brand, in: :query, type: :string, required: false
      parameter name: :procurement_from_customer, in: :query, type: :string, required: false
      parameter name: :allow_procurement_out_of_stock_flag, in: :query, type: :string, required: false
      parameter name: :exclude_category_ids, in: :query, type: :string, required: false
      parameter name: :show_permissions, in: :query, type: :string, required: false
      parameter name: :is_select_all_location, in: :query, type: :string, required: false
      parameter name: :keyword, in: :query, type: :string, required: false

      let(:location_id) { central_kitchen.id }

      context 'when product has variance' do
        response(200, 'ok') do
          let(:wbo) { 'true' }
          let(:include_variance) { 'true' }
          let(:show_permissions) { 'true' }

          before do |example|
            latte
            spicy_burger
            cheese_burger
            latte_variant_choco
            latte_variant_cherry
            espresso_variant_choco

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body).to eq(
              {"products"=>
              [{"id"=>3,
                "name"=>"Cheese Burger",
                "sku"=>"cheese_burger",
                "status"=>"activated",
                "sell_price"=>"5500.0",
                "internal_price"=>"4500.0",
                "internal_distribution_type"=>true,
                "available_stock_flag"=>
                  {"procurement"=>true,
                  "pos"=>true,
                  "grab_food"=>true,
                  "go_food"=>true,
                  "kiosk"=>true,
                  "shopee_food"=>true,
                  "online_ordering"=>true},
                "image_url"=>"",
                "variance_parent_product_id"=>nil,
                "sell_to_customer_type"=>true,
                "sell_to_pos"=>true,
                "sell_to_kiosk"=>true,
                "sell_to_dine_in"=>true,
                "sell_to_grab_food"=>true,
                "sell_to_go_food"=>true,
                "sell_to_shopee_food"=>true,
                "sell_to_online_ordering"=>true,
                "sell_to_procurement_from_customer"=>false,
                "product_category"=>{"id"=>2, "name"=>"Burgers"},
                "product_unit"=>{"id"=>3, "name"=>"piece"},
                "sell_unit"=>{"id"=>3, "name"=>"piece"},
                "back_office_unit"=>{"id"=>3, "name"=>"piece"},
                "central_kitchen_back_office_unit"=>nil,
                "outlet_back_office_unit"=>nil,
                "variances"=>[],
                "product_internal_price_locations"=>[],
                "product_procurement_units"=>
                  [{"id"=>3,
                    "sequence"=>0,
                    "product_unit"=>{"id"=>3, "name"=>"piece"},
                    "internal_price"=>"4500.0"}],
                "can_update"=>true,
                "can_reactivate"=>true,
                "can_deactivate"=>true},
                {"id"=>6,
                "name"=>"Espresso",
                "sku"=>"espresso",
                "status"=>"activated",
                "sell_price"=>"2800.0",
                "internal_price"=>"2500.0",
                "internal_distribution_type"=>true,
                "available_stock_flag"=>
                  {"procurement"=>true,
                  "pos"=>true,
                  "grab_food"=>true,
                  "go_food"=>true,
                  "kiosk"=>true,
                  "shopee_food"=>true,
                  "online_ordering"=>true},
                "image_url"=>"",
                "variance_parent_product_id"=>nil,
                "sell_to_customer_type"=>true,
                "sell_to_pos"=>true,
                "sell_to_kiosk"=>true,
                "sell_to_dine_in"=>true,
                "sell_to_grab_food"=>true,
                "sell_to_go_food"=>true,
                "sell_to_shopee_food"=>true,
                "sell_to_online_ordering"=>true,
                "sell_to_procurement_from_customer"=>false,
                "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                "product_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                "sell_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                "back_office_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                "central_kitchen_back_office_unit"=>nil,
                "outlet_back_office_unit"=>nil,
                "variances"=>
                  [{"id"=>7,
                    "name"=>"Espresso Variant Choco",
                    "sku"=>"espresso-variant-choco",
                    "status"=>"activated",
                    "sell_price"=>"2800.0",
                    "internal_price"=>"2500.0",
                    "internal_distribution_type"=>true,
                    "available_stock_flag"=>
                    {"procurement"=>true,
                      "pos"=>true,
                      "grab_food"=>true,
                      "go_food"=>true,
                      "shopee_food"=>true,
                      "kiosk"=>true,
                      "online_ordering"=>true},
                    "image_url"=>"",
                    "variance_parent_product_id"=>6,
                    "sell_to_customer_type"=>true,
                    "sell_to_pos"=>true,
                    "sell_to_kiosk"=>true,
                    "sell_to_dine_in"=>true,
                    "sell_to_grab_food"=>true,
                    "sell_to_go_food"=>true,
                    "sell_to_shopee_food"=>true,
                    "sell_to_online_ordering"=>true,
                    "sell_to_procurement_from_customer"=>false,
                    "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                    "product_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                    "sell_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                    "back_office_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                    "central_kitchen_back_office_unit"=>nil,
                    "outlet_back_office_unit"=>nil,
                    "product_internal_price_locations"=>[],
                    "product_procurement_units"=>
                    [{"id"=>7,
                      "sequence"=>0,
                      "product_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                      "internal_price"=>"2500.0"}]}],
                "product_internal_price_locations"=>[],
                "product_procurement_units"=>
                  [{"id"=>6,
                    "sequence"=>0,
                    "product_unit"=>{"id"=>4, "name"=>"cup 300 ml"},
                    "internal_price"=>"2500.0"}],
                "can_update"=>true,
                "can_reactivate"=>true,
                "can_deactivate"=>true},
                {"id"=>1,
                "name"=>"Latte",
                "sku"=>"latte",
                "status"=>"activated",
                "sell_price"=>"1800.0",
                "internal_price"=>"1500.0",
                "internal_distribution_type"=>true,
                "available_stock_flag"=>
                  {"procurement"=>true,
                  "pos"=>true,
                  "grab_food"=>true,
                  "go_food"=>true,
                  "kiosk"=>true,
                  "shopee_food"=>true,
                  "online_ordering"=>true},
                "image_url"=>"",
                "variance_parent_product_id"=>nil,
                "sell_to_customer_type"=>true,
                "sell_to_pos"=>true,
                "sell_to_kiosk"=>true,
                "sell_to_dine_in"=>true,
                "sell_to_grab_food"=>true,
                "sell_to_go_food"=>true,
                "sell_to_shopee_food"=>true,
                "sell_to_online_ordering"=>true,
                "sell_to_procurement_from_customer"=>false,
                "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                "sell_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                "back_office_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                "central_kitchen_back_office_unit"=>nil,
                "outlet_back_office_unit"=>nil,
                "variances"=>
                  [{"id"=>4,
                    "name"=>"Latte Variant Choco",
                    "sku"=>"latte-variant-choco",
                    "status"=>"activated",
                    "sell_price"=>"2800.0",
                    "internal_price"=>"2500.0",
                    "internal_distribution_type"=>true,
                    "available_stock_flag"=>
                    {"procurement"=>true,
                      "pos"=>true,
                      "grab_food"=>true,
                      "go_food"=>true,
                      "kiosk"=>true,
                      "shopee_food"=>true,
                      "online_ordering"=>true},
                    "image_url"=>"",
                    "variance_parent_product_id"=>1,
                    "sell_to_customer_type"=>true,
                    "sell_to_pos"=>true,
                    "sell_to_kiosk"=>true,
                    "sell_to_dine_in"=>true,
                    "sell_to_grab_food"=>true,
                    "sell_to_go_food"=>true,
                    "sell_to_shopee_food"=>true,
                    "sell_to_online_ordering"=>true,
                    "sell_to_procurement_from_customer"=>false,
                    "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                    "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "sell_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "back_office_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "central_kitchen_back_office_unit"=>nil,
                    "outlet_back_office_unit"=>nil,
                    "product_internal_price_locations"=>[],
                    "product_procurement_units"=>
                    [{"id"=>4,
                      "sequence"=>0,
                      "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                      "internal_price"=>"2500.0"}]},
                  {"id"=>5,
                    "name"=>"Latte Variant cherry",
                    "sku"=>"latte-variant-cherry",
                    "status"=>"activated",
                    "sell_price"=>"2800.0",
                    "internal_price"=>"2500.0",
                    "internal_distribution_type"=>true,
                    "available_stock_flag"=>
                    {"procurement"=>true,
                      "pos"=>true,
                      "grab_food"=>true,
                      "go_food"=>true,
                      "kiosk"=>true,
                      "shopee_food"=>true,
                      "online_ordering"=>true},
                    "image_url"=>"",
                    "variance_parent_product_id"=>1,
                    "sell_to_customer_type"=>true,
                    "sell_to_pos"=>true,
                    "sell_to_kiosk"=>true,
                    "sell_to_dine_in"=>true,
                    "sell_to_grab_food"=>true,
                    "sell_to_go_food"=>true,
                    "sell_to_shopee_food"=>true,
                    "sell_to_online_ordering"=>true,
                    "sell_to_procurement_from_customer"=>false,
                    "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                    "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "sell_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "back_office_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "central_kitchen_back_office_unit"=>nil,
                    "outlet_back_office_unit"=>nil,
                    "product_internal_price_locations"=>[],
                    "product_procurement_units"=>
                    [{"id"=>5,
                      "sequence"=>0,
                      "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                      "internal_price"=>"2500.0"}]}],
                "product_internal_price_locations"=>[],
                "product_procurement_units"=>
                  [{"id"=>1,
                    "sequence"=>0,
                    "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "internal_price"=>"1500.0"}],
                "can_update"=>true,
                "can_reactivate"=>true,
                "can_deactivate"=>true},
                {"id"=>2,
                "name"=>"Spicy Burger",
                "sku"=>"spicy_burger",
                "status"=>"activated",
                "sell_price"=>"6500.0",
                "internal_price"=>"6000.0",
                "internal_distribution_type"=>true,
                "available_stock_flag"=>
                  {"procurement"=>true,
                  "pos"=>true,
                  "grab_food"=>true,
                  "go_food"=>true,
                  "kiosk"=>true,
                  "shopee_food"=>true,
                  "online_ordering"=>true},
                "image_url"=>"",
                "variance_parent_product_id"=>nil,
                "sell_to_customer_type"=>true,
                "sell_to_pos"=>true,
                "sell_to_kiosk"=>true,
                "sell_to_dine_in"=>true,
                "sell_to_grab_food"=>true,
                "sell_to_go_food"=>true,
                "sell_to_shopee_food"=>true,
                "sell_to_online_ordering"=>true,
                "sell_to_procurement_from_customer"=>false,
                "product_category"=>{"id"=>2, "name"=>"Burgers"},
                "product_unit"=>{"id"=>3, "name"=>"piece"},
                "sell_unit"=>{"id"=>3, "name"=>"piece"},
                "back_office_unit"=>{"id"=>3, "name"=>"piece"},
                "central_kitchen_back_office_unit"=>nil,
                "outlet_back_office_unit"=>nil,
                "variances"=>[],
                "product_internal_price_locations"=>[],
                "product_procurement_units"=>
                  [{"id"=>2,
                    "sequence"=>0,
                    "product_unit"=>{"id"=>3, "name"=>"piece"},
                    "internal_price"=>"6000.0"}],
                "can_update"=>true,
                "can_reactivate"=>true,
                "can_deactivate"=>true}],
              "paging"=>
              {"current_page"=>1, "total_item"=>4, "next_page"=>nil, "prev_page"=>nil}}
            )
          end
        end
      end

      context 'when filter with out_of_stock_flag' do
        response(200, 'ok') do
          let(:out_of_stock_flag) { 'true' }

          before do |example|
            latte
            spicy_burger
            cheese_burger
            latte_variant_choco
            latte_variant_cherry
            espresso_variant_choco

            LocationsProduct.find_by(product: latte, location: central_kitchen)
                            .update(available_stock_flag_grab_food: false,
                                    available_stock_flag_kiosk: false,
                                    options_available_stock_flag_grab_food: false,
                                    available_stock_flag_by_user_grab_food: false)
            latte.reload.reindex(:search_location_setting_v2)

            Product.search_index.refresh

            brand.procurement_setting.update(out_of_stock_restriction: true)

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"products"=>
                [{"id"=>1,
                  "name"=>"Latte",
                  "sku"=>"latte",
                  "status"=>"activated",
                  "sell_price"=>"1800.0",
                  "internal_price"=>"1500.0",
                  "internal_distribution_type"=>true,
                  "available_stock_flag"=>
                  {"procurement"=>true,
                    "pos"=>true,
                    "kiosk"=>false,
                    "grab_food"=>false,
                    "go_food"=>true,
                    "shopee_food"=>true,
                    "online_ordering"=>true},
                  "image_url"=>"",
                  "variance_parent_product_id"=>nil,
                  "sell_to_customer_type"=>true,
                  "sell_to_pos"=>true,
                  "sell_to_kiosk"=>true,
                  "sell_to_dine_in"=>true,
                  "sell_to_grab_food"=>true,
                  "sell_to_go_food"=>true,
                  "sell_to_shopee_food"=>true,
                  "sell_to_online_ordering"=>true,
                  "sell_to_procurement_from_customer"=>false,
                  "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                  "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                  "sell_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                  "back_office_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                  "central_kitchen_back_office_unit"=>nil,
                  "outlet_back_office_unit"=>nil,
                  "product_internal_price_locations"=>[],
                  "product_procurement_units"=>
                  [{"id"=>1,
                    "sequence"=>0,
                    "product_unit"=>{"id"=>2, "name"=>"cup 500 ml"},
                    "internal_price"=>"1500.0"}]}],
              "paging"=>
                {"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil}}
            )
          end
        end
      end

      context 'when filter by platform and availability' do
        let(:location_id) { owned_branch_1.id }

        context 'when sell to pos true and available flag true' do
          let(:sell_to_pos) { 'true' }
          let(:available_stock_flag_pos) { 'true' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_pos: true)
              plastic_bag.update(sell_to_pos: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_pos: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(1)

              product_response = response_body['products'].first
              expect(product_response['id']).to eq(plastic_bag.id)
            end
          end
        end

        context 'when sell to channel true and available flag false' do
          let(:sell_to_grab_food) { 'true' }
          let(:available_stock_flag_grab_food) { 'false' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_grab_food: true)
              plastic_bag.update(sell_to_grab_food: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(1)

              product_response = response_body['products'].first
              expect(product_response['id']).to eq(latte.id)
            end
          end
        end

        context 'when sell to channel true and not send available flag' do
          let(:sell_to_grab_food) { 'true' }

          response(200, 'successful', document: false) do
            before do |example|
              latte.update(sell_to_grab_food: true)
              plastic_bag.update(sell_to_grab_food: true)

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(2)
              expect(response_body['products'].pluck('id')).to match_array([latte.id, plastic_bag.id])
            end
          end
        end

        context 'when not send sell to channel but send available flag true' do
          let(:available_stock_flag_grab_food) { 'false' }

          response(200, 'successful', document: false) do
            before do |example|
              latte
              plastic_bag

              LocationsProduct.find_by(product: latte, location: owned_branch_1).update(available_stock_flag_by_user_grab_food: false)
              latte.reload.reindex(:search_location_setting_v2)

              Product.search_index.refresh
              submit_request(example.metadata)
            end

            it 'should be able to return valid response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['products'].size).to eq(2)
              expect(response_body['products'].pluck('id')).to match_array([latte.id, plastic_bag.id])
            end
          end
        end

        context 'when filter by product sell_to_kiosk truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:sell_to_kiosk) { 'true' }
          let(:page) { 1 }

          before do |example|
            owned_branch_1

            coffee_drinks_category
            latte_sell_to_kiosk
            latte_sell_to_pos

            Product.search_index.refresh

            submit_request(example.metadata)
          end

          response(200, 'successful', document: false) do
            it 'return product which sell_to_kiosk only' do |example|
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body['products'].count).to eq(1)
              expect(response_body['products'].first['id']).to eq(latte_sell_to_kiosk.id)
            end
          end
        end
      end

      context 'when select all location and presentation is all_locations' do
        context 'when product has variance' do
          response(200, 'ok') do
            let(:is_select_all_location) { 'true' }
            let(:presentation) { 'all_locations' }

            before do |example|
              latte
              spicy_burger
              cheese_burger
              latte_variant_choco
              latte_variant_cherry
              espresso_variant_choco

              Product.search_index.refresh

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"products"=>
                  [{"id"=>3,
                    "name"=>"Cheese Burger",
                    "sku"=>"cheese_burger",
                    "image_url"=>"",
                    "product_category"=>{"id"=>2, "name"=>"Burgers"},
                    "status"=>"activated",
                    "is_multi_location"=>true,
                    "location_name"=>"2 locations"},
                  {"id"=>6,
                    "name"=>"Espresso",
                    "sku"=>"espresso",
                    "image_url"=>"",
                    "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                    "status"=>"activated",
                    "is_multi_location"=>true,
                    "location_name"=>"2 locations"},
                  {"id"=>1,
                    "name"=>"Latte",
                    "sku"=>"latte",
                    "image_url"=>"",
                    "product_category"=>{"id"=>1, "name"=>"Coffee Drinks"},
                    "status"=>"activated",
                    "is_multi_location"=>true,
                    "location_name"=>"2 locations"},
                  {"id"=>2,
                    "name"=>"Spicy Burger",
                    "sku"=>"spicy_burger",
                    "image_url"=>"",
                    "product_category"=>{"id"=>2, "name"=>"Burgers"},
                    "status"=>"activated",
                    "is_multi_location"=>true,
                    "location_name"=>"2 locations"}],
                "paging"=>
                  {"current_page"=>1, "total_item"=>4, "next_page"=>nil, "prev_page"=>nil}}
              )
            end
          end
        end

        context 'when the product is global' do
          response(200, 'ok') do
            let(:is_select_all_location) { 'true' }
            let(:presentation) { 'all_locations' }

            before do |example|
              latte
              cheese_burger

              latte.update_columns(food_integration_usage: true)
              latte.reindex_es

              Product.search_index.refresh

              submit_request(example.metadata)
            end

            it 'returns non global products' do |example|
              response_body = JSON.parse(response.body)

              expect(response_body['products']).to eq(
                [{"id"=>cheese_burger.id,
                  "name"=>"Cheese Burger",
                  "image_url"=>"",
                  "sku"=>"cheese_burger",
                  "status"=>"activated",
                  "product_category"=>{"id"=>burgers_category.id, "name"=>"Burgers"},
                  "is_multi_location"=>false,
                  "location_name"=>"Central Kitchen Location Pasar Jeruk"}]
              )
            end
          end
        end
      end

      context 'when keyword is too long or invalid for name/code search' do
        response(400, 'bad request') do
          let(:is_select_all_location) { 'true' }
          let(:presentation) { 'all_locations' }
          let(:keyword) do
            "Classy Bag Laptop - 11 Inch\t2070220990147\t200,000.00 " * 20
          end

          before do |example|
            cheese_burger
            latte

            Product.search_index.refresh
            submit_request(example.metadata)
          end

          it 'returns 400' do |example|
            assert_response_matches_metadata(example.metadata)

            expect(response.body).to include("Keyword too long (max 100 characters)")
          end
        end
      end
    end
  end

  path '/api/products/multibrand', search: true do
    get('list products that match in multibrand') do
      tags 'Restaurant - Products'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :location_to_id, in: :query, type: :string, required: false
      parameter name: :location_to_brand_id, in: :query, type: :string, required: false
      parameter name: :location_from_brand_id, in: :query, type: :string, required: false
      parameter name: :sort_key, in: :query, type: :string, enum: ['asc'], required: false
      parameter name: :sort_order, in: :query, type: :string, enum: ['product_category_name', 'sku', 'name'], required: false
      parameter name: :status, in: :query, type: :string, required: false
      parameter name: :exclude_product_with_variances, in: :query, type: :string, required: false
      parameter name: :recipe_line_product, in: :query, type: :string, required: false
      parameter name: :keyword, in: :query, type: :string, required: false
      parameter name: :category_ids, in: :query, type: :string, required: false
      parameter name: :multibrand_category_ids, in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }
      let(:location_to_id) { brand_2_central_kitchen.id }
      let(:location_to_brand_id) { brand_2.id }
      let(:location_from_brand_id) { brand.id }

      context 'when default params' do
        response(200, 'ok') do
          before do |example|
            owned_branch_1
            latte
            brand_2_latte.update!(sell_price: 3333, allow_custom_sell_price: true)

            rice.update!(external_vendor_type: false)

            brand_2_rice.update!(sell_price: 2222, tax: brand_2_tax)

            spicy_burger

            brand_2_cheese_burger.update!(sell_price: 5555)

            coffee
            brand_2_coffee.update!(sell_price: 1212)

            Product.reindex
            submit_request(example.metadata)
          end

          it 'should return correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            products_data = response_body['products'].map do |product|
              {
                id: product['id'],
                sell_price: product['sell_price'],
                sell_tax_rate: product.dig('tax', 'rate'),
                sell_tax_setting: product['sell_tax_setting'],
                allow_custom_sell_price: product['allow_custom_sell_price']
              }
            end

            expect(products_data).to eq([
              {:id=>coffee.id,
                :sell_price=>"1212.0",
                :sell_tax_rate=>nil,
                :sell_tax_setting=>"price_exclude_tax",
                :allow_custom_sell_price=>false},
              {:id=>latte.id,
                :sell_price=>"3333.0",
                :sell_tax_rate=>nil,
                :sell_tax_setting=>"price_exclude_tax",
                :allow_custom_sell_price=>true}
            ])

            expect(response_body['sell_unit_pair']).to eq(
              {
                latte.id.to_s=>{"id" => latte.sell_unit.id, "name" => latte.sell_unit.name},
                coffee.id.to_s=>{"id" => coffee.sell_unit.id, "name" =>coffee.sell_unit.name}
              }
            )
          end
        end
      end

      context 'when has keyword params' do
        let(:keyword) { 'latte' }

        response(200, 'ok') do
          before do |example|
            owned_branch_1
            latte
            brand_2_latte.update!(sell_price: 3333)

            rice

            brand_2_rice.update!(sell_price: 2222)

            spicy_burger

            brand_2_cheese_burger.update!(sell_price: 5555)

            coffee
            brand_2_coffee.update!(sell_price: 1212)

            Product.reindex
            submit_request(example.metadata)
          end

          it 'should return correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            products_data = response_body['products'].map do |product|
              {
                id: product['id'],
                sell_price: product['sell_price']
              }
            end

            expect(products_data).to eq(
              [{:id=>latte.id, :sell_price=>"3333.0"}]
            )

            expect(response_body['sell_unit_pair']).to eq(
              {latte.id.to_s=>{"id" => cup_500_ml.id, "name" => "cup 500 ml"}}
            )
          end
        end
      end

      context 'when has product_category_id params' do
        let(:multibrand_category_ids) { brand_2_beverage_category.id.to_s }

        response(200, 'ok') do
          before do |example|
            owned_branch_1
            latte
            brand_2_latte.update!(sell_price: 3333)

            rice

            brand_2_rice.update!(sell_price: 2222, product_category_id: nil)

            spicy_burger

            brand_2_cheese_burger.update!(sell_price: 5555)

            coffee
            brand_2_coffee.update!(sell_price: 1212)

            milk
            milk_conversion_1_l
            milk_1_l_procurement_unit
            brand_2_milk.update!(product_category_id: brand_2_beverage_category.id, product_unit_id: brand_2_cup_1_l.id, back_office_unit_id: brand_2_cup_1_l.id, sell_unit_id: brand_2_cup_1_l.id, sell_price: 4300)

            Product.reindex
            submit_request(example.metadata)
          end

          it 'should return correct products' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            products_data = response_body['products'].map do |product|
              {
                id: product['id'],
                sell_price: product['sell_price']
              }
            end

            expect(products_data).to eq(
              [{:id=>milk.id, :sell_price=>"4300.0"}]
            )

            expect(response_body['sell_unit_pair']).to eq(
              {milk.id.to_s=>{"id" => cup_1_l.id, "name" => 'cup 1 l'}}
            )
          end
        end
      end
    end
  end

  path "/api/products/state_acknowledgement" do
    post "acknowledge state push notification products" do
      consumes 'application/json'
      produces 'application/json'
      security [ bearerAuth: [] ]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          id: { type: :string, required: true },
          location_id: { type: :integer, required: true },
          device_id: { type: :integer, required: true },
          unique_id: { type: :integer, required: true },
          state: { type: :string, required: true },
          available_stock_flag: {
            type: :object,
            properties: {
              pos: { type: :boolean },
              go_food: { type: :boolean },
              grab_food: { type: :boolean },
              shopee_food: { type: :boolean },
              online_ordering: { type: :boolean }
            }
          },
        }
      }

      let(:state) { Products::Constants::CHANGE_AVAILABILTY_PUSH_NOTIFICATION_STATE }
      let(:unique_id) { SecureRandom.uuid }
      let(:device) do
        create(:device, location: owned_branch_1, user: owner, brand: brand, support_acknowledgement: true)
      end
      let(:web_push_token) { create(:restaurant_web_push_token, device: device, brand: brand) }
      let(:available_stock_flag) do
        {
          pos: true,
          go_food: true,
          grab_food: true,
          shopee_food: true,
          online_ordering: true
        }
      end
      let(:params) do
        {
          id: latte.id,
          location_id: owned_branch_1.id,
          device_id: device.id,
          unique_id: unique_id,
          state: state,
          available_stock_flag: available_stock_flag
        }
      end

      context 'when valid params' do
        let!(:web_push_token) { create(:restaurant_web_push_token, device: device, brand: brand) }
        let(:locations_product) {latte.locations_products.second}

        response '201', 'successfull to create acknowledgements' do
          before do |example|
            latte
            submit_request(example.metadata)
          end

          it "returns 201 response" do |example|
            assert_response_matches_metadata(example.metadata)
            expect(locations_product.reload.push_notification_acknowledgements).to be_present
            acknowledgement = locations_product.push_notification_acknowledgements.first
            expect(acknowledgement.device_id).to eq(device.id)
            expect(acknowledgement.state).to eq(state)
            expect(acknowledgement.unique_id).to eq(unique_id)
            expect(acknowledgement.metadata['available_stock_flag']).to eq(available_stock_flag.with_indifferent_access)
          end
        end

        context 'when ack already exists' do
          let!(:acknowledgement) do
            create(
              :location_product_push_notification_acknowledgement,
              locations_product: locations_product,
              device: device,
              unique_id: unique_id,
              state: state
            )
          end
          let!(:web_push_token) { create(:restaurant_web_push_token, device: device, brand: brand) }

          response '201', 'successfull to create acknowledgements', document: false do
            before do |example|
              latte
              acknowledgement
              submit_request(example.metadata)
            end

            it "returns 201 response" do |example|
              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end

      context 'when invalid params' do
        context 'when location is invalid' do
          response '404', 'failed to create acknowledgements' do
            before do |example|
              params[:location_id] = 9999
              submit_request(example.metadata)
            end
            it "returns 404 response" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to eq(I18n.t('general.error_404'))
            end
          end
        end

        context 'when products is invalid' do
          response '404', 'failed to create acknowledgements', document: false do
            before do |example|
              params[:id] = 999
              submit_request(example.metadata)
            end

            it "returns 400 response" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to eq(I18n.t('general.error_404'))
            end
          end
        end

        context 'when location products not found' do
          response '400', 'failed to create acknowledgements', document: false do
            before do |example|
              latte
              owned_branch_1
              LocationsProduct.destroy_all
              submit_request(example.metadata)
            end

            it "returns 400 response" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to eq(I18n.t('general.error_404'))
            end
          end
        end

        context 'device is not valid' do
          response '400', 'failed to create acknowledgements', document: false do
            before do |example|
              params[:device_id] = 9999
              submit_request(example.metadata)
            end

            it "returns 400 response" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to eq(I18n.t('products.errors.device_not_found'))
            end
          end
        end

        context 'when state is not valid' do
          response '400', 'failed to create acknowledgements', document: false do
            before do |example|
              params[:state] = 'ABC'
              submit_request(example.metadata)
            end
            it "returns 400 response" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['message']).to eq(I18n.t('products.errors.invalid_push_notif_state'))
            end
          end
        end
      end
    end
  end
end
