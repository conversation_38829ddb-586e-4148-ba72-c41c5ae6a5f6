require './spec/shared/locations'
require './spec/shared/product_option_sets'
require './spec/shared/swagger'
require './spec/shared/audits'
require './spec/shared/bulk_sale_transactions'
require './spec/shared/costings'
require './spec/shared/order_types'

RSpec.describe 'api/option_sets', type: :request, clickhouse: true do
  include_context 'locations creations'
  include_context 'product option set creations'
  include_context 'swagger after response'
  include_context 'bulk sale transactions creations'
  include_context "costings creations"
  include_context "order_types creations"

  let(:grab_order_type) { create(:grab_food_order_type) }

  before(:each) do
    travel_to Time.utc(2024, 8, 27, 7, 56)
    @header = authentication_header(owner)

    grab_order_type
    online_ordering_order_type_without_fee
    brand_dine_in_order_type
    order_type
  end

  after do
    travel_back
  end

  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  let(:Authorization) { @header['Authorization'] }

  let(:user_permission) do
    location_user = LocationsUser.find_by(user: owner, location: owned_branch_1)
    location_user.access_list
  end

  path '/api/option_sets/{id}/history' do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('history option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_history_audit'

        context 'existing option set' do
          let(:id) { option_set.id }

          before do |example|
            owned_branch_1
            ::Audited.store[:current_request_uuid] = SecureRandom.uuid
            submit_request(example.metadata)
          end

          it_should_behave_like 'audit history response'

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(option_set.reload.audits.first.brand_id).to eq option_set.brand_id
          end
        end

        context 'after update option set', bullet: :skip do
          before do |example|
            owned_branch_1
            option_set

            patch "/api/option_sets/#{option_set.id}", params: { format: 'json', option_set: update_option_set_param }, headers: @header

            ::Audited.store[:current_request_uuid] = SecureRandom.uuid
            submit_request(example.metadata)
          end

          let(:id) { option_set.id }
          let(:custom_price) {create(:option_set_custom_price_location, order_type_id: order_type.id, option_set_option_id: option_set.option_set_options.first.id)}
          let(:update_option_set_param) do
            build(:option_set_params, name: 'updated_name',
                                      option_set_options_attributes: [{ id: option_set.option_set_options.first.id,
                                                                        product_id: option_set.option_set_options.first.product_id,
                                                                        product_unit_id: option_set.option_set_options.first.product_unit_id,
                                                                        price: option_set.option_set_options.first.price,
                                                                        quantity: option_set.option_set_options.first.quantity,
                                                                        option_set_custom_price_locations_attributes: [{id: custom_price.id, _destroy: true}] },
                                                                      { id: nil,
                                                                        product_id: second_product.id,
                                                                        product_unit_id: product_unit.id,
                                                                        price: option_set.option_set_options.first.price,
                                                                        quantity: option_set.option_set_options.first.quantity }])
          end

          it 'should have audits for new option set options' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['audits'].length).to eq 6
            expect(response_body['audits'].first['action_label'].include?("Add product #{second_product.name} to Option Set By")).to eq true
          end
        end
      end
    end
  end

  path '/api/option_sets' do
    get('list option_sets') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, type: :string, required: false
      parameter name: :ids, in: :query, type: :string, required: false
      parameter name: :exclude_ids, in: :query, type: :string, required: false
      context 'when filter by keywords' do
        let(:keyword) { option_set.name.upcase[0..3] }
        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_list_option_sets'

          before do
            owned_branch_1
            option_set
          end

          it 'should be able to filter by keyword case insensitive' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['option_sets']
            expect(response_body['option_sets'].size).to eq(1)
            expect(response_body['option_sets'].first.keys).to match_array([
              "id", "name", "brand_id", "deleted", "rule_minimum", "rule_maximum", "has_option_set_as_option",
              "rule_show_option_prefix", "created_at", "updated_at", "created_by_id",
              "last_updated_by_id", "rule_cost_included_in_parent", "parent_rule_update_locked",
              "is_select_all_order_type", "order_type_ids", "exclude_order_type_ids",
              "docket_printing_option", "show_item", "docket_printing_sticker_option", "course_display_menu"
            ])
            expect(response_body['option_sets'].first['name']).to eq option_set.name
          end
        end
      end
      context 'when filter by ids' do
        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_list_option_sets'

          before do |example|
            owned_branch_1
            option_set
            submit_request(example.metadata)
          end

          let(:ids) { option_set.id }

          it 'should be able to show included ids' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['option_sets']
            expect(response_body['option_sets'].first['name']).to eq option_set.name

            expect(response_body['option_sets']).to eq([
              {
                "id"=>option_set.id,
                "name"=>option_set.name,
                "brand_id"=>brand.id,
                "deleted"=>false,
                "rule_minimum"=>1,
                "rule_maximum"=>2,
                "rule_show_option_prefix"=>false,
                "has_option_set_as_option"=>false,
                "created_at"=>"2024-08-27T07:56:00.000Z",
                "updated_at"=>"2024-08-27T07:56:00.000Z",
                "created_by_id"=>nil,
                "last_updated_by_id"=>nil,
                "rule_cost_included_in_parent"=>false,
                "parent_rule_update_locked"=>false,
                "is_select_all_order_type" => true,
                "order_type_ids" => [
                  grab_order_type.id,
                  online_ordering_order_type_without_fee.id,
                  brand_dine_in_order_type.id,
                  order_type.id
                ],
                "exclude_order_type_ids" => [],
                "course_display_menu"=>"group_same_menu",
                "docket_printing_sticker_option" => "same_with_main_menu",
                "docket_printing_option"=>"same_as_main_menu", "show_item"=>true
              }
            ])
          end
        end
      end

      context 'when exclude ids' do
        let(:exclude_ids) { option_set.id }
        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_list_option_sets'

          before do |example|
            owned_branch_1
            option_set
            submit_request(example.metadata)
          end

          it 'should be able to filter and exclude ids' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['option_sets']
            expect(response_body['option_sets'].first).to be_nil
          end
        end
      end

      response 403, 'forbidden' do
        before do |example|
          user_permission.location_permission['product']['index'] = false
          user_permission.save!
          submit_request(example.metadata)
        end

        it 'should not be able to get list of option set' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq(I18n.t('general.error_401'))
        end
      end
    end

    post('create option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref': '#/components/parameters/parameter_create_option_set'
      }

      response 201, 'successful' do
        schema '$ref' => '#/components/responses/response_create_option_sets'
        let(:param) { { option_set: option_set_param } }

        before do |example|
          owned_branch_1
          submit_request(example.metadata)
        end

        it 'returns a valid 201 response' do |example|
          assert_response_matches_metadata(example.metadata)
          expect(OptionSet.count).to eq(1)
          expect(OptionSetCustomPriceLocation.count).to eq(1)

          option_set = OptionSet.take
          expect(option_set.rule_cost_included_in_parent).to eq(true)
          expect(option_set.docket_printing_option).to eq('same_as_main_menu')
          expect(option_set.docket_printing_sticker_option).to eq('same_with_main_menu')
          expect(option_set.course_display_menu).to eq('group_same_menu')
          expect(option_set.is_select_all_order_type).to eq(true)
          expect(option_set.order_type_ids).to eq([
            grab_order_type.id,
            online_ordering_order_type_without_fee.id,
            brand_dine_in_order_type.id,
            order_type.id
          ])
          expect(option_set.exclude_order_type_ids).to eq([])

          response_body = JSON.parse(response.body)
          expect(response_body.keys).to eq(["option_set"])

          option_set = response_body['option_set']
          expect(option_set.keys).to match_array([
            "id", "name", "brand_id", "deleted", "rule_minimum", "rule_maximum", "rule_show_option_prefix",
            "created_at", "updated_at", "created_by_id", "last_updated_by_id", "rule_cost_included_in_parent",
            "parent_rule_update_locked", "docket_printing_option", "show_item", "has_option_set_as_option",
            "docket_printing_sticker_option", "course_display_menu", "is_select_all_order_type",
            "order_type_ids", "exclude_order_type_ids", "option_set_options", "order_types", "exclude_order_types"
          ])

          expect(option_set['order_types']).to eq([
            {
              "id" => grab_order_type.id,
              "name" => grab_order_type.name,
            },
            {
              "id" => online_ordering_order_type_without_fee.id,
              "name" => online_ordering_order_type_without_fee.name,
            },
            {
              "id" => brand_dine_in_order_type.id,
              "name" => brand_dine_in_order_type.name,
            },
            {
              "id" => order_type.id,
              "name" => order_type.name,
            },
          ])
          expect(option_set['exclude_order_types']).to eq([])
        end

        it_should_behave_like 'product option set response with detail'
      end

      response 201, 'successful', document: false do
        context 'when set pre-selected' do
          let(:param) do
            option_set_options = build(
              :option_set_option_params,
              product_id: product.id,
              product_unit_id: product_unit.id,
              pre_selected: true,
              pre_selected_quantity: 1,
              option_set_custom_price_locations_attributes: [
                build(:option_set_custom_price_location_params, order_type_id: order_type.id)
              ]
            )

            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: false,
              order_type_ids: [
                online_ordering_order_type_without_fee.id
              ],
              docket_printing_option: 'split_item_by_category',
              docket_printing_sticker_option: 'per_option_set_menu',
              course_display_menu: 'default_per_options',
              option_set_options_attributes: option_set_options
            )
          end

          before do
            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { OptionSet.count }.from(0).to(1)
              .and change { OptionSetCustomPriceLocation.count }.from(0).to(1)
              .and change { OptionSetOption.count }.from(0).to(1)

            option_set = OptionSet.last
            expect(option_set.docket_printing_option).to eq('split_item_by_category')
            expect(option_set.docket_printing_sticker_option).to eq('per_option_set_menu')
            expect(option_set.course_display_menu).to eq('default_per_options')
            expect(option_set.is_select_all_order_type).to eq(false)
            expect(option_set.order_type_ids).to eq([
              online_ordering_order_type_without_fee.id,
            ])
            expect(option_set.exclude_order_type_ids).to eq([])

            option_set_option = option_set.option_set_options.first
            expect(option_set_option.product_id).to eq(product.id)
            expect(option_set_option.product_unit_id).to eq(product_unit.id)
            expect(option_set_option.option_set_as_option_id).to eq(nil)
            expect(option_set_option.pre_selected).to eq(true)
            expect(option_set_option.pre_selected_quantity).to eq(1)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to eq(["option_set"])

            option_set = response_body['option_set']
            expect(option_set['order_types']).to eq([
              {
                "id" => online_ordering_order_type_without_fee.id,
                "name" => online_ordering_order_type_without_fee.name,
              },
            ])
            expect(option_set['exclude_order_types']).to eq([])
          end
        end

        context 'when set option set as option' do
          let(:sugar_level) do
            create(
              :option_set,
              product: small,
              brand: brand,
              name: "Sugar Level"
            )
          end

          let(:param) do
            option_set_options_attributes = [
              build(
                :option_set_option_params,
                product_id: product.id,
                product_unit_id: product_unit.id,
                pre_selected: true,
                pre_selected_quantity: 1,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              ),
              build(
                :option_set_option_params,
                option_set_as_option_id: sugar_level.id,
                pre_selected: false,
                pre_selected_quantity: 0,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              )
            ]

            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: true,
              exclude_order_type_ids: [
                online_ordering_order_type_without_fee.id
              ],
              option_set_options_attributes: option_set_options_attributes
            )
          end

          before do
            small
            sugar_level

            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { OptionSet.count }.from(1).to(2)
              .and change { OptionSetCustomPriceLocation.count }.from(0).to(2)
              .and change { OptionSetOption.count }.from(1).to(3)

            option_set = OptionSet.last
            expect(option_set.has_option_set_as_option).to eq(true)
            expect(option_set.is_select_all_order_type).to eq(true)
            expect(option_set.order_type_ids).to eq([
              grab_order_type.id,
              brand_dine_in_order_type.id,
              order_type.id
            ])
            expect(option_set.exclude_order_type_ids).to eq([
              online_ordering_order_type_without_fee.id,
            ])

            option_set_option = option_set.option_set_options.first
            expect(option_set_option.product_id).to eq(product.id)
            expect(option_set_option.product_unit_id).to eq(product_unit.id)
            expect(option_set_option.option_set_as_option_id).to eq(nil)
            expect(option_set_option.pre_selected).to eq(true)
            expect(option_set_option.pre_selected_quantity).to eq(1)

            option_set_option = option_set.option_set_options.second
            expect(option_set_option.product_id).to eq(nil)
            expect(option_set_option.product_unit_id).to eq(nil)
            expect(option_set_option.option_set_as_option_id).to eq(sugar_level.id)
            expect(option_set_option.pre_selected).to eq(false)
            expect(option_set_option.pre_selected_quantity).to eq(0)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to eq(["option_set"])

            option_set = response_body['option_set']
            expect(option_set['order_types']).to eq([
              {
                "id" => grab_order_type.id,
                "name" => grab_order_type.name,
              },
              {
                "id" => brand_dine_in_order_type.id,
                "name" => brand_dine_in_order_type.name,
              },
              {
                "id" => order_type.id,
                "name" => order_type.name,
              },
            ])
            expect(option_set['exclude_order_types']).to eq([
              {
                "id" => online_ordering_order_type_without_fee.id,
                "name" => online_ordering_order_type_without_fee.name,
              },
            ])
          end
        end

        context 'when select global order type and brand order type' do
          let(:sugar_level) do
            create(
              :option_set,
              product: small,
              brand: brand,
              name: "Sugar Level"
            )
          end

          let(:param) do
            option_set_options_attributes = [
              build(
                :option_set_option_params,
                product_id: product.id,
                product_unit_id: product_unit.id,
                pre_selected: true,
                pre_selected_quantity: 1,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              ),
              build(
                :option_set_option_params,
                option_set_as_option_id: sugar_level.id,
                pre_selected: false,
                pre_selected_quantity: 0,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              )
            ]

            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: false,
              order_type_ids: [
                grab_order_type.id,
                online_ordering_order_type_without_fee.id
              ],
              option_set_options_attributes: option_set_options_attributes
            )
          end

          before do
            small
            sugar_level

            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { OptionSet.count }.from(1).to(2)
              .and change { OptionSetCustomPriceLocation.count }.from(0).to(2)
              .and change { OptionSetOption.count }.from(1).to(3)

            option_set = OptionSet.last
            expect(option_set.has_option_set_as_option).to eq(true)
            expect(option_set.is_select_all_order_type).to eq(false)
            expect(option_set.order_type_ids).to eq([
              grab_order_type.id,
              online_ordering_order_type_without_fee.id
            ])
            expect(option_set.exclude_order_type_ids).to eq([])

            option_set_option = option_set.option_set_options.first
            expect(option_set_option.product_id).to eq(product.id)
            expect(option_set_option.product_unit_id).to eq(product_unit.id)
            expect(option_set_option.option_set_as_option_id).to eq(nil)
            expect(option_set_option.pre_selected).to eq(true)
            expect(option_set_option.pre_selected_quantity).to eq(1)

            option_set_option = option_set.option_set_options.second
            expect(option_set_option.product_id).to eq(nil)
            expect(option_set_option.product_unit_id).to eq(nil)
            expect(option_set_option.option_set_as_option_id).to eq(sugar_level.id)
            expect(option_set_option.pre_selected).to eq(false)
            expect(option_set_option.pre_selected_quantity).to eq(0)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to eq(["option_set"])

            option_set = response_body['option_set']
            expect(option_set['order_types']).to eq([
              {
                "id" => grab_order_type.id,
                "name" => grab_order_type.name,
              },
              {
                "id" => online_ordering_order_type_without_fee.id,
                "name" => online_ordering_order_type_without_fee.name,
              },
            ])
            expect(option_set['exclude_order_types']).to eq([])
          end
        end

        context 'when select global order type' do
          let(:sugar_level) do
            create(
              :option_set,
              product: small,
              brand: brand,
              name: "Sugar Level"
            )
          end

          let(:param) do
            option_set_options_attributes = [
              build(
                :option_set_option_params,
                product_id: product.id,
                product_unit_id: product_unit.id,
                pre_selected: true,
                pre_selected_quantity: 1,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              ),
              build(
                :option_set_option_params,
                option_set_as_option_id: sugar_level.id,
                pre_selected: false,
                pre_selected_quantity: 0,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              )
            ]

            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: false,
              order_type_ids: [
                grab_order_type.id,
              ],
              option_set_options_attributes: option_set_options_attributes
            )
          end

          before do
            small
            sugar_level

            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { OptionSet.count }.from(1).to(2)
              .and change { OptionSetCustomPriceLocation.count }.from(0).to(2)
              .and change { OptionSetOption.count }.from(1).to(3)

            option_set = OptionSet.last
            expect(option_set.has_option_set_as_option).to eq(true)
            expect(option_set.is_select_all_order_type).to eq(false)
            expect(option_set.order_type_ids).to eq([
              grab_order_type.id,
            ])
            expect(option_set.exclude_order_type_ids).to eq([])

            option_set_option = option_set.option_set_options.first
            expect(option_set_option.product_id).to eq(product.id)
            expect(option_set_option.product_unit_id).to eq(product_unit.id)
            expect(option_set_option.option_set_as_option_id).to eq(nil)
            expect(option_set_option.pre_selected).to eq(true)
            expect(option_set_option.pre_selected_quantity).to eq(1)

            option_set_option = option_set.option_set_options.second
            expect(option_set_option.product_id).to eq(nil)
            expect(option_set_option.product_unit_id).to eq(nil)
            expect(option_set_option.option_set_as_option_id).to eq(sugar_level.id)
            expect(option_set_option.pre_selected).to eq(false)
            expect(option_set_option.pre_selected_quantity).to eq(0)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to eq(["option_set"])

            option_set = response_body['option_set']
            expect(option_set['order_types']).to eq([
              {
                "id" => grab_order_type.id,
                "name" => grab_order_type.name,
              },
            ])
            expect(option_set['exclude_order_types']).to eq([])
          end
        end
      end

      context 'when duplicate products' do
        response(422, 'unprocessable entity') do
          schema '$ref' => '#/components/responses/response_create_option_sets'
          let(:param) { { option_set: option_set_param_duplicate_products } }

          before do
            owned_branch_1
          end

          it 'should not create option set' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { OptionSet.count }
            .and not_change { OptionSetCustomPriceLocation.count }

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"message"=>"These products are duplicated: Latte"}
            )
          end
        end
      end

      context 'when duplicate products but one of them is about to be destroyed' do
        response(201, 'Created') do
          schema '$ref' => '#/components/responses/response_create_option_sets'
          let(:param) { { option_set: option_set_param_duplicate_products } }

          before do
            owned_branch_1
            param[:option_set][:option_set_options_attributes].first['_destroy'] = true
          end

          it 'should create option set' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { OptionSet.count }.from(0).to(1)
            .and change { OptionSetCustomPriceLocation.count }.from(0).to(1)

            response_body = JSON.parse(response.body)
            expect(response_body['option_set']['name']).to eq('test')
          end
        end
      end

      response 403, 'forbidden' do
        let(:param) { { option_set: option_set_param } }

        before do |example|
          user_permission.location_permission['product']['create'] = false
          user_permission.save!
          submit_request(example.metadata)
        end

        it 'should not be able to create option set' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq(I18n.t('general.error_401'))
        end
      end

      response(422, 'unprocessable entity', document: false) do
        context 'when use other brand order type and global order type' do
          let(:param) do
            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: false,
              order_type_ids: [
                grab_order_type.id,
                brand_3_dine_in_order_type.id
              ]
            )
          end

          let(:brand_3_dine_in_order_type) do
            create(
              :dine_in_order_type,
              name: 'Dine in order type with default fee for brand 3',
              is_select_all_location: true,
              brand: brand_3
            )
          end

          before do
            brand_3

            owned_branch_1

            brand_3_dine_in_order_type
          end

          it 'should not create order type' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors"=>{
                "option_set_options"=>[
                  "Option set options can't be blank"
                ],
                "order_type_ids" => [
                  "Order type ids not from the same resource"
                ]
              }
            })
          end
        end

        context 'when use other brand order type' do
          let(:param) do
            build(
              :option_set_params,
              brand_id: brand.id,
              is_select_all_order_type: false,
              order_type_ids: [
                brand_3_dine_in_order_type.id
              ]
            )
          end

          let(:brand_3_dine_in_order_type) do
            create(
              :dine_in_order_type,
              name: 'Dine in order type with default fee for brand 3',
              is_select_all_location: true,
              brand: brand_3
            )
          end

          before do
            brand_3

            owned_branch_1

            brand_3_dine_in_order_type
          end

          it 'should not create order type' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors"=>{
                "option_set_options"=>[
                  "Option set options can't be blank"
                ],
                "order_type_ids" => [
                  "Order type ids not from the same resource"
                ]
              }
            })
          end
        end

        context 'when option_set_option has option_set_as_option is duplicate' do
          let(:sugar_level) do
            create(
              :option_set,
              product: small,
              brand: brand,
              name: "Sugar Level"
            )
          end

          let(:param) do
            option_set_options_attributes = [
              build(
                :option_set_option_params,
                option_set_as_option_id: sugar_level.id,
                pre_selected: false,
                pre_selected_quantity: 0,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              ),
              build(
                :option_set_option_params,
                option_set_as_option_id: sugar_level.id,
                pre_selected: false,
                pre_selected_quantity: 0,
                option_set_custom_price_locations_attributes: [
                  build(:option_set_custom_price_location_params, order_type_id: order_type.id)
                ]
              )
            ]

            build(:option_set_params, brand_id: brand.id, option_set_options_attributes: option_set_options_attributes)
          end

          before do
            small
            sugar_level

            owned_branch_1
          end

          it 'should not create option set' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { OptionSet.count }
            .and not_change { OptionSetCustomPriceLocation.count }

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"message"=>"These option sets are duplicated: #{sugar_level.name}"}
            )
          end
        end

        context 'when set pre-selected-quantity nil' do
          let(:param) do
            option_set_options = build(
              :option_set_option_params,
              product_id: product.id,
              product_unit_id: product_unit.id,
              pre_selected: true,
              pre_selected_quantity: nil,
              option_set_custom_price_locations_attributes: [
                build(:option_set_custom_price_location_params, order_type_id: order_type.id)
              ]
            )

            build(:option_set_params, brand_id: brand.id, option_set_options_attributes: option_set_options)
          end

          before do
            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)
              .and not_change { OptionSetOption.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors"=>{
                "option_set_options[0].pre_selected_quantity"=>[
                  "Option set options[0] pre selected quantity is not a number"
                ]
              }
            })
          end
        end

        context 'when set pre-selected-quantity negative' do
          let(:param) do
            option_set_options = build(
              :option_set_option_params,
              product_id: product.id,
              product_unit_id: product_unit.id,
              pre_selected: true,
              pre_selected_quantity: -10,
              option_set_custom_price_locations_attributes: [
                build(:option_set_custom_price_location_params, order_type_id: order_type.id)
              ]
            )

            build(:option_set_params, brand_id: brand.id, option_set_options_attributes: option_set_options)
          end

          before do
            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)
              .and not_change { OptionSetOption.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors"=>{
                "option_set_options[0].pre_selected_quantity"=>[
                  "Option set options[0] pre selected quantity must be greater than 0"
                ]
              }
            })
          end
        end

        context 'when set pre-selected-quantity zero' do
          let(:param) do
            option_set_options = build(
              :option_set_option_params,
              product_id: product.id,
              product_unit_id: product_unit.id,
              pre_selected: true,
              pre_selected_quantity: 0,
              option_set_custom_price_locations_attributes: [
                build(:option_set_custom_price_location_params, order_type_id: order_type.id)
              ]
            )

            build(:option_set_params, brand_id: brand.id, option_set_options_attributes: option_set_options)
          end

          before do
            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)
              .and not_change { OptionSetOption.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors"=>{
                "option_set_options[0].pre_selected_quantity"=>[
                  "Option set options[0] pre selected quantity must be greater than 0"
                ]
              }
            })
          end
        end

        context 'when set product is not sell to customer' do
          let(:param) do
            option_set_options = build(
              :option_set_option_params,
              product_id: small.id,
              product_unit_id: small.product_unit.id,
              pre_selected: true,
              pre_selected_quantity: 0,
              option_set_custom_price_locations_attributes: [
                build(:option_set_custom_price_location_params, order_type_id: order_type.id)
              ]
            )

            build(:option_set_params, brand_id: brand.id, option_set_options_attributes: option_set_options)
          end

          before do
            small.sell_to_customer_type = false
            small.sell_to_dine_in = false
            small.sell_to_grab_food = false
            small.sell_to_go_food = false
            small.sell_to_online_ordering = false
            small.sell_to_pos = false
            small.sell_to_kiosk = false
            small.save!

            owned_branch_1
          end

          it 'returns a valid 201 response' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to not_change { OptionSet.count }.from(0)
              .and not_change { OptionSetCustomPriceLocation.count }.from(0)
              .and not_change { OptionSetOption.count }.from(0)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "message" => "Product should sell to customer"
            })
          end
        end
      end
    end
  end

  path '/api/option_sets/{id}' do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_show_option_sets'
        let(:id) { option_set.id }

        before do |example|
          owned_branch_1
          submit_request(example.metadata)
        end

        it 'returns a valid 200 response' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)

          option_set_response_body = response_body['option_set']
          expect(option_set_response_body.keys).to match_array([
            "id", "name", "brand_id", "deleted", "rule_minimum", "rule_maximum", "has_option_set_as_option",
            "rule_show_option_prefix", "created_at", "updated_at", "created_by_id",
            "last_updated_by_id", "rule_cost_included_in_parent", "parent_rule_update_locked",
            'is_select_all_order_type', 'order_type_ids', 'exclude_order_type_ids', 'order_types', 'exclude_order_types',
            "docket_printing_option", "show_item", "docket_printing_sticker_option", "option_set_options", "course_display_menu"
          ])
          expect(option_set_response_body['id']).to eq option_set.id
          expect(option_set_response_body['option_set_options']).not_to be_empty
          expect(option_set_response_body['rule_cost_included_in_parent']).to eq(false)
          expect(option_set_response_body['docket_printing_option']).to eq('same_as_main_menu')
          expect(option_set_response_body['docket_printing_sticker_option']).to eq('same_with_main_menu')
          expect(option_set_response_body['course_display_menu']).to eq('group_same_menu')
          expect(option_set_response_body['is_select_all_order_type']).to eq(true)
          expect(option_set_response_body['order_types']).to eq([
            {
              "id" => grab_order_type.id,
              "name" => grab_order_type.name,
            },
            {
              "id" => online_ordering_order_type_without_fee.id,
              "name" => online_ordering_order_type_without_fee.name,
            },
            {
              "id" => brand_dine_in_order_type.id,
              "name" => brand_dine_in_order_type.name,
            },
            {
              "id" => order_type.id,
              "name" => order_type.name,
            },
          ])
          expect(option_set_response_body['exclude_order_types']).to eq([])

          option_set_option = option_set.option_set_options.first
          expect(option_set_response_body['option_set_options']).to eq([
            {
              "id"=>option_set_option.id,
              "price"=>"10.0",
              "quantity"=>"10.0",
              "sequence"=>0,
              "pre_selected"=>false,
              "pre_selected_quantity"=>'0.0',
              "enable_max_chosen"=>false,
              "max_chosen_quantity"=>"0.0",
              "deleted"=>false,
              "product"=>{
                "id"=>option_set_option.product_id,
                "name"=>option_set_option.product.name
              },
              "product_unit"=>{
                "id"=>option_set_option.product.product_unit_id,
                "name"=>option_set_option.product.product_unit.name
              },
              "custom_prices"=>[]
            }
          ])
        end

        it_should_behave_like 'product option set response with detail'
      end

      response 403, 'forbidden' do
        let(:id) { option_set.id }

        before do |example|
          user_permission.location_permission['product']['show'] = false
          user_permission.save!
          submit_request(example.metadata)
        end

        it 'should not be able to see detail of option set' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq(I18n.t('general.error_401'))
        end
      end
    end

    put('update option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref': '#/components/parameters/parameter_update_option_set'
      }

      context 'when valid params' do
        context 'when no sale detail modifier' do
          response 200, 'successful' do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { option_set.id }
            let(:param) do
              {
                option_set: {
                  name: 'new_name',
                  is_select_all_order_type: false,
                  order_type_ids: [
                    online_ordering_order_type_without_fee.id
                  ],
                  rule_cost_included_in_parent: true,
                  show_item: false,
                  docket_printing_option: "split_item_by_category"
                }
              }
            end

            before do |example|
              owned_branch_1
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['name']).to eq 'new_name'
              expect(option_set_response_body['rule_cost_included_in_parent']).to eq(true)
              expect(option_set_response_body['docket_printing_option']).to eq("split_item_by_category")
              expect(option_set_response_body['show_item']).to eq(false)
              expect(option_set_response_body['is_select_all_order_type']).to eq(false)
              expect(option_set_response_body['order_type_ids']).to eq([
                online_ordering_order_type_without_fee.id
              ])
              expect(option_set_response_body['order_types']).to eq([
                {
                  "id" => online_ordering_order_type_without_fee.id,
                  "name" => online_ordering_order_type_without_fee.name,
                }
              ])
              expect(option_set_response_body['exclude_order_type_ids']).to eq([])
              expect(option_set_response_body['exclude_order_types']).to eq([])

              option_set_option = option_set.option_set_options.first
              expect(option_set_response_body['option_set_options']).to eq([
                {
                  "id"=>option_set_option.id,
                  "price"=>"10.0",
                  "quantity"=>"10.0",
                  "sequence"=>0,
                  "pre_selected"=>false,
                  "pre_selected_quantity"=>'0.0',
                  "enable_max_chosen"=>false,
                  "max_chosen_quantity"=>"0.0",
                  "deleted"=>false,
                  "product"=>{
                    "id"=>option_set_option.product_id,
                    "name"=>option_set_option.product.name
                  },
                  "product_unit"=>{
                    "id"=>option_set_option.product.product_unit_id,
                    "name"=>option_set_option.product.product_unit.name
                  },
                  "custom_prices"=>[]
                }
              ])
            end

            it_should_behave_like 'product option set response with detail'
          end
        end

        context 'when no sale detail modifier and opdate option_set_option' do
          let(:option_set_option) { option_set.option_set_options.first }

          response 200, 'successful', document: false do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { option_set.id }
            let(:param) do
              {
                option_set: {
                  name: 'new_name',
                  rule_cost_included_in_parent: true,
                  show_item: false,
                  is_select_all_order_type: true,
                  exclude_order_type_ids: [
                    online_ordering_order_type_without_fee.id
                  ],
                  option_set_options_attributes: [
                    {
                      id: option_set_option.id,
                      pre_selected: true,
                      pre_selected_quantity: 10
                    }
                  ],
                  docket_printing_sticker_option: "per_option_set_menu",
                  course_display_menu: "default_per_options",
                  docket_printing_option: "split_item_by_category"
                }
              }
            end

            before do |example|
              owned_branch_1
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['name']).to eq 'new_name'
              expect(option_set_response_body['rule_cost_included_in_parent']).to eq(true)
              expect(option_set_response_body['docket_printing_option']).to eq("split_item_by_category")
              expect(option_set_response_body['docket_printing_sticker_option']).to eq("per_option_set_menu")
              expect(option_set_response_body['course_display_menu']).to eq("default_per_options")
              expect(option_set_response_body['show_item']).to eq(false)
              expect(option_set_response_body['is_select_all_order_type']).to eq(true)
              expect(option_set_response_body['order_type_ids']).to eq([
                grab_order_type.id,
                brand_dine_in_order_type.id,
                order_type.id
              ])
              expect(option_set_response_body['order_types']).to eq([
                {
                  "id" => grab_order_type.id,
                  "name" => grab_order_type.name,
                },
                {
                  "id" => brand_dine_in_order_type.id,
                  "name" => brand_dine_in_order_type.name,
                },
                {
                  "id" => order_type.id,
                  "name" => order_type.name,
                },
              ])
              expect(option_set_response_body['exclude_order_type_ids']).to eq([
                online_ordering_order_type_without_fee.id
              ])
              expect(option_set_response_body['exclude_order_types']).to eq([
                {
                  "id" => online_ordering_order_type_without_fee.id,
                  "name" => online_ordering_order_type_without_fee.name,
                }
              ])

              expect(option_set_response_body['option_set_options']).to eq([
                {
                  "id"=>option_set_option.id,
                  "price"=>"10.0",
                  "quantity"=>"10.0",
                  "sequence"=>0,
                  "pre_selected"=>true,
                  "pre_selected_quantity"=>'10.0',
                  "enable_max_chosen"=>false,
                  "max_chosen_quantity"=>"0.0",
                  "deleted"=>false,
                  "product"=>{
                    "id"=>option_set_option.product_id,
                    "name"=>option_set_option.product.name
                  },
                  "product_unit"=>{
                    "id"=>option_set_option.product.product_unit_id,
                    "name"=>option_set_option.product.product_unit.name
                  },
                  "custom_prices"=>[]
                }
              ])
            end
          end
        end

        context 'when has sale transaction modifier and costing change rule_cost_included_in_parent to true', bullet: :skip do
          response 200, 'successful' do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { sugar_level.id }
            let(:param) { { option_set: { name: 'new_name', rule_cost_included_in_parent: true } } }

            before do |example|
              latte
              few_sugar_level
              regular_sugar_level
              sugar_level.update!(rule_cost_included_in_parent: false)
              bulk_past_transactions_with_modifier_owned_online_branch_1
              SaleTransaction.all.each do |sale|
                sale.sale_detail_transactions.each do |sale_detail|
                  create(
                      :inventory,
                      product: sale_detail.product,
                      location: sale.location,
                      in_stock: sale_detail.quantity,
                      stock_date: sale.local_sales_time,
                      resource: sale,
                      resource_line: sale_detail
                    )
                end
              end
              costing = Costing.create!(brand_id: brand.id, location_id: nil, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              costing_consumers_all_locations(costing)
              costing.cost_per_products.where(product_id: latte.id).update_all(price_unit: 1200)
              costing.cost_per_products.where(product_id: spicy_burger.id).update_all(price_unit: 2000)
              costing.cost_per_products.where(product_id: few_sugar.id).update_all(price_unit: 300)
              SaleDetailModifier.all.each do |sale_detail_modifier|
                sale_detail_modifier.meta = sale_detail_modifier.meta.merge(
                  option_set_option_id: few_sugar_level.id,
                  option_set_id: sugar_level.id
                )
                sale_detail_modifier.option_set_id = sugar_level.id
                sale_detail_modifier.option_set_option_id = few_sugar_level.id
                sale_detail_modifier.save!
              end

              costing_calculate_cost_of_products_all_locations(costing)
              replicate_data_to_clickhouse!
            end

            it 'should move the cost to the parent' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { SaleDetailModifier.order(:id).all.map { |modifier| modifier.meta['cost'] } }.from(["300.0", "300.0", "300.0"]).to(["0.0", "0.0", "0.0"])
              .and not_change { SaleDetailModifier.order(:id).all.map { |modifier| modifier.meta['self_cost'] } }
              .and change { SaleDetailTransaction.order(:id).all.map { |modifier| modifier.meta['cost'] } }.from(["1200.0", "6000.0", "1200.0", "6000.0", "1200.0", "6000.0"]).to(["1500.0", "6000.0", "1500.0", "6000.0", "1500.0", "6000.0"])
              .and not_change { SaleDetailTransaction.order(:id).all.map { |modifier| modifier.meta['self_cost'] } }

              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['rule_cost_included_in_parent']).to eq(true)
              expect(SaleDetailModifier.all.pluck(:meta)).to match_array(
                [{"cost"=>"0.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>'0.0',
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>'0.0',
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>'0.0'},
               {"cost"=>"0.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>'0.0',
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>'0.0',
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>'0.0'},
               {"cost"=>"0.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>'0.0',
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>'0.0',
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>'0.0'}]
              )

              expect(SaleDetailTransaction.all.pluck(:meta)).to match_array(
                [{"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"1500.0",
                "self_cost"=>"1200.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"15000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"15000.0"},
               {"cost"=>"1500.0",
                "self_cost"=>"1200.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"15000.0",
                "sales_by_free_of_charge"=>"0.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_total_line_discount_prorate"=>"15000.0"},
               {"cost"=>"1500.0",
                "self_cost"=>"1200.0",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_total_line_amount"=>"15000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"15000.0"}]
              )
            end
          end
        end

        context 'when has sale transaction modifier and costing change rule_cost_included_in_parent to false', bullet: :skip, document: false do
          response 200, 'successful' do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { sugar_level.id }
            let(:param) { { option_set: { name: 'new_name_2', rule_cost_included_in_parent: false } } }

            before do |example|
              latte
              few_sugar_level
              regular_sugar_level
              sugar_level.update!(rule_cost_included_in_parent: true)
              bulk_past_transactions_with_modifier_owned_online_branch_1
              SaleTransaction.all.each do |sale|
                sale.sale_detail_transactions.each do |sale_detail|
                  create(
                      :inventory,
                      product: sale_detail.product,
                      location: sale.location,
                      in_stock: sale_detail.quantity,
                      stock_date: sale.local_sales_time,
                      resource: sale,
                      resource_line: sale_detail
                    )
                end
              end
              costing = Costing.create!(brand_id: brand.id, location_id: nil, start_period: Time.zone.today - 2.months,
                                        end_period: Time.zone.today - 1.day)
              costing_consumers_all_locations(costing)
              costing.cost_per_products.where(product_id: latte.id).update_all(price_unit: 1200)
              costing.cost_per_products.where(product_id: spicy_burger.id).update_all(price_unit: 2000)
              costing.cost_per_products.where(product_id: few_sugar.id).update_all(price_unit: 300)
              SaleDetailModifier.all.each do |sale_detail_modifier|
                sale_detail_modifier.meta = sale_detail_modifier.meta.merge(
                  option_set_option_id: few_sugar_level.id,
                  option_set_id: sugar_level.id
                )
                sale_detail_modifier.option_set_id = sugar_level.id
                sale_detail_modifier.option_set_option_id = few_sugar_level.id
                sale_detail_modifier.rule_cost_included_in_parent = true
                sale_detail_modifier.save!
              end

              costing_calculate_cost_of_products_all_locations(costing)
              replicate_data_to_clickhouse!
            end

            it 'should move the cost to the child' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { SaleDetailModifier.order(:id).all.map { |modifier| modifier.meta['cost'] } }.from(["0.0", "0.0", "0.0"]).to(["300.0", "300.0", "300.0"])
              .and not_change { SaleDetailModifier.order(:id).all.map { |modifier| modifier.meta['self_cost'] } }
              .and change { SaleDetailTransaction.order(:id).all.map { |modifier| modifier.meta['cost'] } }.from(["1500.0", "6000.0", "1500.0", "6000.0", "1500.0", "6000.0"]).to(["1200.0", "6000.0", "1200.0", "6000.0", "1200.0", "6000.0"])
              .and not_change { SaleDetailTransaction.order(:id).all.map { |modifier| modifier.meta['self_cost'] } }

              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['rule_cost_included_in_parent']).to eq(false)

              expect(SaleDetailModifier.all.pluck(:meta)).to match_array(
                [{"cost"=>"300.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"5000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>"5000.0"},
               {"cost"=>"300.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"5000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>"5000.0"},
               {"cost"=>"300.0",
                "self_cost"=>"300.0",
                "option_set_id"=>1,
                "option_set_option_id"=>2,
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"5000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_amount_prorate_discount"=>"5000.0"}]
              )

              expect(SaleDetailTransaction.all.pluck(:meta)).to match_array(
                [{"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"6000.0",
                "self_cost"=>"6000.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"1200.0",
                "self_cost"=>"1200.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"1200.0",
                "self_cost"=>"1200.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"},
               {"cost"=>"1200.0",
                "self_cost"=>"1200.0",
                "sales_feed_adjustment_notes" => "-",
                "parent_rule_prorate_discount"=>"0.0",
                "parent_rule_prorate_surcharge"=>'0.0',
                "parent_rule_total_line_amount"=>"10000.0",
                "sales_by_free_of_charge"=>"0.0",
                "parent_rule_total_line_discount_prorate"=>"10000.0"}]
              )
            end
          end
        end

        context 'when has options and rule_minimum changed' do
           let(:option_set_option) { option_set.option_set_options.first }

          response 200, 'successful', document: false do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { option_set.id }
            let(:param) do
              {
                option_set: {
                  name: 'new_name',
                  rule_minimum: 2,
                  option_set_options_attributes: [
                    {
                      id: option_set_option.id,
                      pre_selected: true,
                      pre_selected_quantity: 10,
                      _destroy: true
                    },
                    {
                      option_set_custom_price_locations_attributes:  [],
                      pre_selected: false,
                      pre_selected_quantity: "0",
                      price: "0",
                      product_id: latte.id,
                      product_unit_id: latte.product_unit_id,
                      quantity: "1",
                      sequence: 1
                    }
                  ],
                }
              }
            end

            before do |example|
              owned_branch_1

              product_option_set
              product_option_set.product.update(status: 'deactivated') # prevent push notification and bullet alert
            end

            it 'returns a valid 200 response' do |example|
              expect(Restaurant::Jobs::Product::OutOfStockParentOptionJob).to receive(:new).and_call_original
              expect(SyncProductIncomingQuantity).to receive(:perform_later).with(product_option_set.product_id).once # run once event product has multiple locations

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['name']).to eq 'new_name'
              expect(option_set_response_body['rule_minimum']).to eq(2)

              new_option_set_option = OptionSetOption.last
              expect(option_set_response_body['option_set_options']).to eq([
                {
                  "id"=>new_option_set_option.id,
                  "price"=>"0.0",
                  "quantity"=>"1.0",
                  "sequence"=>1,
                  "pre_selected"=>false,
                  "pre_selected_quantity"=>'0.0',
                  "enable_max_chosen"=>false,
                  "max_chosen_quantity"=>"0.0",
                  "deleted"=>false,
                  "product"=>{
                    "id"=>new_option_set_option.product_id,
                    "name"=>new_option_set_option.product.name
                  },
                  "product_unit"=>{
                    "id"=>new_option_set_option.product.product_unit_id,
                    "name"=>new_option_set_option.product.product_unit.name
                  },
                  "custom_prices"=>[]
                }
              ])
            end
          end
        end
      end

      context 'when duplicate products but one of them is about to be destroyed' do
        response(200, 'unprocessable entity') do
          schema '$ref' => '#/components/responses/response_create_option_sets'
          let(:id) { ice_level.id }

          let(:param) do
            latte
            line = ice_level.option_set_options.first
            {
              option_set: {
                name: 'new name',
                option_set_options_attributes: [
                  {
                    id: line.id,
                    product_id: line.product_id,
                    price: 15000,
                    quantity: 3,
                    product_unit_id: line.product_unit_id,
                    _destroy: true
                  },
                  {
                    id: nil,
                    product_id: espresso.id,
                    price: 12000,
                    quantity: 2,
                    product_unit_id: espresso.product_unit_id
                  },
                ]
              }
            }
          end

          before do
            latte
            ice_level
          end

          it 'should not create option set' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { OptionSet.count }
            .and not_change { OptionSetCustomPriceLocation.count }

            response_body = JSON.parse(response.body)
            expect(response_body['option_set']['name']).to eq('new name')

            line = ice_level.reload.option_set_options.first
            expect(line.product).to eq(espresso)
          end
        end
      end

      response 403, 'forbidden' do
        let(:id) { option_set.id }
        let(:param) { { option_set: { name: 'new_name' } } }

        before do |example|
          user_permission.location_permission['product']['update'] = false
          user_permission.save!
          submit_request(example.metadata)
        end

        it 'should not be able to update option set' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body).to eq({"message"=>"You are not authorized.", "relogin"=>false})
        end
      end

      response 422, 'Unprocessable entity', document: false do
        context 'when set pre_selected' do
          let(:option_set_option) { option_set.option_set_options.first }
          let(:id) { option_set.id }
          let(:base_param) do
            {
              option_set: {
                name: 'new_name',
                rule_cost_included_in_parent: true,
                show_item: false,
                docket_printing_option: "split_item_by_category"
              }
            }
          end

          before do
            owned_branch_1
          end

          context 'when pre_selected_quantity negative' do
            let(:param) do
              base_param[:option_set].merge(option_set_options_attributes: [
                {
                  id: option_set_option.id,
                  pre_selected: true,
                  pre_selected_quantity: -10
                }
              ])
            end

            it 'returns a valid 200 response' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({
                "errors"=>{
                  "option_set_options[0].pre_selected_quantity"=>[
                    "Option set options[0] pre selected quantity must be greater than 0"
                  ]
                }
              })
            end
          end

          context 'when pre_selected_quantity zero' do
            let(:param) do
              base_param[:option_set].merge(option_set_options_attributes: [
                {
                  id: option_set_option.id,
                  pre_selected: true,
                  pre_selected_quantity: 0
                }
              ])
            end

            it 'returns a valid 200 response' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({
                "errors"=>{
                  "option_set_options[0].pre_selected_quantity"=>[
                    "Option set options[0] pre selected quantity must be greater than 0"
                  ]
                }
              })
            end
          end

          context 'when pre_selected_quantity null' do
            let(:param) do
              base_param[:option_set].merge(option_set_options_attributes: [
                {
                  id: option_set_option.id,
                  pre_selected: true,
                  pre_selected_quantity: nil
                }
              ])
            end

            it 'returns a valid 200 response' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({
                "errors"=>{
                  "option_set_options[0].pre_selected_quantity"=>[
                    "Option set options[0] pre selected quantity is not a number"
                  ]
                }
              })
            end
          end
        end
      end
    end

    delete('delete option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      response(204, 'successful') do
        let(:id) { option_set.id }
        before do |example|
          owned_branch_1
          option_set
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response' do |example|
          assert_response_matches_metadata(example.metadata)
          option_set.reload
          expect(option_set.deleted).to eq(true)
        end
      end

      response 422, 'unprocessable entity' do
        let(:id) { option_set.id }

        before do
          owned_branch_1
          option_set
          product_option_set
        end

        it 'should not be able to delete if has product' do |example|
          expect do
            submit_request(example.metadata)
          end.to change { OptionSet.count }.by(0)

          assert_response_matches_metadata(example.metadata)
        end
      end

      response 403, 'forbidden' do
        let(:id) { option_set.id }

        before do |example|
          user_permission.location_permission['product']['destroy'] = false
          user_permission.save!
          submit_request(example.metadata)
        end

        it 'should not be able to delete option set' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq(I18n.t('general.error_401'))
        end
      end
    end
  end
end
