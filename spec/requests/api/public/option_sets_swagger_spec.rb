require './spec/shared/locations'
require './spec/shared/product_option_sets'
require './spec/shared/swagger'
require './spec/shared/audits'
require './spec/shared/bulk_sale_transactions'
require './spec/shared/domains/public/brand_public_api_keys'

RSpec.describe 'api/public/option_sets', type: :request, clickhouse: true do
  include_context 'locations creations'
  include_context 'product option set creations'
  include_context 'swagger after response'
  include_context 'bulk sale transactions creations'
  include_context 'public api key integration brand'

  before(:each) do
    api_key_integration_brand
    api_key_integration_other_brand
  end

  let(:Authorization) { 'api-key-brand' }

  path '/api/public/option_sets' do
    get('list option_sets') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :keyword, in: :query, type: :string, required: false
      parameter name: :ids, in: :query, type: :string, required: false
      parameter name: :exclude_ids, in: :query, type: :string, required: false

      context 'when filter by keywords' do
        let(:keyword) { option_set.name.upcase[0..3] }
        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_list_option_sets'

          before do |example|
            owned_branch_1
            option_set
            submit_request(example.metadata)
          end
          it 'should be able to filter by keyword case insensitive' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['option_sets']

            option_sets_response_body = response_body['option_sets']
            expect(option_sets_response_body.size).to eq(1)

            option_set_response_body = option_sets_response_body.first
            expect(option_set_response_body['name']).to eq option_set.name
            expect(option_set_response_body['is_select_all_order_type']).to eq(true)
            expect(option_set_response_body['order_type_ids']).to eq([])
            expect(option_set_response_body['exclude_order_type_ids']).to eq([])
          end
        end
      end

      response 401, 'unauthorized' do

        let(:Authorization) { 'XXX' }

        it 'should not be able to get list of option set' do |example|
          submit_request(example.metadata)
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body['message']).to eq(I18n.t('general.error_401'))
        end
      end
    end

    post('create option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :param, in: :body, schema: {
        '$ref': '#/components/parameters/parameter_create_option_set'
      }

      response 201, 'successful' do
        schema '$ref' => '#/components/responses/response_create_option_sets'
        let(:option_set_param_without_product_unit) do
          custom = build(:option_set_custom_price_location_params, order_type_id: order_type.id)
          option_set_options = build(:option_set_option_params, product_id: product.id,
          option_set_custom_price_locations_attributes: [custom])
          build(:option_set_params, brand_id: brand.id, option_set_options_attributes: [option_set_options])
        end

        let(:param) { { option_set: option_set_param_without_product_unit } }
        before do |example|
          product_unit
          owned_branch_1
          submit_request(example.metadata)
        end

        it 'returns a valid 201 response' do |example|
          assert_response_matches_metadata(example.metadata)
          expect(OptionSet.count).to eq(1)
          expect(OptionSetCustomPriceLocation.count).to eq(0)

          option_set = OptionSet.take
          expect(option_set.rule_cost_included_in_parent).to eq(true)
          expect(option_set.option_set_options.first.product_unit_id).to be_present
          expect(option_set.option_set_options.first.option_set_custom_price_location_ids).to eq([])
          expect(option_set.is_select_all_order_type).to eq(true)
          expect(option_set.order_type_ids).to eq([
            order_type.id
          ])
          expect(option_set.exclude_order_type_ids).to eq([])
        end

        it_should_behave_like 'product option set response with detail'
      end
    end
  end

  path '/api/public/option_sets/{id}' do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_show_option_sets'
        let(:id) { option_set.id }

        before do |example|
          owned_branch_1
          submit_request(example.metadata)
        end

        it 'returns a valid 200 response' do |example|
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)

          option_set_response_body = response_body['option_set']
          expect(option_set_response_body['id']).to eq option_set.id
          expect(option_set_response_body['option_set_options']).not_to be_empty
          expect(option_set_response_body['rule_cost_included_in_parent']).to eq(false)
          expect(option_set_response_body['is_select_all_order_type']).to eq(true)
          expect(option_set_response_body['order_type_ids']).to eq([])
          expect(option_set_response_body['exclude_order_type_ids']).to eq([])
        end

        it_should_behave_like 'product option set response with detail'
      end
    end

    put('update option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :param, in: :body, schema: {
        '$ref': '#/components/parameters/parameter_update_option_set'
      }

      context 'when valid params' do
        context 'when no sale detail modifier' do
          response 200, 'successful' do
            schema '$ref' => '#/components/responses/response_update_option_sets'

            let(:id) { option_set.id }
            let(:param) { { option_set: { name: 'new_name', rule_cost_included_in_parent: true } } }

            before do |example|
              owned_branch_1
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)

              option_set_response_body = response_body['option_set']
              expect(option_set_response_body['name']).to eq 'new_name'
              expect(option_set_response_body['rule_cost_included_in_parent']).to eq(true)
              expect(option_set_response_body['is_select_all_order_type']).to eq(true)
              expect(option_set_response_body['order_type_ids']).to eq([])
              expect(option_set_response_body['exclude_order_type_ids']).to eq([])
            end
          end
        end
      end
    end

    delete('delete option_set') do
      tags 'Restaurant - Option Sets'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]

      response(204, 'successful') do
        let(:id) { option_set.id }
        before do |example|
          owned_branch_1
          option_set
          submit_request(example.metadata)
        end

        it 'returns a valid 204 response' do |example|
          assert_response_matches_metadata(example.metadata)
          option_set.reload
          expect(option_set.deleted).to eq(true)
        end
      end
    end
  end
end
