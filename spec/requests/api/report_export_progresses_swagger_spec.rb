require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/report_export_progresses'

RSpec.describe 'api/report_export_progresses', type: :request do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'report export progresses creations'

  before(:each) do
    travel_to Time.utc(2022, 6, 14, 11, 0)
    @header = authentication_header(owner)
  end

  after do
    travel_back
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  path '/api/report_export_progresses' do
    get 'List of report export progressed' do
      tags 'Restaurant - Report Export Progresses'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :start_date, in: :query, type: :string, required: false
      parameter name: :end_date, in: :query, type: :string, required: false
      parameter name: :request_time_start_date, in: :query, type: :string, required: false
      parameter name: :request_time_end_date, in: :query, type: :string, required: false
      parameter name: :status, in: :query, type: :string, required: false
      parameter name: :report_types, in: :query, type: :string, required: false
      parameter name: :item_per_page, in: :query, type: :string, required: false
      parameter name: :page, in: :query, type: :string, required: false

      context 'when no data' do
        response(200, 'successful') do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should return blank list of report export progresses' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"data"=>[],
              "paging"=>
               {"current_page"=>1, "total_item"=>0}}
            )
          end
        end
      end

      context 'when has data' do
        before do
          processing_incoming_order_report_export_progress
          completed_incoming_order_report_export_progress
          processing_net_sales_report_export_progress
        end

        context 'when no params' do
          response(200, 'successful') do
            before do |example|
              submit_request(example.metadata)
            end

            it 'should return correct list of report export progresses' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body).to eq(
                {"data"=>
                [{"id"=>processing_incoming_order_report_export_progress.id,
                  "report_type"=>"incoming_order",
                  "start_date"=>"2022-05-26",
                  "end_date"=>"2022-06-04",
                  "status"=>"processing",
                  "locations"=>
                   [{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"},
                    {"id"=>owned_branch_2.id, "name"=>"Owned Location Sudirman"}],
                  "is_select_all_location"=>true,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"csv",
                  "report_type_label"=>"Incoming Order",
                  "send_to_email"=>true,
                  "location_description"=>"All locations"},
                 {"id"=>completed_incoming_order_report_export_progress.id,
                  "report_type"=>"incoming_order",
                  "start_date"=>"2022-06-05",
                  "end_date"=>"2022-06-09",
                  "status"=>"completed",
                  "locations"=>
                   [{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"},
                    {"id"=>owned_branch_2.id, "name"=>"Owned Location Sudirman"}],
                  "is_select_all_location"=>false,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"csv",
                  "report_type_label"=>"Incoming Order",
                  "send_to_email"=>true,
                  "location_description"=>"Multiple locations"},
                 {"id"=>processing_net_sales_report_export_progress.id,
                  "report_type"=>"net_sales",
                  "start_date"=>"2022-06-10",
                  "end_date"=>"2022-06-13",
                  "status"=>"processing",
                  "locations"=>[{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"}],
                  "is_select_all_location"=>false,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"xlsx",
                  "report_type_label"=>"Net Sales",
                  "send_to_email"=>true,
                  "location_description"=>"Owned Location Parung"}],
               "paging"=>
                {"current_page"=>1, "total_item"=>3}}
              )
            end
          end
        end

        context 'when progress from another brand' do
          response(200, 'successful') do
            before do |example|
              processing_incoming_order_report_export_progress.update_columns(brand_id: brand_2.id)
              completed_incoming_order_report_export_progress.update_columns(brand_id: brand_2.id)
              submit_request(example.metadata)
            end

            it 'should return correct list of report export progresses' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body).to eq(
                {"paging"=>{"current_page"=>1, "total_item"=>1},
                "data"=>
                 [{"id"=>processing_net_sales_report_export_progress.id,
                   "report_type"=>"net_sales",
                   "start_date"=>"2022-06-10",
                   "end_date"=>"2022-06-13",
                   "status"=>"processing",
                   "locations"=>[{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"}],
                   "is_select_all_location"=>false,
                   "file_links"=>["www.example.com", "www.example2.com"],
                   "user_id"=>owner.id,
                   "brand_id"=>brand.id,
                   "created_at"=>"2022-06-14T11:00:00.000Z",
                   "updated_at"=>"2022-06-14T11:00:00.000Z",
                   "report_format"=>"xlsx",
                   "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                   "location_description"=>"Owned Location Parung",
                   "send_to_email"=>true,
                   "report_type_label"=>"Net Sales"}]}
              )
            end
          end
        end

        context 'when with status param' do
          response(200, 'successful') do
            let(:status) { 'processing' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should return correct list of report export progresses' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body).to eq(
                {"data"=>
                [{"id"=>processing_incoming_order_report_export_progress.id,
                  "report_type"=>"incoming_order",
                  "start_date"=>"2022-05-26",
                  "end_date"=>"2022-06-04",
                  "status"=>"processing",
                  "locations"=>
                   [{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"},
                    {"id"=>owned_branch_2.id, "name"=>"Owned Location Sudirman"}],
                  "is_select_all_location"=>true,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"csv",
                  "report_type_label"=>"Incoming Order",
                  "send_to_email"=>true,
                  "location_description"=>"All locations"},
                 {"id"=>processing_net_sales_report_export_progress.id,
                  "report_type"=>"net_sales",
                  "start_date"=>"2022-06-10",
                  "end_date"=>"2022-06-13",
                  "status"=>"processing",
                  "locations"=>[{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"}],
                  "is_select_all_location"=>false,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"xlsx",
                  "report_type_label"=>"Net Sales",
                  "send_to_email"=>true,
                  "location_description"=>"Owned Location Parung"}],
               "paging"=>
                {"current_page"=>1, "total_item"=>2}}
              )
            end
          end
        end

        context 'when with report type param' do
          let(:report_types) { 'net_sales,stock_movement' }

          response(200, 'successful') do
            before do |example|
              processing_stock_movement_report_export_progress
              submit_request(example.metadata)
            end

            it 'should return correct list of report export progresses' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body).to eq(
                {"data"=>
                [{"id"=>processing_net_sales_report_export_progress.id,
                  "report_type"=>"net_sales",
                  "start_date"=>"2022-06-10",
                  "end_date"=>"2022-06-13",
                  "status"=>"processing",
                  "locations"=>[{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"}],
                  "is_select_all_location"=>false,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"xlsx",
                  "report_type_label"=>"Net Sales",
                  "send_to_email"=>true,
                  "location_description"=>"Owned Location Parung"},
                {"id"=>processing_stock_movement_report_export_progress.id,
                  "report_type"=>"stock_movement",
                  "start_date"=>"2022-05-26",
                  "end_date"=>"2022-06-04",
                  "status"=>"processing",
                  "locations"=>
                  [{"id"=>owned_branch_1.id, "name"=>"Owned Location Parung"},
                    {"id"=>owned_branch_2.id, "name"=>"Owned Location Sudirman"}],
                  "is_select_all_location"=>true,
                  "export_requested_at"=>"2022-06-14T11:00:00.000Z",
                  "file_links"=>["www.example.com", "www.example2.com"],
                  "user_id"=>owner.id,
                  "brand_id"=>brand.id,
                  "created_at"=>"2022-06-14T11:00:00.000Z",
                  "updated_at"=>"2022-06-14T11:00:00.000Z",
                  "report_format"=>"csv",
                  "report_type_label"=>"Stock Movement",
                  "send_to_email"=>true,
                  "location_description"=>"All locations"}
                ],
               "paging"=>
                {"current_page"=>1, "total_item"=>2}}
              )
            end
          end
        end

        context 'when with start date and end date param' do
          context 'when filter 25 may 2022 - 9 jun 2022' do
            let(:start_date) { '25/5/2022' }
            let(:end_date) { '09/6/2022' }

            response(200, 'successful') do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return only 2 report progress, 26 may - 4 jun 2022, and 10 jun - 13 jun 2022' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect( response_body['data'].map { |x| x['id'] }).to match_array([
                  processing_incoming_order_report_export_progress.id,
                  completed_incoming_order_report_export_progress.id
                ])
              end
            end
          end

          context 'when filter 4 jun 2022 - 13 jun 2022' do
            let(:start_date) { '04/06/2022' }
            let(:end_date) { '13/06/2022' }

            response(200, 'successful') do
              before do |example|
                processing_incoming_order_report_export_progress.update_columns(start_date: nil)
                submit_request(example.metadata)
              end

              it 'should return only 3 report progress, nil - 4 jun 2022, and 10 jun - 13 jun 2022,
                  and 10 jun - 13 jun 2022' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect( response_body['data'].map { |x| x['id'] }).to match_array([
                  processing_incoming_order_report_export_progress.id,
                  completed_incoming_order_report_export_progress.id,
                  processing_net_sales_report_export_progress.id
                  ])
              end
            end
          end
        end

        context 'when with request time param' do
          let(:request_time_start_date) { '15/6/2022' }
          let(:request_time_end_date) { '15/6/2022' }

          response(200, 'successful') do
            before do |example|
              brand.update(timezone: 'Asia/Jakarta')

              processing_incoming_order_report_export_progress
                .update_columns(export_requested_at: processing_incoming_order_report_export_progress.export_requested_at + 10.hours)

              completed_incoming_order_report_export_progress
                .update_columns(export_requested_at: completed_incoming_order_report_export_progress.export_requested_at + 24.hours)
              submit_request(example.metadata)
            end

            it 'should return correct report progress' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect( response_body['data'].map { |export_progress| export_progress['id'] }).to match_array([
                processing_incoming_order_report_export_progress.id,
                completed_incoming_order_report_export_progress.id
              ])
            end
          end
        end
      end
    end
  end

  path '/api/report_export_progresses/report_types' do
    get 'Get report types' do
      tags 'Restaurant - Report Export Progresses'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      context 'when english language' do
        response(200, 'successful') do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should return correct data' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to match_array(
              [{"report_type"=>"incoming_order", "label"=>"Incoming Order"},
                {"report_type"=>"production_summary", "label"=>"Production Summary"},
                {"report_type"=>"outgoing_delivery", "label"=>"Outgoing Delivery"},
                {"report_type"=>"outgoing_order", "label"=>"Outgoing Order"},
                {"report_type"=>"incoming_delivery", "label"=>"Incoming Delivery"},
                {"report_type"=>"gross_profit_order", "label"=>"Gross Profit Order"},
                {"report_type"=>"gross_profit_product", "label"=>"Gross Profit per Product"},
                {"report_type"=>"order_fulfillment", "label"=>"Order Fulfillment"},
                {"report_type"=>"stock_waste", "label"=>"Stock Waste"},
                {"report_type"=>"product_stock", "label"=>"Product Stock"},
                {"report_type"=>"stock_movement", "label"=>"Stock Movement"},
                {"report_type"=>"par", "label"=>"PAR"},
                {"report_type"=>"usage_variance", "label"=>"Usage Variance"},
                {"report_type"=>"net_sales", "label"=>"Net Sales"},
                {"report_type"=>"sales_summary", "label"=>"Sales Summary"},
                {"report_type"=>"sales_feed", "label"=>"Sales Feed"},
                {"report_type"=>"sales_by_product", "label"=>"Sales By Product"},
                {"report_type"=>"sales_type", "label"=>"Sales Type"},
                {"report_type"=>"payment_method", "label"=>"By Payment Method"},
                {"report_type"=>"promotion", "label"=>"Promotion"},
                {"report_type"=>"royalty", "label"=>"Royalty"},
                {"report_type"=>"disbursement", "label"=>"Disbursement"},
                {"report_type"=>"account_transaction", "label"=>"Balance"},
                {"report_type"=>"money_movement", "label"=>"Money Movement"},
                {"report_type"=>"profit_loss", "label"=>"Profit & Loss"},
                {"report_type"=>"tax", "label"=>"Tax"},
                {"report_type"=>"hourly_sales", "label"=>"Hourly Sales"},
                {"report_type"=>"daily_sales", "label"=>"Daily Sales"},
                {"report_type"=>"money_movement_balance", "label"=>"Money Movement Balance"},
                {"report_type"=>"export_buy_price", "label"=>"Product Buy Price"},
                {"report_type"=>"export_sell_price", "label"=>"Menu Price Book"},
                {"report_type"=>"customer_database", "label"=>"Customer Database"},
                {"report_type"=>"sales_by_customer", "label"=>"Sales By Customer"},
                {"report_type"=>"customer_deposit_balance",
                  "label"=>"Customer Deposit Balance"},
                {"report_type"=>"customer_point_movement", "label"=>"Loyalty Point Movement"},
                {"report_type"=>"customer_deposit_movement",
                  "label"=>"Customer Deposit Movement"},
                {"report_type"=>"daily_customer_profile", "label"=>"Daily Customer Profile"},
                {"report_type"=>"customer", "label"=>"Customer"},
                {"report_type"=>"closing", "label"=>"Closing"},
                {"report_type"=>"customer_order", "label"=>"Customer Order"},
                {"report_type"=>"production_cost", "label"=>"Production Cost"},
                {"report_type"=>"hourly_guest_visit", "label"=>"Hourly Guest Visit"},
                {"report_type"=>"stock_waste_per_section",
                  "label"=>"Stock Waste per Section"},
                {"report_type"=>"product_stock_per_section",
                  "label"=>"Product Stock per Section"},
                {"report_type"=>"stock_movement_per_section",
                  "label"=>"Stock Movement per Section"},
                {"report_type"=>"multi_brand_usage_variance",
                  "label"=>"Multi Brand Usage Variance"},
                {"report_type"=>"sale_transactions_report", "label"=>"Sales Transaction"},
                {"report_type"=>"hourly_sales_by_product",
                  "label"=>"Hourly Sales by Product"},
                {"report_type"=>"vendor_price_history", "label"=>"Vendor Price History"},
                {"report_type"=>"sales_by_summary", "label"=>"Sales By Product Summary"},
                {"report_type"=>"pos_activity_log", "label"=>"Pos Activity Log"},
                {"report_type"=>"pay_later_payment", "label"=>"Pay Later Payment"},
                {"report_type"=>"brand_otp_credit_transaction",
                  "label"=>"Brand Credit Transaction"},
                {"report_type"=>"cash_closing", "label"=>"Cash Closing"},
                {"label"=>"Recipe List", "report_type"=>"export_recipe"},
                {"label"=>"Stock Take", "report_type"=>"stock_adjustment"},
                {"label"=>"Stock Movement Summary", "report_type"=>"stock_movement_summary"},
                {"label"=>"Stock Movement Summary per Section", "report_type"=>"stock_movement_summary_per_section"},
                {"label"=>"Vendor Product List", "report_type"=>"export_vendor_product"}
              ]
            )
          end
        end
      end

      context 'when indonesian language' do
        response(200, 'successful') do
          before do |example|
            owner.update!(active_language: :id)
            submit_request(example.metadata)
          end

          after do
            owner.update!(active_language: :en)
          end

          it 'should return correct data' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to match_array(
              [{"report_type"=>"incoming_order", "label"=>"Pesanan Masuk"},
              {"report_type"=>"outgoing_delivery", "label"=>"Pengiriman Keluar"},
              {"report_type"=>"production_summary", "label"=>"Ringkasan Produksi"},
              {"report_type"=>"outgoing_order", "label"=>"Pesanan Keluar"},
              {"report_type"=>"incoming_delivery", "label"=>"Pengiriman Masuk"},
              {"report_type"=>"gross_profit_order", "label"=>"Laba Kotor"},
              {"report_type"=>"gross_profit_product",
                "label"=>"Laba Kotor berdasarkan Produk"},
              {"report_type"=>"order_fulfillment", "label"=>"Pemenuhan Pemesanan"},
              {"report_type"=>"stock_waste", "label"=>"Stok Waste"},
              {"report_type"=>"product_stock", "label"=>"Stok Produk"},
              {"report_type"=>"stock_movement", "label"=>"Pergerakan Stok"},
              {"report_type"=>"par", "label"=>"PAR"},
              {"report_type"=>"usage_variance", "label"=>"Pemakaian"},
              {"report_type"=>"net_sales", "label"=>"Penjualan Bersih"},
              {"report_type"=>"sales_summary", "label"=>"Ringkasan Penjualan"},
              {"report_type"=>"sales_feed", "label"=>"Daftar Penjualan"},
              {"report_type"=>"sales_by_product", "label"=>"Penjualan Berdasarkan Produk"},
              {"report_type"=>"sales_type", "label"=>"Jenis Penjualan"},
              {"report_type"=>"payment_method", "label"=>"Berdasarkan Metode Pembayaran"},
              {"report_type"=>"promotion", "label"=>"Promosi"},
              {"report_type"=>"royalty", "label"=>"Royalti"},
              {"report_type"=>"disbursement", "label"=>"Pencairan Dana"},
              {"report_type"=>"account_transaction", "label"=>"Saldo Runchise"},
              {"report_type"=>"money_movement", "label"=>"Pergerakan Uang"},
              {"report_type"=>"profit_loss", "label"=>"Laporan Laba & Rugi"},
              {"report_type"=>"tax", "label"=>"Pajak"},
              {"report_type"=>"hourly_sales", "label"=>"Penjualan Berdasarkan Jam"},
              {"report_type"=>"daily_sales", "label"=>"Penjualan Harian"},
              {"report_type"=>"money_movement_balance", "label"=>"Saldo Pergerakan Uang"},
              {"report_type"=>"export_buy_price", "label"=>"Harga Beli Produk"},
              {"report_type"=>"export_sell_price", "label"=>"Daftar Harga Menu"},
              {"report_type"=>"customer_database", "label"=>"Basis Data Pelanggan"},
              {"report_type"=>"sales_by_customer", "label"=>"Penjualan Pelanggan"},
              {"report_type"=>"customer_deposit_balance", "label"=>"Saldo Pelanggan"},
              {"report_type"=>"customer_point_movement", "label"=>"Pergerakan Point Pelanggan"},
              {"report_type"=>"customer_deposit_movement",
                "label"=>"Pergerakan Saldo Pelanggan"},
              {"report_type"=>"daily_customer_profile", "label"=>"Profil Pelanggan Harian"},
              {"report_type"=>"customer", "label"=>"Pelanggan"},
              {"report_type"=>"closing", "label"=>"Penutupan"},
              {"report_type"=>"customer_order", "label"=>"Customer Order"},
              {"report_type"=>"production_cost", "label"=>"Biaya Produksi"},
              {"report_type"=>"hourly_guest_visit", "label"=>"Kunjungan Tamu per Jam"},
              {"report_type"=>"stock_waste_per_section", "label"=>"Stok Waste per Area"},
              {"report_type"=>"product_stock_per_section", "label"=>"Stok Produk per Area"},
              {"report_type"=>"stock_movement_per_section",
                "label"=>"Pergerakan Stok per Area"},
              {"report_type"=>"multi_brand_usage_variance",
                "label"=>"Pemakaian Variasi Multi Brand"},
              {"report_type"=>"sale_transactions_report", "label"=>"Transaksi Penjualan"},
              {"report_type"=>"hourly_sales_by_product",
                "label"=>"Penjualan Per Jam Berdasarkan Produk"},
              {"report_type"=>"vendor_price_history", "label"=>"Riwayat Harga Vendor"},
              {"report_type"=>"sales_by_summary", "label"=>"Ringkasan berdasarkan Produk"},
              {"report_type"=>"pos_activity_log", "label"=>"Pos Activity Log"},
              {"report_type"=>"pay_later_payment", "label"=>"Pembayaran Pay Later"},
              {"report_type"=>"brand_otp_credit_transaction",
                "label"=>"Brand Credit Transaction"},
              {"report_type"=>"cash_closing", "label"=>"Rekapan Cash"},
              {"report_type"=>"export_recipe", "label"=>"Daftar Resep"},
              {"label"=>"Hitung Stok", "report_type"=>"stock_adjustment"},
              {"label"=>"Ringkasan Pergerakan Stok", "report_type"=>"stock_movement_summary"},
              {"label"=>"Ringkasan Pergerakan Stok per Area", "report_type"=>"stock_movement_summary_per_section"},
              {"label"=>"Daftar Produk Vendor", "report_type"=>"export_vendor_product"}
              ],
            )
          end
        end
      end
    end
  end

  path '/api/report_export_progresses/{id}' do
    get 'Get of report export progressed detail' do
      tags 'Restaurant - Report Export Progresses'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :id, in: :path, type: :string

      context 'when valid data' do
        response(200, 'successful') do
          let(:id) { completed_incoming_order_report_export_progress.id }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should return correct data' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['signed_file_links'].length).to eq(2)
            expect(response_body['report_type_label']).to eq('Incoming Order')
          end
        end
      end
    end
  end

  path '/api/report_export_progresses/bulk_destroy' do
    delete 'Bulk destroy report export progresses' do
      tags 'Restaurant - Report Export Progresses'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_costing_new_bulk_destroy'
      }

      context 'when has completed export' do
        before do
          completed_incoming_order_report_export_progress
          processing_net_sales_report_export_progress.update(status: 'completed')
        end

        response(200, 'successful') do
          before do
            processing_incoming_order_report_export_progress.update(status: 'completed')
            completed_incoming_order_report_export_progress.update(status: 'completed')
            processing_net_sales_report_export_progress.update(status: 'completed')
          end

          let(:param) do
            { ids: [
              processing_incoming_order_report_export_progress.id,
              completed_incoming_order_report_export_progress.id,
              processing_net_sales_report_export_progress.id
            ]}
          end

          it 'should be able to bulk destroy report export progresses' do |example|
            expect(Restaurant::Models::ReportExportProgress.count).to eq(3)
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Restaurant::Models::ReportExportProgress, :count).to(0)
          end
        end
      end
    end
  end
end
